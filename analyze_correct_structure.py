import pandas as pd
import json
import os

def analyze_correct_structure():
    """正确分析processed文件夹的方案结构"""
    
    print("=== 分析方案结构 ===")
    
    processed_files = [f for f in os.listdir('processed') if f.endswith('.xlsx') and not f.startswith('~$')]
    print(f"发现 {len(processed_files)} 个方案文件")
    
    all_schemes = {}
    
    for file in processed_files:
        print(f"\n--- 分析方案: {file} ---")
        file_path = f'processed/{file}'
        
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)
            
            # 查找方案基本信息
            scheme_info = {}
            for i in range(min(20, len(df))):
                row = df.iloc[i]
                for j, cell in enumerate(row.values):
                    if pd.notna(cell) and isinstance(cell, str):
                        if '方案' in cell or '解决方案' in cell:
                            scheme_info['name'] = cell
                            scheme_info['code'] = file.replace('.xlsx', '')
                            break
                if 'name' in scheme_info:
                    break
            
            print(f"方案名称: {scheme_info.get('name', '未知')}")
            
            # 查找BOM数据开始位置
            bom_start_row = None
            header_row = None
            
            for i in range(len(df)):
                row_values = [str(val) for val in df.iloc[i].values if pd.notna(val)]
                
                # 查找BOM标识
                if any('BOM' in val for val in row_values):
                    bom_start_row = i
                    print(f"找到BOM开始行: {i}")
                    break
            
            if bom_start_row is not None:
                # 查找表头行（包含"级别"、"物件编号"等）
                for i in range(bom_start_row, min(bom_start_row + 5, len(df))):
                    row_values = [str(val) for val in df.iloc[i].values if pd.notna(val)]
                    if any('级别' in val for val in row_values) and any('物件编号' in val for val in row_values):
                        header_row = i
                        print(f"找到表头行: {i}")
                        break
                
                if header_row is not None:
                    # 设置正确的列名
                    headers = []
                    for val in df.iloc[header_row].values:
                        if pd.notna(val):
                            headers.append(str(val).strip())
                        else:
                            headers.append(f'Col_{len(headers)}')
                    
                    # 获取数据部分
                    data_df = df.iloc[header_row + 1:].copy()
                    data_df.columns = headers[:len(data_df.columns)]
                    
                    print(f"数据列名: {headers[:15]}")
                    
                    # 过滤有效的X08数据
                    if '物件编号' in data_df.columns and '级别' in data_df.columns:
                        valid_data = data_df[
                            (pd.notna(data_df['物件编号'])) & 
                            (data_df['物件编号'].astype(str).str.contains('X08', na=False))
                        ].copy()
                        
                        print(f"有效X08数据: {len(valid_data)} 行")
                        
                        # 分析层级结构
                        hierarchy = analyze_hierarchy(valid_data)
                        
                        scheme_info['hierarchy'] = hierarchy
                        scheme_info['total_products'] = len(valid_data)
                        
                        all_schemes[scheme_info['code']] = scheme_info
                        
                        # 显示层级统计
                        print("层级统计:")
                        for level, items in hierarchy.items():
                            print(f"  级别{level}: {len(items)} 项")
                            if level == '2':  # 显示几个级别2的示例（页签）
                                for item in items[:5]:
                                    print(f"    - {item['description']}")
                                if len(items) > 5:
                                    print(f"    ... 还有 {len(items) - 5} 项")
        
        except Exception as e:
            print(f"分析 {file} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 保存方案数据
    with open('方案结构分析.json', 'w', encoding='utf-8') as f:
        json.dump(all_schemes, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 分析完成 ===")
    print(f"成功分析 {len(all_schemes)} 个方案")
    for code, info in all_schemes.items():
        print(f"  {code}: {info.get('name', '未知')} ({info.get('total_products', 0)} 个产品)")
    
    return all_schemes

def analyze_hierarchy(data_df):
    """分析层级结构"""
    hierarchy = {"1": [], "2": [], "3": [], "4": []}
    
    # 需要的列
    required_cols = ['级别', '物件编号', '物件描述', '数量', '位置号']
    available_cols = {col: col for col in required_cols if col in data_df.columns}
    
    # 查找其他可能的列名
    for col in data_df.columns:
        if '品牌' in col or '制造商' in col:
            available_cols['品牌'] = col
        elif '型号' in col or '部件号' in col:
            available_cols['型号'] = col
        elif '单位' in col:
            available_cols['单位'] = col
        elif '备注' in col or '说明' in col:
            available_cols['备注'] = col
    
    print(f"可用列映射: {available_cols}")
    
    current_parent = {"1": None, "2": None, "3": None}
    
    for idx, row in data_df.iterrows():
        level = str(row[available_cols['级别']]) if '级别' in available_cols and pd.notna(row[available_cols['级别']]) else '4'
        code = str(row[available_cols['物件编号']]) if '物件编号' in available_cols else ''
        desc = str(row[available_cols['物件描述']]) if '物件描述' in available_cols and pd.notna(row[available_cols['物件描述']]) else ''
        
        if code and 'X08' in code:
            item = {
                'code': code,
                'description': desc,
                'level': level,
                'sequence': len(hierarchy[level]) + 1,
                'quantity': str(row[available_cols['数量']]) if '数量' in available_cols and pd.notna(row[available_cols['数量']]) else '1',
                'location': str(row[available_cols['位置号']]) if '位置号' in available_cols and pd.notna(row[available_cols['位置号']]) else '',
                'brand': str(row[available_cols['品牌']]) if '品牌' in available_cols and pd.notna(row[available_cols['品牌']]) else '',
                'model': str(row[available_cols['型号']]) if '型号' in available_cols and pd.notna(row[available_cols['型号']]) else '',
                'unit': str(row[available_cols['单位']]) if '单位' in available_cols and pd.notna(row[available_cols['单位']]) else '个',
                'required': '是',  # 默认必配
                'remark': str(row[available_cols['备注']]) if '备注' in available_cols and pd.notna(row[available_cols['备注']]) else ''
            }
            
            # 设置父级关系
            if level in ['2', '3', '4']:
                parent_level = str(int(level) - 1)
                if current_parent[parent_level]:
                    item['parent'] = current_parent[parent_level]
            
            # 更新当前父级
            if level in ['1', '2', '3']:
                current_parent[level] = code
            
            hierarchy[level].append(item)
    
    return hierarchy

def load_lifecycle_data():
    """加载生命周期数据"""
    lifecycle_data = {}
    
    try:
        df_gov = pd.read_excel('政府退市查询-方案产品表格_20250805_0949.xlsx', sheet_name='方案产品表格')
        
        for idx, row in df_gov.iterrows():
            option_code = str(row['选件编号']) if pd.notna(row['选件编号']) else ''
            product_code = str(row['产品编号']) if pd.notna(row['产品编号']) else ''
            lifecycle = str(row['生命周期']) if pd.notna(row['生命周期']) else '量产'
            
            if option_code and 'X08' in option_code:
                lifecycle_data[option_code] = lifecycle
            if product_code:
                lifecycle_data[product_code] = lifecycle
        
        print(f"加载生命周期数据: {len(lifecycle_data)} 项")
        
    except Exception as e:
        print(f"加载生命周期数据失败: {e}")
    
    return lifecycle_data

if __name__ == "__main__":
    schemes = analyze_correct_structure()
    lifecycle = load_lifecycle_data()
    
    # 合并数据
    final_data = {
        'schemes': schemes,
        'lifecycle': lifecycle,
        'metadata': {
            'generated_at': pd.Timestamp.now().isoformat(),
            'total_schemes': len(schemes)
        }
    }
    
    with open('完整方案数据.json', 'w', encoding='utf-8') as f:
        json.dump(final_data, f, ensure_ascii=False, indent=2)
    
    print("\n数据已保存到: 完整方案数据.json")
