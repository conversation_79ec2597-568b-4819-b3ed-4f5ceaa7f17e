import pandas as pd
import os

def analyze_detailed_structure():
    file_path = 'processed/M200701.00024.xlsx'
    
    # 读取数据，跳过前面的标题行
    df = pd.read_excel(file_path, sheet_name=0, skiprows=5)
    
    print("=== 数据结构分析 ===")
    print(f"总行数: {len(df)}")
    
    # 找到BOM数据开始的位置
    bom_start = None
    for i, row in df.iterrows():
        if str(row.iloc[0]).strip() == 'BOM':
            bom_start = i + 1  # BOM标题的下一行
            break
    
    if bom_start:
        print(f"BOM数据开始行: {bom_start}")
        
        # 获取BOM数据
        bom_df = df.iloc[bom_start:].copy()
        
        # 重新设置列名 - 查找正确的表头行
        header_row = None
        for i in range(max(0, bom_start-3), min(len(df), bom_start+3)):
            row_values = df.iloc[i].values
            if any('级别' in str(val) for val in row_values if pd.notna(val)):
                header_row = i
                break

        if header_row is not None:
            print(f"找到表头行: {header_row}")
            new_columns = []
            for i, col_name in enumerate(df.iloc[header_row].values):
                if pd.notna(col_name) and str(col_name).strip():
                    new_columns.append(str(col_name).strip())
                else:
                    new_columns.append(f'Col_{i}')

            # 确保列名数量匹配
            while len(new_columns) < len(bom_df.columns):
                new_columns.append(f'Col_{len(new_columns)}')

            bom_df.columns = new_columns[:len(bom_df.columns)]

            # 重新获取数据，从表头的下一行开始
            bom_df = df.iloc[header_row+1:].copy()
            bom_df.columns = new_columns[:len(bom_df.columns)]
        
        print(f"BOM数据列名: {bom_df.columns.tolist()[:15]}")
        
        # 分析层级结构
        print("\n=== 层级结构分析 ===")
        
        # 找到编码列和描述列
        code_col = None
        desc_col = None
        level_col = None
        
        for col in bom_df.columns:
            if '物件编号' in col or 'X08' in str(bom_df[col].iloc[0] if len(bom_df) > 0 else ''):
                code_col = col
            elif '物件描述' in col or '描述' in col:
                desc_col = col
            elif '级别' in col:
                level_col = col
        
        print(f"编码列: {code_col}")
        print(f"描述列: {desc_col}")
        print(f"级别列: {level_col}")
        
        if code_col and desc_col:
            # 过滤有效数据
            valid_data = bom_df[
                (pd.notna(bom_df[code_col])) & 
                (bom_df[code_col].astype(str).str.contains('X08', na=False))
            ].copy()
            
            print(f"\n有效的X08数据行数: {len(valid_data)}")
            
            # 分析层级
            print("\n=== 详细层级分析 ===")
            
            # 按级别分组
            if level_col:
                level_groups = valid_data.groupby(level_col)
                for level, group in level_groups:
                    print(f"\n级别 {level} ({len(group)} 项):")
                    for idx, row in group.head(5).iterrows():
                        code = row[code_col]
                        desc = row[desc_col] if pd.notna(row[desc_col]) else "无描述"
                        print(f"  {code} - {desc}")
                    if len(group) > 5:
                        print(f"  ... 还有 {len(group) - 5} 项")
            
            # 分析高配/低配
            print(f"\n=== 高配/低配分析 ===")
            high_config = valid_data[valid_data[code_col].astype(str).str.contains('01002', na=False)]
            low_config = valid_data[valid_data[code_col].astype(str).str.contains('01003', na=False)]
            
            print(f"高配项目 (01002): {len(high_config)} 项")
            if len(high_config) > 0:
                for idx, row in high_config.head(3).iterrows():
                    print(f"  {row[code_col]} - {row[desc_col] if pd.notna(row[desc_col]) else '无描述'}")
            
            print(f"低配项目 (01003): {len(low_config)} 项")
            if len(low_config) > 0:
                for idx, row in low_config.head(3).iterrows():
                    print(f"  {row[code_col]} - {row[desc_col] if pd.notna(row[desc_col]) else '无描述'}")
            
            # 分析页签分类（第二级）
            print(f"\n=== 页签分类分析 ===")
            # 排除高配/低配的主分类，找到子分类
            sub_categories = valid_data[
                ~valid_data[code_col].astype(str).str.contains('01002|01003', na=False)
            ]
            
            # 按编码模式分组
            category_patterns = {}
            for idx, row in sub_categories.iterrows():
                code = str(row[code_col])
                if 'X08' in code:
                    # 提取分类模式 X08.01.00xxx 或 X08.02.00xxx
                    parts = code.split('.')
                    if len(parts) >= 3:
                        pattern = f"{parts[0]}.{parts[1]}"
                        if pattern not in category_patterns:
                            category_patterns[pattern] = []
                        category_patterns[pattern].append({
                            'code': code,
                            'desc': row[desc_col] if pd.notna(row[desc_col]) else "无描述",
                            'level': row[level_col] if level_col and pd.notna(row[level_col]) else "未知"
                        })
            
            print(f"发现 {len(category_patterns)} 个主要分类模式:")
            for pattern, items in category_patterns.items():
                print(f"\n{pattern} 分类 ({len(items)} 项):")
                # 按级别排序显示
                sorted_items = sorted(items, key=lambda x: int(x['level']) if str(x['level']).isdigit() else 999)
                for item in sorted_items[:8]:
                    print(f"  级别{item['level']}: {item['code']} - {item['desc']}")
                if len(items) > 8:
                    print(f"  ... 还有 {len(items) - 8} 项")

if __name__ == "__main__":
    analyze_detailed_structure()
