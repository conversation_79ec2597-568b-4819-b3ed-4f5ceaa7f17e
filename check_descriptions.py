import pandas as pd

def check_descriptions():
    file_path = 'processed/M200701.00024.xlsx'
    
    # 读取数据
    df = pd.read_excel(file_path, sheet_name=0, skiprows=5)
    
    # 找到BOM数据开始的位置
    bom_start = None
    for i, row in df.iterrows():
        if str(row.iloc[0]).strip() == 'BOM':
            bom_start = i + 1
            break
    
    # 找到表头行
    header_row = None
    for i in range(max(0, bom_start-3), min(len(df), bom_start+3)):
        row_values = df.iloc[i].values
        if any('级别' in str(val) for val in row_values if pd.notna(val)):
            header_row = i
            break
    
    if header_row is not None:
        # 获取BOM数据
        bom_df = df.iloc[header_row+1:].copy()
        
        # 设置列名
        new_columns = []
        for i, col_name in enumerate(df.iloc[header_row].values):
            if pd.notna(col_name) and str(col_name).strip():
                new_columns.append(str(col_name).strip())
            else:
                new_columns.append(f'Col_{i}')
        
        while len(new_columns) < len(bom_df.columns):
            new_columns.append(f'Col_{len(new_columns)}')
        
        bom_df.columns = new_columns[:len(bom_df.columns)]
        
        print("=== 列名检查 ===")
        print("所有列名:")
        for i, col in enumerate(bom_df.columns):
            print(f"{i}: {col}")
        
        # 查找描述相关的列
        desc_columns = []
        for col in bom_df.columns:
            if '描述' in col or '名称' in col:
                desc_columns.append(col)
        
        print(f"\n描述相关的列: {desc_columns}")
        
        # 过滤有效的X08数据
        valid_data = bom_df[
            (pd.notna(bom_df['物件编号'])) & 
            (bom_df['物件编号'].astype(str).str.contains('X08', na=False))
        ].copy()
        
        print(f"\n=== X08数据描述检查 ===")
        print(f"有效数据行数: {len(valid_data)}")
        
        # 检查每个描述列的内容
        for desc_col in desc_columns:
            print(f"\n--- {desc_col} 列内容 ---")
            non_empty = valid_data[pd.notna(valid_data[desc_col]) & (valid_data[desc_col] != '')]
            print(f"非空行数: {len(non_empty)}")
            
            if len(non_empty) > 0:
                print("前10个非空描述:")
                for idx, row in non_empty.head(10).iterrows():
                    code = row['物件编号']
                    desc = row[desc_col]
                    level = row['级别'] if '级别' in row else 'N/A'
                    print(f"  级别{level}: {code} - {desc}")
        
        # 检查物件描述列
        if '物件描述' in bom_df.columns:
            print(f"\n=== 物件描述详细分析 ===")
            desc_data = valid_data[pd.notna(valid_data['物件描述']) & (valid_data['物件描述'] != '')]
            print(f"有描述的数据: {len(desc_data)} 行")
            
            # 按级别分组显示
            if len(desc_data) > 0:
                level_groups = desc_data.groupby('级别')
                for level, group in level_groups:
                    print(f"\n级别 {level} 的描述 ({len(group)} 项):")
                    for idx, row in group.head(8).iterrows():
                        code = row['物件编号']
                        desc = row['物件描述']
                        print(f"  {code} - {desc}")
                    if len(group) > 8:
                        print(f"  ... 还有 {len(group) - 8} 项")

if __name__ == "__main__":
    check_descriptions()
