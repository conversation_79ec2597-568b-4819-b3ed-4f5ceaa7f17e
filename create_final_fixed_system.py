import json
import pandas as pd

def create_final_fixed_system():
    """创建修复后的最终系统"""
    
    print("=== 创建修复后的系统 ===")
    
    # 1. 读取现有的方案数据
    with open('方案数据结构.json', 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
    
    # 2. 读取生命周期数据
    lifecycle_data = {}
    try:
        df_gov = pd.read_excel('政府退市查询-方案产品表格_20250805_0949.xlsx', sheet_name='方案产品表格')
        for idx, row in df_gov.iterrows():
            option_code = str(row['选件编号']) if pd.notna(row['选件编号']) else ''
            product_code = str(row['产品编号']) if pd.notna(row['产品编号']) else ''
            lifecycle = str(row['生命周期']) if pd.notna(row['生命周期']) else '量产'
            
            if option_code and 'X08' in option_code:
                lifecycle_data[option_code] = lifecycle
            if product_code:
                lifecycle_data[product_code] = lifecycle
        
        print(f"加载生命周期数据: {len(lifecycle_data)} 项")
    except Exception as e:
        print(f"加载生命周期数据失败: {e}")
    
    # 3. 转换和修复数据结构
    schemes_data = {}
    scheme_names = {
        'M200701.00024.xlsx': '司法SDT(司法行政)【主推】智慧监狱安防智能指挥解决方案',
        'M200701.00025.xlsx': '司法SDT（海关）综合保税区智能监管解决方案',
        'M200701.00027.xlsx': '司法SDT(司法行政)【主推】社矫远程视频督察管控解决方案',
        'M200701.00033.xlsx': '司法SDT(司法行政)【主推】监狱AB门智能化管控解决方案',
        'M200701.00037.xlsx': '司法SDT(司法行政)监狱指挥协调管控解决方案'
    }
    
    for file_key, hierarchy in raw_data.items():
        if file_key in scheme_names:
            scheme_code = file_key.replace('.xlsx', '')
            
            # 修复层级数据，建立父子关系
            fixed_hierarchy = fix_hierarchy_relationships(hierarchy)
            
            schemes_data[scheme_code] = {
                'code': scheme_code,
                'name': scheme_names[file_key],
                'hierarchy': fixed_hierarchy
            }
            
            print(f"\n方案: {scheme_names[file_key]}")
            for level, items in fixed_hierarchy.items():
                print(f"  级别{level}: {len(items)} 项")
                if level == '4' and len(items) > 0:
                    # 显示第四级设备的父子关系
                    parent_count = {}
                    for item in items:
                        parent = item.get('parent', '无父级')
                        parent_count[parent] = parent_count.get(parent, 0) + 1
                    print(f"    父级分布: {dict(list(parent_count.items())[:3])}")
    
    # 4. 创建HTML文件
    html_content = create_complete_html(schemes_data, lifecycle_data)
    
    with open('方案清单管理系统_完整版.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"\n=== 系统创建完成 ===")
    print(f"成功创建 {len(schemes_data)} 个方案的管理系统")
    print("文件: 方案清单管理系统_完整版.html")
    
    return schemes_data, lifecycle_data

def fix_hierarchy_relationships(hierarchy):
    """修复层级关系，建立正确的父子关系"""
    
    fixed_hierarchy = {"1": [], "2": [], "3": [], "4": []}
    
    # 复制并转换数据格式
    for level, items in hierarchy.items():
        for i, item in enumerate(items):
            fixed_item = {
                'sequence': i + 1,
                'code': item['code'],
                'description': item['description'],
                'level': item['level'],
                'quantity': '1',
                'unit': '个',
                'brand': '大华',
                'model': item['code'],
                'required': '是',
                'location': item['description'],
                'remark': ''
            }
            fixed_hierarchy[level].append(fixed_item)
    
    # 建立父子关系（基于数据的逻辑顺序）
    # 级别2的父级是级别1
    level1_items = fixed_hierarchy["1"]
    level2_items = fixed_hierarchy["2"]
    level3_items = fixed_hierarchy["3"]
    level4_items = fixed_hierarchy["4"]
    
    # 为级别2设置父级（通常是高配或低配）
    if len(level1_items) > 0:
        high_config = None
        for item in level1_items:
            if '高配' in item['description']:
                high_config = item['code']
                break
        
        if high_config:
            for item in level2_items:
                item['parent'] = high_config
    
    # 为级别3设置父级（基于级别2）
    # 简单的分配策略：按顺序分配给级别2的项目
    if len(level2_items) > 0 and len(level3_items) > 0:
        items_per_level2 = max(1, len(level3_items) // len(level2_items))
        
        for i, level3_item in enumerate(level3_items):
            parent_index = min(i // items_per_level2, len(level2_items) - 1)
            level3_item['parent'] = level2_items[parent_index]['code']
    
    # 为级别4设置父级（基于级别3）
    if len(level3_items) > 0 and len(level4_items) > 0:
        items_per_level3 = max(1, len(level4_items) // len(level3_items))
        
        for i, level4_item in enumerate(level4_items):
            parent_index = min(i // items_per_level3, len(level3_items) - 1)
            level4_item['parent'] = level3_items[parent_index]['code']
    
    return fixed_hierarchy

def create_complete_html(schemes_data, lifecycle_data):
    """创建完整的HTML内容"""
    
    return f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案清单管理系统</title>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .tab-content {{ display: none; }}
        .tab-content.active {{ display: block; }}
        .lifecycle-badge {{ @apply px-2 py-1 rounded-full text-xs font-medium; }}
        .lifecycle-量产 {{ @apply bg-green-100 text-green-800; }}
        .lifecycle-工程样机 {{ @apply bg-yellow-100 text-yellow-800; }}
        .lifecycle-停产 {{ @apply bg-red-100 text-red-800; }}
        .lifecycle-退市 {{ @apply bg-gray-100 text-gray-800; }}
        .lifecycle-开发 {{ @apply bg-blue-100 text-blue-800; }}
        .table-container {{ max-height: 600px; overflow-y: auto; }}
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 头部 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-list-alt mr-2"></i>方案清单管理系统
            </h1>
            
            <!-- 四级选择说明 -->
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>选择流程：</strong>
                            第一级：选择方案 → 第二级：选择高配/低配 → 第三级：选择模块页签 → 第四级：查看具体设备清单
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 三级选择 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">第一级-选择方案:</label>
                    <select id="schemeSelect" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                        <option value="">请选择方案...</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">第二级-配置类型:</label>
                    <select id="configType" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1" disabled>
                        <option value="">请先选择方案</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">筛选生命周期:</label>
                    <select id="lifecycleFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                        <option value="">全部</option>
                        <option value="量产">量产</option>
                        <option value="工程样机">工程样机</option>
                        <option value="停产">停产</option>
                        <option value="退市">退市</option>
                    </select>
                </div>
            </div>
            
            <!-- 搜索和更新 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">搜索设备:</label>
                    <input type="text" id="searchInput" placeholder="搜索设备名称或编号..." 
                           class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                </div>
                
                <div class="flex items-center space-x-2">
                    <label for="fileUpload" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md cursor-pointer text-sm">
                        <i class="fas fa-upload mr-1"></i>更新生命周期数据
                    </label>
                    <input type="file" id="fileUpload" accept=".xlsx,.xls" class="hidden">
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="grid grid-cols-4 gap-4 text-center">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                    <div class="text-sm text-gray-600">总设备数</div>
                </div>
                <div class="bg-green-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="activeCount">0</div>
                    <div class="text-sm text-gray-600">量产设备</div>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600" id="sampleCount">0</div>
                    <div class="text-sm text-gray-600">工程样机</div>
                </div>
                <div class="bg-red-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-red-600" id="retiredCount">0</div>
                    <div class="text-sm text-gray-600">停产/退市</div>
                </div>
            </div>
        </div>

        <!-- 第三级：页签导航 -->
        <div class="bg-white rounded-lg shadow-md mb-6" id="tabContainer" style="display: none;">
            <div class="border-b border-gray-200">
                <div class="px-6 py-3 bg-gray-50">
                    <h3 class="text-lg font-medium text-gray-800">第三级 - 模块页签选择</h3>
                </div>
                <nav class="flex space-x-2 px-6 overflow-x-auto" id="tabNavigation">
                    <!-- 动态生成页签 -->
                </nav>
            </div>
        </div>

        <!-- 第四级：设备清单 -->
        <div class="bg-white rounded-lg shadow-md p-6" id="productContainer" style="display: none;">
            <div class="mb-4">
                <h3 class="text-lg font-medium text-gray-800">第四级 - 具体设备清单</h3>
                <p class="text-sm text-gray-600">显示所选模块下的具体设备信息</p>
            </div>
            <div class="table-container">
                <div id="productTable">
                    <!-- 动态生成设备表格 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let schemesData = {json.dumps(schemes_data, ensure_ascii=False, indent=8)};
        let lifecycleData = {json.dumps(lifecycle_data, ensure_ascii=False, indent=8)};
        let currentScheme = null;
        let currentConfig = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {{
            initializeSchemeSelect();
            setupEventListeners();
        }});

        function initializeSchemeSelect() {{
            const select = document.getElementById('schemeSelect');
            select.innerHTML = '<option value="">请选择方案...</option>';
            
            for (const [code, scheme] of Object.entries(schemesData)) {{
                const option = document.createElement('option');
                option.value = code;
                option.textContent = scheme.name;
                select.appendChild(option);
            }}
        }}

        function setupEventListeners() {{
            document.getElementById('schemeSelect').addEventListener('change', onSchemeChange);
            document.getElementById('configType').addEventListener('change', onConfigChange);
            document.getElementById('lifecycleFilter').addEventListener('change', filterProducts);
            document.getElementById('searchInput').addEventListener('input', function() {{
                searchProducts(this.value);
            }});
            document.getElementById('fileUpload').addEventListener('change', handleFileUpload);
        }}

        function onSchemeChange() {{
            const schemeCode = document.getElementById('schemeSelect').value;
            if (!schemeCode) {{
                document.getElementById('configType').disabled = true;
                document.getElementById('configType').innerHTML = '<option value="">请先选择方案</option>';
                hideTabsAndProducts();
                return;
            }}

            currentScheme = schemesData[schemeCode];
            
            // 更新配置选择
            const configSelect = document.getElementById('configType');
            configSelect.disabled = false;
            configSelect.innerHTML = '<option value="">请选择配置...</option>';
            
            // 查找高配/低配选项
            const level1Items = currentScheme.hierarchy["1"] || [];
            level1Items.forEach(item => {{
                const option = document.createElement('option');
                option.value = item.code;
                option.textContent = item.description;
                configSelect.appendChild(option);
            }});
            
            hideTabsAndProducts();
        }}

        function onConfigChange() {{
            const configCode = document.getElementById('configType').value;
            if (!configCode || !currentScheme) {{
                hideTabsAndProducts();
                return;
            }}

            currentConfig = configCode;
            renderTabs();
            showTabsAndProducts();
            updateStatistics();
        }}

        function renderTabs() {{
            const tabNav = document.getElementById('tabNavigation');
            tabNav.innerHTML = '';
            
            const level2Items = currentScheme.hierarchy["2"] || [];
            
            level2Items.forEach((item, index) => {{
                const tabButton = document.createElement('button');
                tabButton.className = `py-3 px-3 border-b-2 font-medium text-xs whitespace-nowrap ${{index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}}`;
                tabButton.textContent = item.description;
                tabButton.onclick = () => switchTab(item.code, tabButton);
                tabNav.appendChild(tabButton);
            }});
            
            // 默认显示第一个页签
            if (level2Items.length > 0) {{
                renderProductTable(level2Items[0].code);
            }}
        }}

        function switchTab(moduleCode, button) {{
            // 更新按钮状态
            document.querySelectorAll('#tabNavigation button').forEach(btn => {{
                btn.className = btn.className.replace('border-blue-500 text-blue-600', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
            }});
            button.className = button.className.replace('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'border-blue-500 text-blue-600');
            
            renderProductTable(moduleCode);
        }}

        function renderProductTable(moduleCode) {{
            // 第三级：页签对应的子场景
            const level3Items = (currentScheme.hierarchy["3"] || []).filter(item => item.parent === moduleCode);
            // 第四级：具体设备清单
            const level4Items = currentScheme.hierarchy["4"] || [];
            
            let html = `
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0">
                        <tr>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">序号</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">子场景</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">产品简称</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">物料代码</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">型号</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">品牌</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">数量</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">单位</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">是否必配</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">安装位置及用途</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">生命周期</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">备注说明</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
            `;
            
            let sequence = 1;
            
            // 遍历第三级（子场景）
            level3Items.forEach(level3Item => {{
                // 查找属于这个子场景的第四级设备
                const childDevices = level4Items.filter(item => item.parent === level3Item.code);
                
                if (childDevices.length > 0) {{
                    // 显示第四级设备清单
                    childDevices.forEach(device => {{
                        const lifecycle = getProductLifecycle(device.code);
                        const lifecycleClass = `lifecycle-${{lifecycle}}`;
                        
                        html += `
                            <tr class="product-row hover:bg-gray-50" data-lifecycle="${{lifecycle}}">
                                <td class="px-3 py-2 text-xs text-gray-900">${{sequence}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.description}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.description}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.code}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.model}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.brand}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.quantity}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.unit}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.required}}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.location}}</td>
                                <td class="px-3 py-2">
                                    <span class="lifecycle-badge ${{lifecycleClass}}">${{lifecycle}}</span>
                                </td>
                                <td class="px-3 py-2 text-xs text-gray-900">${{device.remark}}</td>
                            </tr>
                        `;
                        sequence++;
                    }});
                }} else {{
                    // 如果第三级没有对应的第四级设备，显示第三级本身作为设备
                    const lifecycle = getProductLifecycle(level3Item.code);
                    const lifecycleClass = `lifecycle-${{lifecycle}}`;
                    
                    html += `
                        <tr class="product-row hover:bg-gray-50" data-lifecycle="${{lifecycle}}">
                            <td class="px-3 py-2 text-xs text-gray-900">${{sequence}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.description}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.description}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.code}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.model}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.brand}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.quantity}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.unit}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.required}}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.location}}</td>
                            <td class="px-3 py-2">
                                <span class="lifecycle-badge ${{lifecycleClass}}">${{lifecycle}}</span>
                            </td>
                            <td class="px-3 py-2 text-xs text-gray-900">${{level3Item.remark}}</td>
                        </tr>
                    `;
                    sequence++;
                }}
            }});
            
            // 如果没有找到任何数据，显示提示信息
            if (sequence === 1) {{
                html += `
                    <tr>
                        <td colspan="12" class="px-3 py-8 text-center text-gray-500">
                            <i class="fas fa-inbox text-2xl mb-2"></i>
                            <div>该模块暂无设备清单数据</div>
                            <div class="text-xs mt-1">请检查数据或选择其他模块</div>
                        </td>
                    </tr>
                `;
            }}
            
            html += `
                    </tbody>
                </table>
            `;
            
            document.getElementById('productTable').innerHTML = html;
        }}

        function getProductLifecycle(productCode) {{
            return lifecycleData[productCode] || '量产';
        }}

        function updateStatistics() {{
            if (!currentScheme) return;
            
            const allProducts = [
                ...(currentScheme.hierarchy["3"] || []),
                ...(currentScheme.hierarchy["4"] || [])
            ];
            
            const totalCount = allProducts.length;
            const activeCount = allProducts.filter(p => getProductLifecycle(p.code) === '量产').length;
            const sampleCount = allProducts.filter(p => getProductLifecycle(p.code) === '工程样机').length;
            const retiredCount = allProducts.filter(p => ['停产', '退市'].includes(getProductLifecycle(p.code))).length;
            
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('activeCount').textContent = activeCount;
            document.getElementById('sampleCount').textContent = sampleCount;
            document.getElementById('retiredCount').textContent = retiredCount;
        }}

        function filterProducts() {{
            const lifecycle = document.getElementById('lifecycleFilter').value;
            const rows = document.querySelectorAll('.product-row');
            
            rows.forEach(row => {{
                if (!lifecycle || row.dataset.lifecycle === lifecycle) {{
                    row.style.display = '';
                }} else {{
                    row.style.display = 'none';
                }}
            }});
        }}

        function searchProducts(query) {{
            const rows = document.querySelectorAll('.product-row');
            
            rows.forEach(row => {{
                const text = row.textContent.toLowerCase();
                if (!query || text.includes(query.toLowerCase())) {{
                    row.style.display = '';
                }} else {{
                    row.style.display = 'none';
                }}
            }});
        }}

        function handleFileUpload(event) {{
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {{
                try {{
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {{type: 'array'}});
                    const sheetName = workbook.SheetNames.find(name => name.includes('方案产品表格')) || workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);
                    
                    // 更新生命周期数据
                    jsonData.forEach(row => {{
                        const optionCode = row['选件编号'];
                        const productCode = row['产品编号'];
                        const lifecycle = row['生命周期'];
                        
                        if (optionCode && lifecycle) {{
                            lifecycleData[optionCode] = lifecycle;
                        }}
                        if (productCode && lifecycle) {{
                            lifecycleData[productCode] = lifecycle;
                        }}
                    }});
                    
                    // 重新渲染当前页签
                    if (currentScheme && currentConfig) {{
                        const activeTab = document.querySelector('#tabNavigation button.border-blue-500');
                        if (activeTab) {{
                            const moduleCode = Array.from(document.querySelectorAll('#tabNavigation button')).indexOf(activeTab);
                            const level2Items = currentScheme.hierarchy["2"] || [];
                            if (level2Items[moduleCode]) {{
                                renderProductTable(level2Items[moduleCode].code);
                            }}
                        }}
                        updateStatistics();
                    }}
                    
                    alert('生命周期数据更新成功！');
                }} catch (error) {{
                    console.error('文件读取错误:', error);
                    alert('文件读取失败，请检查文件格式！');
                }}
            }};
            reader.readAsArrayBuffer(file);
        }}

        function hideTabsAndProducts() {{
            document.getElementById('tabContainer').style.display = 'none';
            document.getElementById('productContainer').style.display = 'none';
        }}

        function showTabsAndProducts() {{
            document.getElementById('tabContainer').style.display = 'block';
            document.getElementById('productContainer').style.display = 'block';
        }}
    </script>
</body>
</html>'''

if __name__ == "__main__":
    create_final_fixed_system()
