import pandas as pd
import json

def create_simple_system():
    """创建简化的数据系统"""
    
    print("=== 创建简化数据系统 ===")
    
    # 1. 读取生命周期数据
    lifecycle_data = {}
    hierarchy_data = {
        "1": [],
        "2": [],
        "3": [],
        "4": []
    }
    
    try:
        df_gov = pd.read_excel('政府退市查询-方案产品表格_20250805_0949.xlsx', sheet_name='方案产品表格')
        print(f"读取到 {len(df_gov)} 行生命周期数据")
        
        # 分析数据结构
        unique_models = df_gov['模型编号'].unique()
        print(f"发现 {len(unique_models)} 个模型")
        
        # 按选件编号分组，构建层级结构
        option_groups = df_gov.groupby('选件编号')
        
        for option_code, group in option_groups:
            if pd.notna(option_code) and 'X08' in str(option_code):
                option_desc = group['选件描述'].iloc[0] if pd.notna(group['选件描述'].iloc[0]) else ''
                
                # 根据编码模式判断级别
                code_parts = str(option_code).split('.')
                if len(code_parts) >= 3:
                    third_part = code_parts[2]
                    
                    # 判断级别
                    if third_part.endswith('002') or third_part.endswith('003'):
                        level = "1"  # 高配/低配
                    elif len(third_part) == 5 and third_part.startswith('00'):
                        level = "2"  # 主要模块
                    elif len(third_part) == 5 and third_part.startswith('01'):
                        level = "2"  # 主要模块
                    else:
                        level = "3"  # 子模块
                    
                    # 添加到层级数据
                    item = {
                        'code': str(option_code),
                        'description': option_desc,
                        'level': level
                    }
                    
                    # 避免重复
                    if not any(existing['code'] == item['code'] for existing in hierarchy_data[level]):
                        hierarchy_data[level].append(item)
                
                # 添加生命周期数据
                for _, row in group.iterrows():
                    product_code = str(row['产品编号']) if pd.notna(row['产品编号']) else ''
                    product_desc = str(row['产品描述']) if pd.notna(row['产品描述']) else ''
                    lifecycle = str(row['生命周期']) if pd.notna(row['生命周期']) else '量产'
                    
                    if product_code:
                        lifecycle_data[product_code] = lifecycle
                        
                        # 将产品作为级别4添加
                        product_item = {
                            'code': product_code,
                            'description': product_desc,
                            'level': "4",
                            'parent': str(option_code)
                        }
                        
                        if not any(existing['code'] == product_item['code'] for existing in hierarchy_data["4"]):
                            hierarchy_data["4"].append(product_item)
        
        print(f"层级数据统计:")
        for level, items in hierarchy_data.items():
            print(f"  级别{level}: {len(items)} 项")
        
        print(f"生命周期数据: {len(lifecycle_data)} 项")
        
        # 2. 创建HTML文件
        create_html_file(hierarchy_data, lifecycle_data)
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

def create_html_file(hierarchy_data, lifecycle_data):
    """创建完整的HTML文件"""
    
    html_template = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案清单可视化系统</title>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .lifecycle-badge { @apply px-2 py-1 rounded-full text-xs font-medium; }
        .lifecycle-量产 { @apply bg-green-100 text-green-800; }
        .lifecycle-工程样机 { @apply bg-yellow-100 text-yellow-800; }
        .lifecycle-停产 { @apply bg-red-100 text-red-800; }
        .lifecycle-退市 { @apply bg-gray-100 text-gray-800; }
        .lifecycle-开发 { @apply bg-blue-100 text-blue-800; }
        .search-highlight { background-color: yellow; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 头部 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-list-alt mr-2"></i>方案清单可视化系统
            </h1>
            
            <!-- 控制面板 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">配置类型:</label>
                    <select id="configType" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="high">高配</option>
                        <option value="low">低配</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">生命周期:</label>
                    <select id="lifecycleFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="">全部</option>
                        <option value="量产">量产</option>
                        <option value="工程样机">工程样机</option>
                        <option value="停产">停产</option>
                        <option value="退市">退市</option>
                        <option value="开发">开发</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">搜索:</label>
                    <input type="text" id="searchInput" placeholder="搜索产品..." 
                           class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                </div>
                
                <div class="flex items-center space-x-2">
                    <label for="fileUpload" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md cursor-pointer text-sm">
                        <i class="fas fa-upload mr-1"></i>更新数据
                    </label>
                    <input type="file" id="fileUpload" accept=".xlsx,.xls" class="hidden">
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                    <div class="text-sm text-gray-600">总产品数</div>
                </div>
                <div class="bg-green-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="activeCount">0</div>
                    <div class="text-sm text-gray-600">量产产品</div>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600" id="sampleCount">0</div>
                    <div class="text-sm text-gray-600">工程样机</div>
                </div>
                <div class="bg-red-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-red-600" id="retiredCount">0</div>
                    <div class="text-sm text-gray-600">停产/退市</div>
                </div>
                <div class="bg-purple-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600" id="replacedCount">0</div>
                    <div class="text-sm text-gray-600">今日替换</div>
                </div>
            </div>
        </div>

        <!-- 页签导航 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6 overflow-x-auto" id="tabNavigation">
                    <!-- 动态生成页签 -->
                </nav>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div id="tabContent">
                <!-- 动态生成内容 -->
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let hierarchyData = ''' + json.dumps(hierarchy_data, ensure_ascii=False) + ''';
        let productLifecycleData = ''' + json.dumps(lifecycle_data, ensure_ascii=False) + ''';
        let replacementRecords = JSON.parse(localStorage.getItem('replacementRecords') || '[]');
        let currentConfig = 'high';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            renderTabs();
            updateStatistics();
        });

        function setupEventListeners() {
            document.getElementById('configType').addEventListener('change', function() {
                currentConfig = this.value;
                renderTabs();
                updateStatistics();
            });

            document.getElementById('lifecycleFilter').addEventListener('change', filterProducts);
            document.getElementById('searchInput').addEventListener('input', function() {
                searchProducts(this.value);
            });
        }

        function renderTabs() {
            const tabNav = document.getElementById('tabNavigation');
            const tabContent = document.getElementById('tabContent');
            
            const level2Items = hierarchyData["2"] || [];
            
            tabNav.innerHTML = '';
            tabContent.innerHTML = '';
            
            level2Items.forEach((item, index) => {
                const tabButton = document.createElement('button');
                tabButton.className = `py-4 px-4 border-b-2 font-medium text-sm whitespace-nowrap ${index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`;
                tabButton.textContent = item.description;
                tabButton.onclick = () => switchTab(item.code, tabButton);
                tabNav.appendChild(tabButton);
                
                const tabPane = document.createElement('div');
                tabPane.id = `tab-${item.code}`;
                tabPane.className = `tab-content ${index === 0 ? 'active' : ''}`;
                tabPane.innerHTML = generateTabContent(item.code);
                tabContent.appendChild(tabPane);
            });
        }

        function generateTabContent(parentCode) {
            const products = (hierarchyData["4"] || []).filter(item => item.parent === parentCode);
            
            if (products.length === 0) {
                return '<div class="text-center text-gray-500 py-8">暂无产品数据</div>';
            }
            
            let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';
            
            products.forEach(product => {
                const lifecycle = getProductLifecycle(product.code);
                const lifecycleClass = `lifecycle-${lifecycle}`;
                
                html += `
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow product-card" data-lifecycle="${lifecycle}">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-medium text-gray-800 text-sm">${product.description}</h4>
                            <span class="lifecycle-badge ${lifecycleClass}">${lifecycle}</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-3">${product.code}</p>
                        <div class="flex justify-between items-center">
                            <button onclick="viewProductDetails('${product.code}')" 
                                    class="text-blue-600 hover:text-blue-800 text-xs">
                                <i class="fas fa-eye mr-1"></i>详情
                            </button>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        function switchTab(code, button) {
            document.querySelectorAll('#tabNavigation button').forEach(btn => {
                btn.className = btn.className.replace('border-blue-500 text-blue-600', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
            });
            button.className = button.className.replace('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'border-blue-500 text-blue-600');
            
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`tab-${code}`).classList.add('active');
        }

        function getProductLifecycle(productCode) {
            return productLifecycleData[productCode] || '量产';
        }

        function updateStatistics() {
            const allProducts = hierarchyData["4"] || [];
            const totalCount = allProducts.length;
            const activeCount = allProducts.filter(p => getProductLifecycle(p.code) === '量产').length;
            const sampleCount = allProducts.filter(p => getProductLifecycle(p.code) === '工程样机').length;
            const retiredCount = allProducts.filter(p => ['停产', '退市'].includes(getProductLifecycle(p.code))).length;
            
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('activeCount').textContent = activeCount;
            document.getElementById('sampleCount').textContent = sampleCount;
            document.getElementById('retiredCount').textContent = retiredCount;
            document.getElementById('replacedCount').textContent = 0;
        }

        function filterProducts() {
            const lifecycle = document.getElementById('lifecycleFilter').value;
            const productCards = document.querySelectorAll('.product-card');
            
            productCards.forEach(card => {
                if (!lifecycle || card.dataset.lifecycle === lifecycle) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function searchProducts(query) {
            const productCards = document.querySelectorAll('.product-card');
            
            productCards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (!query || text.includes(query.toLowerCase())) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function viewProductDetails(productCode) {
            const product = (hierarchyData["4"] || []).find(p => p.code === productCode);
            const lifecycle = getProductLifecycle(productCode);
            
            alert(`产品详情：\\n编号：${productCode}\\n描述：${product ? product.description : '未知'}\\n生命周期：${lifecycle}`);
        }
    </script>
</body>
</html>'''
    
    with open('方案清单系统_完整版.html', 'w', encoding='utf-8') as f:
        f.write(html_template)
    
    print("HTML文件已创建: 方案清单系统_完整版.html")

if __name__ == "__main__":
    create_simple_system()
