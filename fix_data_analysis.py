import pandas as pd
import json
import os

def fix_data_analysis():
    """修复数据分析"""
    
    print("=== 修复数据分析 ===")
    
    processed_files = [f for f in os.listdir('processed') if f.endswith('.xlsx') and not f.startswith('~$')]
    print(f"发现 {len(processed_files)} 个方案文件")
    
    all_schemes = {}
    
    for file in processed_files:
        print(f"\n--- 分析方案: {file} ---")
        file_path = f'processed/{file}'
        
        try:
            # 读取Excel文件，跳过前面的行
            df = pd.read_excel(file_path, sheet_name=0, skiprows=5)
            
            # 查找方案基本信息
            scheme_info = {
                'code': file.replace('.xlsx', ''),
                'name': f'方案 {file.replace(".xlsx", "")}',  # 默认名称
                'hierarchy': {"1": [], "2": [], "3": [], "4": []},
                'total_products': 0
            }
            
            # 查找BOM数据和表头
            bom_found = False
            header_row = None
            
            for i in range(len(df)):
                row_values = [str(val) for val in df.iloc[i].values if pd.notna(val)]
                
                # 查找BOM标识
                if any('BOM' in val for val in row_values):
                    bom_found = True
                    print(f"找到BOM标识在行 {i}")
                    
                    # 在BOM后查找表头
                    for j in range(i, min(i + 5, len(df))):
                        header_values = [str(val) for val in df.iloc[j].values if pd.notna(val)]
                        if any('级别' in val for val in header_values) and any('物件编号' in val for val in header_values):
                            header_row = j
                            print(f"找到表头行: {j}")
                            break
                    break
            
            if header_row is not None:
                # 重新读取，从表头开始
                data_df = pd.read_excel(file_path, sheet_name=0, skiprows=header_row + 5)
                
                # 设置列名
                header_df = pd.read_excel(file_path, sheet_name=0, skiprows=5)
                headers = []
                for val in header_df.iloc[header_row].values:
                    if pd.notna(val):
                        headers.append(str(val).strip())
                    else:
                        headers.append(f'Col_{len(headers)}')
                
                # 获取实际数据
                actual_data = header_df.iloc[header_row + 1:].copy()
                if len(headers) <= len(actual_data.columns):
                    actual_data.columns = headers[:len(actual_data.columns)]
                
                print(f"数据形状: {actual_data.shape}")
                print(f"前几列: {list(actual_data.columns[:10])}")
                
                # 查找关键列
                key_columns = {}
                for col in actual_data.columns:
                    col_str = str(col).lower()
                    if '级别' in col_str:
                        key_columns['level'] = col
                    elif '物件编号' in col_str:
                        key_columns['code'] = col
                    elif '物件描述' in col_str or '描述' in col_str:
                        key_columns['description'] = col
                    elif '数量' in col_str:
                        key_columns['quantity'] = col
                
                print(f"关键列映射: {key_columns}")
                
                if 'code' in key_columns and 'level' in key_columns:
                    # 过滤X08数据
                    valid_mask = (
                        pd.notna(actual_data[key_columns['code']]) & 
                        actual_data[key_columns['code']].astype(str).str.contains('X08', na=False)
                    )
                    valid_data = actual_data[valid_mask].copy()
                    
                    print(f"有效X08数据: {len(valid_data)} 行")
                    
                    if len(valid_data) > 0:
                        # 构建层级数据
                        hierarchy = build_hierarchy(valid_data, key_columns)
                        scheme_info['hierarchy'] = hierarchy
                        scheme_info['total_products'] = len(valid_data)
                        
                        # 尝试获取方案名称
                        try:
                            info_df = pd.read_excel(file_path, sheet_name=0, nrows=10)
                            for i in range(len(info_df)):
                                for val in info_df.iloc[i].values:
                                    if pd.notna(val) and isinstance(val, str) and '解决方案' in val:
                                        scheme_info['name'] = val
                                        break
                                if scheme_info['name'] != f'方案 {file.replace(".xlsx", "")}':
                                    break
                        except:
                            pass
                        
                        all_schemes[scheme_info['code']] = scheme_info
                        
                        print(f"方案名称: {scheme_info['name']}")
                        print("层级统计:")
                        for level, items in hierarchy.items():
                            print(f"  级别{level}: {len(items)} 项")
                            if level == '1' and len(items) > 0:
                                for item in items[:2]:
                                    print(f"    - {item['description']}")
                            elif level == '2' and len(items) > 0:
                                for item in items[:3]:
                                    print(f"    - {item['description']}")
        
        except Exception as e:
            print(f"分析 {file} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    return all_schemes

def build_hierarchy(data_df, key_columns):
    """构建层级结构"""
    hierarchy = {"1": [], "2": [], "3": [], "4": []}
    
    current_parents = {"1": None, "2": None, "3": None}
    
    for idx, row in data_df.iterrows():
        try:
            level = str(int(row[key_columns['level']])) if pd.notna(row[key_columns['level']]) else '4'
            code = str(row[key_columns['code']]) if pd.notna(row[key_columns['code']]) else ''
            desc = str(row[key_columns['description']]) if 'description' in key_columns and pd.notna(row[key_columns['description']]) else code
            
            if code and 'X08' in code and level in hierarchy:
                item = {
                    'sequence': len(hierarchy[level]) + 1,
                    'code': code,
                    'description': desc,
                    'level': level,
                    'quantity': str(row[key_columns['quantity']]) if 'quantity' in key_columns and pd.notna(row[key_columns['quantity']]) else '1',
                    'unit': '个',
                    'brand': '',
                    'model': code,
                    'required': '是',
                    'location': desc,
                    'remark': ''
                }
                
                # 设置父级关系
                if level in ['2', '3', '4']:
                    parent_level = str(int(level) - 1)
                    if parent_level in current_parents and current_parents[parent_level]:
                        item['parent'] = current_parents[parent_level]
                
                # 更新当前父级
                if level in ['1', '2', '3']:
                    current_parents[level] = code
                
                hierarchy[level].append(item)
        
        except Exception as e:
            continue
    
    return hierarchy

def create_final_html():
    """创建最终的HTML系统"""
    
    # 分析方案数据
    schemes = fix_data_analysis()
    
    # 加载生命周期数据
    lifecycle_data = {}
    try:
        df_gov = pd.read_excel('政府退市查询-方案产品表格_20250805_0949.xlsx', sheet_name='方案产品表格')
        for idx, row in df_gov.iterrows():
            option_code = str(row['选件编号']) if pd.notna(row['选件编号']) else ''
            product_code = str(row['产品编号']) if pd.notna(row['产品编号']) else ''
            lifecycle = str(row['生命周期']) if pd.notna(row['生命周期']) else '量产'
            
            if option_code and 'X08' in option_code:
                lifecycle_data[option_code] = lifecycle
            if product_code:
                lifecycle_data[product_code] = lifecycle
    except Exception as e:
        print(f"加载生命周期数据失败: {e}")
    
    # 创建HTML
    html_content = create_html_template(schemes, lifecycle_data)
    
    with open('方案清单管理系统.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"\n=== 系统创建完成 ===")
    print(f"成功创建 {len(schemes)} 个方案的管理系统")
    print("文件: 方案清单管理系统.html")
    
    return schemes, lifecycle_data

def create_html_template(schemes, lifecycle_data):
    """创建HTML模板"""
    
    return f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案清单管理系统</title>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .tab-content {{ display: none; }}
        .tab-content.active {{ display: block; }}
        .lifecycle-badge {{ @apply px-2 py-1 rounded-full text-xs font-medium; }}
        .lifecycle-量产 {{ @apply bg-green-100 text-green-800; }}
        .lifecycle-工程样机 {{ @apply bg-yellow-100 text-yellow-800; }}
        .lifecycle-停产 {{ @apply bg-red-100 text-red-800; }}
        .lifecycle-退市 {{ @apply bg-gray-100 text-gray-800; }}
        .lifecycle-开发 {{ @apply bg-blue-100 text-blue-800; }}
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 头部 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-list-alt mr-2"></i>方案清单管理系统
            </h1>
            
            <!-- 三级选择 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">选择方案:</label>
                    <select id="schemeSelect" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                        <option value="">请选择方案...</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">配置类型:</label>
                    <select id="configType" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1" disabled>
                        <option value="">请先选择方案</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">生命周期:</label>
                    <select id="lifecycleFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                        <option value="">全部</option>
                        <option value="量产">量产</option>
                        <option value="工程样机">工程样机</option>
                        <option value="停产">停产</option>
                        <option value="退市">退市</option>
                    </select>
                </div>
            </div>
            
            <!-- 搜索和更新 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">搜索:</label>
                    <input type="text" id="searchInput" placeholder="搜索产品..." 
                           class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                </div>
                
                <div class="flex items-center space-x-2">
                    <label for="fileUpload" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md cursor-pointer text-sm">
                        <i class="fas fa-upload mr-1"></i>更新生命周期数据
                    </label>
                    <input type="file" id="fileUpload" accept=".xlsx,.xls" class="hidden">
                </div>
            </div>
        </div>

        <!-- 页签导航 -->
        <div class="bg-white rounded-lg shadow-md mb-6" id="tabContainer" style="display: none;">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6 overflow-x-auto" id="tabNavigation">
                    <!-- 动态生成页签 -->
                </nav>
            </div>
        </div>

        <!-- 产品列表 -->
        <div class="bg-white rounded-lg shadow-md p-6" id="productContainer" style="display: none;">
            <div id="productTable">
                <!-- 动态生成产品表格 -->
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let schemesData = {json.dumps(schemes, ensure_ascii=False)};
        let lifecycleData = {json.dumps(lifecycle_data, ensure_ascii=False)};
        let currentScheme = null;
        let currentConfig = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {{
            initializeSchemeSelect();
            setupEventListeners();
        }});

        function initializeSchemeSelect() {{
            const select = document.getElementById('schemeSelect');
            select.innerHTML = '<option value="">请选择方案...</option>';
            
            for (const [code, scheme] of Object.entries(schemesData)) {{
                const option = document.createElement('option');
                option.value = code;
                option.textContent = scheme.name;
                select.appendChild(option);
            }}
        }}

        function setupEventListeners() {{
            document.getElementById('schemeSelect').addEventListener('change', onSchemeChange);
            document.getElementById('configType').addEventListener('change', onConfigChange);
            document.getElementById('lifecycleFilter').addEventListener('change', filterProducts);
            document.getElementById('searchInput').addEventListener('input', function() {{
                searchProducts(this.value);
            }});
        }}

        function onSchemeChange() {{
            const schemeCode = document.getElementById('schemeSelect').value;
            if (!schemeCode) {{
                document.getElementById('configType').disabled = true;
                document.getElementById('configType').innerHTML = '<option value="">请先选择方案</option>';
                hideTabsAndProducts();
                return;
            }}

            currentScheme = schemesData[schemeCode];
            
            // 更新配置选择
            const configSelect = document.getElementById('configType');
            configSelect.disabled = false;
            configSelect.innerHTML = '<option value="">请选择配置...</option>';
            
            // 查找高配/低配选项
            const level1Items = currentScheme.hierarchy["1"] || [];
            level1Items.forEach(item => {{
                const option = document.createElement('option');
                option.value = item.code;
                option.textContent = item.description;
                configSelect.appendChild(option);
            }});
            
            hideTabsAndProducts();
        }}

        function onConfigChange() {{
            const configCode = document.getElementById('configType').value;
            if (!configCode || !currentScheme) {{
                hideTabsAndProducts();
                return;
            }}

            currentConfig = configCode;
            renderTabs();
            showTabsAndProducts();
        }}

        function renderTabs() {{
            const tabNav = document.getElementById('tabNavigation');
            tabNav.innerHTML = '';
            
            const level2Items = currentScheme.hierarchy["2"] || [];
            
            level2Items.forEach((item, index) => {{
                const tabButton = document.createElement('button');
                tabButton.className = `py-4 px-4 border-b-2 font-medium text-sm whitespace-nowrap ${{index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}}`;
                tabButton.textContent = item.description;
                tabButton.onclick = () => switchTab(item.code, tabButton);
                tabNav.appendChild(tabButton);
            }});
            
            // 默认显示第一个页签
            if (level2Items.length > 0) {{
                renderProductTable(level2Items[0].code);
            }}
        }}

        function switchTab(moduleCode, button) {{
            // 更新按钮状态
            document.querySelectorAll('#tabNavigation button').forEach(btn => {{
                btn.className = btn.className.replace('border-blue-500 text-blue-600', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
            }});
            button.className = button.className.replace('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'border-blue-500 text-blue-600');
            
            renderProductTable(moduleCode);
        }}

        function renderProductTable(moduleCode) {{
            const level3Items = (currentScheme.hierarchy["3"] || []).filter(item => item.parent === moduleCode);
            const level4Items = currentScheme.hierarchy["4"] || [];
            
            let html = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">子场景</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品简称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物料代码</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">型号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">品牌</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否必配</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">安装位置及用途</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生命周期</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注说明</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
            `;
            
            let sequence = 1;
            
            level3Items.forEach(level3Item => {{
                const childProducts = level4Items.filter(item => item.parent === level3Item.code);
                
                if (childProducts.length > 0) {{
                    childProducts.forEach(product => {{
                        const lifecycle = getProductLifecycle(product.code);
                        const lifecycleClass = `lifecycle-${{lifecycle}}`;
                        
                        html += `
                            <tr class="product-row" data-lifecycle="${{lifecycle}}">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{sequence}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.description}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.description}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.code}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.model}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.brand || '大华'}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.quantity}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.unit}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.required}}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.location}}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="lifecycle-badge ${{lifecycleClass}}">${{lifecycle}}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{product.remark}}</td>
                            </tr>
                        `;
                        sequence++;
                    }});
                }} else {{
                    // 如果没有子产品，显示级别3项目本身
                    const lifecycle = getProductLifecycle(level3Item.code);
                    const lifecycleClass = `lifecycle-${{lifecycle}}`;
                    
                    html += `
                        <tr class="product-row" data-lifecycle="${{lifecycle}}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{sequence}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.description}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.description}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.code}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.model}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.brand || '大华'}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.quantity}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.unit}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.required}}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.location}}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="lifecycle-badge ${{lifecycleClass}}">${{lifecycle}}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{level3Item.remark}}</td>
                        </tr>
                    `;
                    sequence++;
                }}
            }});
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            document.getElementById('productTable').innerHTML = html;
        }}

        function getProductLifecycle(productCode) {{
            return lifecycleData[productCode] || '量产';
        }}

        function filterProducts() {{
            const lifecycle = document.getElementById('lifecycleFilter').value;
            const rows = document.querySelectorAll('.product-row');
            
            rows.forEach(row => {{
                if (!lifecycle || row.dataset.lifecycle === lifecycle) {{
                    row.style.display = '';
                }} else {{
                    row.style.display = 'none';
                }}
            }});
        }}

        function searchProducts(query) {{
            const rows = document.querySelectorAll('.product-row');
            
            rows.forEach(row => {{
                const text = row.textContent.toLowerCase();
                if (!query || text.includes(query.toLowerCase())) {{
                    row.style.display = '';
                }} else {{
                    row.style.display = 'none';
                }}
            }});
        }}

        function hideTabsAndProducts() {{
            document.getElementById('tabContainer').style.display = 'none';
            document.getElementById('productContainer').style.display = 'none';
        }}

        function showTabsAndProducts() {{
            document.getElementById('tabContainer').style.display = 'block';
            document.getElementById('productContainer').style.display = 'block';
        }}
    </script>
</body>
</html>'''

if __name__ == "__main__":
    create_final_html()
