import pandas as pd
import json
import os

def fix_hierarchy_data():
    """修复层级数据，建立正确的父子关系"""
    
    print("=== 修复层级数据 ===")
    
    processed_files = [f for f in os.listdir('processed') if f.endswith('.xlsx') and not f.startswith('~$')]
    all_schemes = {}
    
    for file in processed_files:
        print(f"\n--- 处理方案: {file} ---")
        file_path = f'processed/{file}'
        
        try:
            # 读取Excel文件，跳过前面的行
            df = pd.read_excel(file_path, sheet_name=0, skiprows=5)
            
            # 查找BOM数据开始位置
            bom_start_row = None
            header_row = None
            
            for i in range(len(df)):
                row_values = [str(val) for val in df.iloc[i].values if pd.notna(val)]
                if any('BOM' in val for val in row_values):
                    bom_start_row = i
                    break
            
            if bom_start_row is not None:
                # 查找表头行
                for i in range(bom_start_row, min(bom_start_row + 5, len(df))):
                    row_values = [str(val) for val in df.iloc[i].values if pd.notna(val)]
                    if any('级别' in val for val in row_values) and any('物件编号' in val for val in row_values):
                        header_row = i
                        break
                
                if header_row is not None:
                    # 获取表头
                    headers = []
                    for val in df.iloc[header_row].values:
                        if pd.notna(val):
                            headers.append(str(val).strip())
                        else:
                            headers.append(f'Col_{len(headers)}')
                    
                    # 获取数据
                    data_df = df.iloc[header_row + 1:].copy()
                    data_df.columns = headers[:len(data_df.columns)]
                    
                    # 过滤X08数据
                    if '物件编号' in data_df.columns and '级别' in data_df.columns:
                        valid_data = data_df[
                            (pd.notna(data_df['物件编号'])) & 
                            (data_df['物件编号'].astype(str).str.contains('X08', na=False))
                        ].copy()
                        
                        print(f"有效X08数据: {len(valid_data)} 行")
                        
                        # 构建带父子关系的层级数据
                        hierarchy = build_hierarchy_with_parents(valid_data)
                        
                        scheme_code = file.replace('.xlsx', '')
                        scheme_names = {
                            'M200701.00024': '司法SDT(司法行政)【主推】智慧监狱安防智能指挥解决方案',
                            'M200701.00025': '司法SDT（海关）综合保税区智能监管解决方案',
                            'M200701.00027': '司法SDT(司法行政)【主推】社矫远程视频督察管控解决方案',
                            'M200701.00033': '司法SDT(司法行政)【主推】监狱AB门智能化管控解决方案',
                            'M200701.00037': '司法SDT(司法行政)监狱指挥协调管控解决方案'
                        }
                        
                        all_schemes[scheme_code] = {
                            'code': scheme_code,
                            'name': scheme_names.get(scheme_code, f'方案 {scheme_code}'),
                            'hierarchy': hierarchy
                        }
                        
                        print(f"方案: {scheme_names.get(scheme_code, scheme_code)}")
                        print("层级统计:")
                        for level, items in hierarchy.items():
                            print(f"  级别{level}: {len(items)} 项")
                            if level == '2':  # 显示页签示例
                                for item in items[:3]:
                                    print(f"    - {item['description']}")
                            elif level == '4':  # 显示设备示例
                                for item in items[:3]:
                                    parent = item.get('parent', '无父级')
                                    print(f"    - {item['description']} (父级: {parent})")
        
        except Exception as e:
            print(f"处理 {file} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    return all_schemes

def build_hierarchy_with_parents(data_df):
    """构建带父子关系的层级数据"""
    hierarchy = {"1": [], "2": [], "3": [], "4": []}
    
    # 用于跟踪当前的父级
    current_parents = {"1": None, "2": None, "3": None}
    
    # 查找关键列
    key_columns = {}
    for col in data_df.columns:
        col_str = str(col).lower()
        if '级别' in col_str:
            key_columns['level'] = col
        elif '物件编号' in col_str:
            key_columns['code'] = col
        elif '物件描述' in col_str or '描述' in col_str:
            key_columns['description'] = col
        elif '数量' in col_str:
            key_columns['quantity'] = col
    
    print(f"关键列: {key_columns}")
    
    for idx, row in data_df.iterrows():
        try:
            level = str(int(row[key_columns['level']])) if pd.notna(row[key_columns['level']]) else '4'
            code = str(row[key_columns['code']]) if pd.notna(row[key_columns['code']]) else ''
            desc = str(row[key_columns['description']]) if 'description' in key_columns and pd.notna(row[key_columns['description']]) else code
            
            if code and 'X08' in code and level in hierarchy:
                item = {
                    'sequence': len(hierarchy[level]) + 1,
                    'code': code,
                    'description': desc,
                    'level': level,
                    'quantity': str(row[key_columns['quantity']]) if 'quantity' in key_columns and pd.notna(row[key_columns['quantity']]) else '1',
                    'unit': '个',
                    'brand': '大华',
                    'model': code,
                    'required': '是',
                    'location': desc,
                    'remark': ''
                }
                
                # 设置父级关系
                if level in ['2', '3', '4']:
                    parent_level = str(int(level) - 1)
                    if parent_level in current_parents and current_parents[parent_level]:
                        item['parent'] = current_parents[parent_level]
                
                # 更新当前父级
                if level in ['1', '2', '3']:
                    current_parents[level] = code
                
                hierarchy[level].append(item)
        
        except Exception as e:
            continue
    
    return hierarchy

def create_updated_html():
    """创建更新后的HTML系统"""
    
    # 修复数据
    schemes = fix_hierarchy_data()
    
    # 加载生命周期数据
    lifecycle_data = {}
    try:
        df_gov = pd.read_excel('政府退市查询-方案产品表格_20250805_0949.xlsx', sheet_name='方案产品表格')
        for idx, row in df_gov.iterrows():
            option_code = str(row['选件编号']) if pd.notna(row['选件编号']) else ''
            product_code = str(row['产品编号']) if pd.notna(row['产品编号']) else ''
            lifecycle = str(row['生命周期']) if pd.notna(row['生命周期']) else '量产'
            
            if option_code and 'X08' in option_code:
                lifecycle_data[option_code] = lifecycle
            if product_code:
                lifecycle_data[product_code] = lifecycle
        
        print(f"\n加载生命周期数据: {len(lifecycle_data)} 项")
    except Exception as e:
        print(f"加载生命周期数据失败: {e}")
    
    # 保存修复后的数据
    final_data = {
        'schemes': schemes,
        'lifecycle': lifecycle_data
    }
    
    with open('修复后的方案数据.json', 'w', encoding='utf-8') as f:
        json.dump(final_data, f, ensure_ascii=False, indent=2)
    
    # 读取现有HTML并更新数据
    with open('方案清单管理系统.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 替换数据
    schemes_js = json.dumps(schemes, ensure_ascii=False, indent=8)
    lifecycle_js = json.dumps(lifecycle_data, ensure_ascii=False, indent=8)
    
    # 查找并替换数据部分
    start_marker = 'let schemesData = '
    end_marker = ';'
    
    start_pos = html_content.find(start_marker)
    if start_pos != -1:
        # 找到第一个数据定义的结束位置
        brace_count = 0
        pos = start_pos + len(start_marker)
        while pos < len(html_content):
            if html_content[pos] == '{':
                brace_count += 1
            elif html_content[pos] == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_pos = pos + 1
                    break
            pos += 1
        
        # 替换schemes数据
        new_schemes_line = f'let schemesData = {schemes_js};'
        html_content = html_content[:start_pos] + new_schemes_line + html_content[end_pos:]
        
        # 替换lifecycle数据
        lifecycle_start = html_content.find('let lifecycleData = ')
        if lifecycle_start != -1:
            brace_count = 0
            pos = lifecycle_start + len('let lifecycleData = ')
            while pos < len(html_content):
                if html_content[pos] == '{':
                    brace_count += 1
                elif html_content[pos] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        lifecycle_end = pos + 1
                        break
                pos += 1
            
            new_lifecycle_line = f'let lifecycleData = {lifecycle_js};'
            html_content = html_content[:lifecycle_start] + new_lifecycle_line + html_content[lifecycle_end:]
    
    # 保存更新后的HTML
    with open('方案清单管理系统_修复版.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"\n=== 修复完成 ===")
    print(f"成功修复 {len(schemes)} 个方案")
    print("文件: 方案清单管理系统_修复版.html")
    print("数据: 修复后的方案数据.json")
    
    return schemes, lifecycle_data

if __name__ == "__main__":
    create_updated_html()
