import pandas as pd
import json
import os

def generate_complete_data():
    """生成完整的数据文件供HTML使用"""
    
    print("=== 生成完整数据文件 ===")
    
    # 1. 读取方案层级数据
    hierarchy_data = {}
    processed_files = [f for f in os.listdir('processed') if f.endswith('.xlsx') and not f.startswith('~$')]
    
    for file in processed_files:
        print(f"处理文件: {file}")
        file_path = f'processed/{file}'
        
        try:
            df = pd.read_excel(file_path, sheet_name=0, skiprows=5)
            
            # 找到BOM数据
            bom_start = None
            for i, row in df.iterrows():
                if str(row.iloc[0]).strip() == 'BOM':
                    bom_start = i + 1
                    break
            
            if bom_start:
                # 找到表头
                header_row = None
                for i in range(max(0, bom_start-3), min(len(df), bom_start+3)):
                    row_values = df.iloc[i].values
                    if any('级别' in str(val) for val in row_values if pd.notna(val)):
                        header_row = i
                        break
                
                if header_row is not None:
                    # 获取数据
                    bom_df = df.iloc[header_row+1:].copy()
                    
                    # 设置列名
                    new_columns = []
                    for col_name in df.iloc[header_row].values:
                        if pd.notna(col_name) and str(col_name).strip():
                            new_columns.append(str(col_name).strip())
                        else:
                            new_columns.append(f'Col_{len(new_columns)}')
                    
                    while len(new_columns) < len(bom_df.columns):
                        new_columns.append(f'Col_{len(new_columns)}')
                    
                    bom_df.columns = new_columns[:len(bom_df.columns)]
                    
                    # 过滤X08数据
                    valid_data = bom_df[
                        (pd.notna(bom_df['物件编号'])) & 
                        (bom_df['物件编号'].astype(str).str.contains('X08', na=False))
                    ].copy()
                    
                    # 按级别分组
                    for idx, row in valid_data.iterrows():
                        level = str(row['级别']) if pd.notna(row['级别']) else '0'
                        code = str(row['物件编号'])
                        desc = str(row['物件描述']) if pd.notna(row['物件描述']) else ''
                        
                        if level not in hierarchy_data:
                            hierarchy_data[level] = []
                        
                        # 查找父级关系
                        parent = None
                        if level == '3':
                            # 级别3的父级是级别2
                            parent = find_parent_by_position(valid_data, idx, '2')
                        elif level == '4':
                            # 级别4的父级是级别3
                            parent = find_parent_by_position(valid_data, idx, '3')
                        
                        item = {
                            'code': code,
                            'description': desc,
                            'level': level,
                            'file': file
                        }
                        
                        if parent:
                            item['parent'] = parent
                        
                        # 避免重复
                        if not any(existing['code'] == code for existing in hierarchy_data[level]):
                            hierarchy_data[level].append(item)
        
        except Exception as e:
            print(f"处理 {file} 失败: {e}")
    
    # 2. 读取生命周期数据
    lifecycle_data = {}
    try:
        df_gov = pd.read_excel('政府退市查询-方案产品表格_20250805_0949.xlsx', sheet_name='方案产品表格')
        print(f"生命周期数据: {len(df_gov)} 行")
        
        for idx, row in df_gov.iterrows():
            option_code = str(row['选件编号']) if pd.notna(row['选件编号']) else ''
            lifecycle = str(row['生命周期']) if pd.notna(row['生命周期']) else '量产'
            
            if option_code and 'X08' in option_code:
                lifecycle_data[option_code] = lifecycle
        
        print(f"生命周期映射: {len(lifecycle_data)} 个产品")
        
    except Exception as e:
        print(f"读取生命周期数据失败: {e}")
    
    # 3. 生成最终数据结构
    final_data = {
        'hierarchy': hierarchy_data,
        'lifecycle': lifecycle_data,
        'metadata': {
            'generated_at': pd.Timestamp.now().isoformat(),
            'total_products': sum(len(items) for items in hierarchy_data.values()),
            'files_processed': len(processed_files)
        }
    }
    
    # 4. 保存数据
    with open('方案数据.json', 'w', encoding='utf-8') as f:
        json.dump(final_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 数据生成完成 ===")
    print(f"层级数据统计:")
    for level, items in hierarchy_data.items():
        print(f"  级别{level}: {len(items)} 项")
    print(f"生命周期数据: {len(lifecycle_data)} 项")
    print(f"数据已保存到: 方案数据.json")
    
    return final_data

def find_parent_by_position(df, current_idx, target_level):
    """根据位置查找父级编码"""
    # 向上查找最近的目标级别项目
    for i in range(current_idx - 1, -1, -1):
        row = df.iloc[i]
        if str(row['级别']) == target_level and pd.notna(row['物件编号']):
            return str(row['物件编号'])
    return None

def update_html_with_data():
    """更新HTML文件中的初始数据"""
    
    # 读取生成的数据
    with open('方案数据.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 读取HTML文件
    with open('方案清单可视化系统.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 替换初始数据
    hierarchy_js = json.dumps(data['hierarchy'], ensure_ascii=False, indent=12)
    lifecycle_js = json.dumps(data['lifecycle'], ensure_ascii=False, indent=12)
    
    # 查找并替换数据
    start_marker = '// 模拟初始层级数据'
    end_marker = '};'
    
    start_pos = html_content.find(start_marker)
    if start_pos != -1:
        end_pos = html_content.find(end_marker, start_pos) + len(end_marker)
        
        new_data_section = f"""// 真实层级数据
            hierarchyData = {hierarchy_js};
            
            // 真实生命周期数据
            productLifecycleData = {lifecycle_js}"""
        
        html_content = html_content[:start_pos] + new_data_section + html_content[end_pos:]
        
        # 保存更新后的HTML
        with open('方案清单可视化系统_完整版.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("HTML文件已更新: 方案清单可视化系统_完整版.html")
    else:
        print("未找到数据替换位置")

if __name__ == "__main__":
    # 生成数据
    data = generate_complete_data()
    
    # 更新HTML文件
    update_html_with_data()
    
    print("\n=== 完成 ===")
    print("请打开 '方案清单可视化系统_完整版.html' 查看完整系统")
