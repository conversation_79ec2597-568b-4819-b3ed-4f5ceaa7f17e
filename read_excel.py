import pandas as pd
import os

def analyze_data_structure():
    # 分析第一个文件的详细结构
    file_path = 'processed/M200701.00024.xlsx'
    print(f"=== 详细分析 {file_path} ===")

    df = pd.read_excel(file_path, sheet_name=0)

    # 找到实际的数据开始行（跳过标题行）
    data_start_row = None
    for i, row in df.iterrows():
        if str(row.iloc[0]).strip() not in ['类型', '描述', '标题块', 'nan', ''] and pd.notna(row.iloc[0]):
            if '编号' in str(row.iloc[0]) or 'M200701' in str(row.iloc[0]):
                data_start_row = i
                break

    print(f"数据开始行: {data_start_row}")

    if data_start_row is not None:
        # 重新读取，跳过前面的行
        df_clean = pd.read_excel(file_path, sheet_name=0, skiprows=data_start_row)
        print(f"清理后的列名: {df_clean.columns.tolist()}")
        print(f"清理后的数据形状: {df_clean.shape}")

        # 查看前20行数据
        print("\n前20行数据:")
        for i, row in df_clean.head(20).iterrows():
            print(f"行{i}: {[str(x)[:50] if pd.notna(x) else 'NaN' for x in row.values[:10]]}")

        # 分析X08编码的层级结构
        print("\n=== X08编码层级分析 ===")
        x08_col = None
        for col in df_clean.columns:
            if df_clean[col].dtype == 'object':
                x08_mask = df_clean[col].astype(str).str.contains('X08', na=False)
                if x08_mask.any():
                    x08_col = col
                    break

        if x08_col:
            print(f"X08数据在列: {x08_col}")
            x08_data = df_clean[df_clean[x08_col].astype(str).str.contains('X08', na=False)]

            # 分析编码模式
            x08_codes = x08_data[x08_col].unique()
            print(f"\n找到 {len(x08_codes)} 个X08编码:")

            # 按编码模式分组
            level1_codes = []  # X08.01.01002 类型（高配/低配）
            level2_codes = []  # X08.01.00819 类型（页签分类）

            for code in x08_codes:
                code_str = str(code)
                if 'X08' in code_str:
                    parts = code_str.split('.')
                    if len(parts) >= 3:
                        # 分析第三部分的数字模式
                        third_part = parts[2]
                        if third_part.startswith('01') and len(third_part) == 5:
                            if third_part[2:] in ['002', '003', '004']:  # 可能是高配/低配标识
                                level1_codes.append(code_str)
                            else:
                                level2_codes.append(code_str)
                        else:
                            level2_codes.append(code_str)

            print(f"\n可能的第一级编码（高配/低配）: {len(level1_codes)} 个")
            for code in sorted(level1_codes)[:10]:
                print(f"  - {code}")

            print(f"\n可能的第二级编码（页签分类）: {len(level2_codes)} 个")
            for code in sorted(level2_codes)[:10]:
                print(f"  - {code}")

            # 查看每个编码对应的产品信息
            print(f"\n=== 编码对应的产品信息 ===")
            for code in sorted(x08_codes)[:5]:
                code_rows = x08_data[x08_data[x08_col] == code]
                print(f"\n编码 {code} 对应的产品:")
                for idx, row in code_rows.head(3).iterrows():
                    # 查找产品名称列（通常在描述/名称列）
                    desc_col = None
                    for col in df_clean.columns:
                        if '描述' in str(col) or '名称' in str(col):
                            desc_col = col
                            break

                    if desc_col and pd.notna(row[desc_col]):
                        print(f"  - {row[desc_col]}")

def read_government_file():
    print("\n=== 重新分析政府退市查询表格 ===")
    try:
        # 尝试不同的读取方式
        df_gov = pd.read_excel('政府退市查询-方案产品表格_20250805_0949.xlsx', header=None)
        print(f"原始数据形状: {df_gov.shape}")

        # 查看前10行来找到实际的表头
        print("前10行数据:")
        for i in range(min(10, len(df_gov))):
            row_data = [str(x)[:30] if pd.notna(x) else 'NaN' for x in df_gov.iloc[i].values[:8]]
            print(f"行{i}: {row_data}")

    except Exception as e:
        print(f"读取政府退市查询表格失败: {e}")

if __name__ == "__main__":
    analyze_data_structure()
    read_government_file()
