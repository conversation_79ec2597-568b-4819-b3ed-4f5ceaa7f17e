import pandas as pd
import json

def read_government_file():
    file_path = '政府退市查询-方案产品表格_20250805_0949.xlsx'
    
    print("=== 政府退市查询表格分析 ===")
    
    try:
        # 尝试读取所有sheet
        excel_file = pd.ExcelFile(file_path)
        print(f"Sheet名称: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- Sheet: {sheet_name} ---")
            
            # 读取sheet
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"数据形状: {df.shape}")
            
            if df.shape[0] > 0 and df.shape[1] > 0:
                print(f"列名: {df.columns.tolist()}")
                
                # 查看前10行数据
                print("\n前10行数据:")
                for i in range(min(10, len(df))):
                    row_data = []
                    for j, val in enumerate(df.iloc[i].values):
                        if j < 8:  # 只显示前8列
                            if pd.notna(val):
                                row_data.append(str(val)[:30])
                            else:
                                row_data.append('NaN')
                    print(f"行{i}: {row_data}")
                
                # 查找包含产品信息的列
                print(f"\n数据类型分析:")
                for col in df.columns[:10]:  # 只分析前10列
                    non_null_count = df[col].notna().sum()
                    print(f"  {col}: {non_null_count} 个非空值")
                    
                    # 如果有数据，显示几个示例
                    if non_null_count > 0:
                        sample_data = df[col].dropna().head(3).tolist()
                        print(f"    示例: {sample_data}")
            else:
                print("空数据表")
    
    except Exception as e:
        print(f"读取失败: {e}")
        
        # 尝试其他读取方式
        try:
            print("\n尝试无表头读取...")
            df = pd.read_excel(file_path, header=None)
            print(f"无表头数据形状: {df.shape}")
            
            if df.shape[0] > 0:
                print("前10行原始数据:")
                for i in range(min(10, len(df))):
                    row_data = []
                    for j, val in enumerate(df.iloc[i].values):
                        if j < 8:
                            if pd.notna(val):
                                row_data.append(str(val)[:30])
                            else:
                                row_data.append('NaN')
                    print(f"行{i}: {row_data}")
        except Exception as e2:
            print(f"无表头读取也失败: {e2}")

def analyze_processed_files():
    print("\n" + "="*80)
    print("=== 分析processed文件夹中的方案数据 ===")
    
    import os
    processed_files = [f for f in os.listdir('processed') if f.endswith('.xlsx') and not f.startswith('~$')]
    
    all_data = {}
    
    for file in processed_files[:1]:  # 先分析一个文件
        print(f"\n--- 分析 {file} ---")
        file_path = f'processed/{file}'
        
        try:
            df = pd.read_excel(file_path, sheet_name=0, skiprows=5)
            
            # 找到BOM数据
            bom_start = None
            for i, row in df.iterrows():
                if str(row.iloc[0]).strip() == 'BOM':
                    bom_start = i + 1
                    break
            
            if bom_start:
                # 找到表头
                header_row = None
                for i in range(max(0, bom_start-3), min(len(df), bom_start+3)):
                    row_values = df.iloc[i].values
                    if any('级别' in str(val) for val in row_values if pd.notna(val)):
                        header_row = i
                        break
                
                if header_row is not None:
                    # 获取数据
                    bom_df = df.iloc[header_row+1:].copy()
                    
                    # 设置列名
                    new_columns = []
                    for col_name in df.iloc[header_row].values:
                        if pd.notna(col_name) and str(col_name).strip():
                            new_columns.append(str(col_name).strip())
                        else:
                            new_columns.append(f'Col_{len(new_columns)}')
                    
                    while len(new_columns) < len(bom_df.columns):
                        new_columns.append(f'Col_{len(new_columns)}')
                    
                    bom_df.columns = new_columns[:len(bom_df.columns)]
                    
                    # 过滤X08数据
                    valid_data = bom_df[
                        (pd.notna(bom_df['物件编号'])) & 
                        (bom_df['物件编号'].astype(str).str.contains('X08', na=False))
                    ].copy()
                    
                    print(f"有效X08数据: {len(valid_data)} 行")
                    
                    # 构建层级数据结构
                    hierarchy = {}
                    
                    for idx, row in valid_data.iterrows():
                        level = str(row['级别']) if pd.notna(row['级别']) else '0'
                        code = str(row['物件编号'])
                        desc = str(row['物件描述']) if pd.notna(row['物件描述']) else ''
                        
                        if level not in hierarchy:
                            hierarchy[level] = []
                        
                        hierarchy[level].append({
                            'code': code,
                            'description': desc,
                            'level': level
                        })
                    
                    all_data[file] = hierarchy
                    
                    print(f"层级分布:")
                    for level, items in hierarchy.items():
                        print(f"  级别{level}: {len(items)} 项")
        
        except Exception as e:
            print(f"分析 {file} 失败: {e}")
    
    # 保存数据结构到JSON文件
    if all_data:
        with open('方案数据结构.json', 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)
        print(f"\n数据结构已保存到 方案数据结构.json")

if __name__ == "__main__":
    read_government_file()
    analyze_processed_files()
