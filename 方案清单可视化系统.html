<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案清单可视化系统</title>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .lifecycle-badge {
            @apply px-2 py-1 rounded-full text-xs font-medium;
        }
        .lifecycle-量产 { @apply bg-green-100 text-green-800; }
        .lifecycle-工程样机 { @apply bg-yellow-100 text-yellow-800; }
        .lifecycle-停产 { @apply bg-red-100 text-red-800; }
        .lifecycle-退市 { @apply bg-gray-100 text-gray-800; }
        .lifecycle-开发 { @apply bg-blue-100 text-blue-800; }
        .search-highlight { background-color: yellow; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 头部 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-list-alt mr-2"></i>方案清单可视化系统
            </h1>
            
            <!-- 控制面板 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <!-- 高配/低配选择 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">配置类型:</label>
                    <select id="configType" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="high">高配</option>
                        <option value="low">低配</option>
                    </select>
                </div>
                
                <!-- 生命周期筛选 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">生命周期:</label>
                    <select id="lifecycleFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="">全部</option>
                        <option value="量产">量产</option>
                        <option value="工程样机">工程样机</option>
                        <option value="停产">停产</option>
                        <option value="退市">退市</option>
                        <option value="开发">开发</option>
                    </select>
                </div>
                
                <!-- 搜索框 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">搜索:</label>
                    <input type="text" id="searchInput" placeholder="搜索产品..." 
                           class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                </div>
                
                <!-- 文件上传 -->
                <div class="flex items-center space-x-2">
                    <label for="fileUpload" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md cursor-pointer text-sm">
                        <i class="fas fa-upload mr-1"></i>更新数据
                    </label>
                    <input type="file" id="fileUpload" accept=".xlsx,.xls" class="hidden">
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                    <div class="text-sm text-gray-600">总产品数</div>
                </div>
                <div class="bg-green-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="activeCount">0</div>
                    <div class="text-sm text-gray-600">量产产品</div>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600" id="sampleCount">0</div>
                    <div class="text-sm text-gray-600">工程样机</div>
                </div>
                <div class="bg-red-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-red-600" id="retiredCount">0</div>
                    <div class="text-sm text-gray-600">停产/退市</div>
                </div>
                <div class="bg-purple-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600" id="replacedCount">0</div>
                    <div class="text-sm text-gray-600">今日替换</div>
                </div>
            </div>
        </div>

        <!-- 页签导航 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" id="tabNavigation">
                    <!-- 动态生成页签 -->
                </nav>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div id="tabContent">
                <!-- 动态生成内容 -->
            </div>
        </div>

        <!-- 替换记录弹窗 -->
        <div id="replaceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">设备替换</h3>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">原设备:</label>
                        <input type="text" id="originalProduct" readonly class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">替换为:</label>
                        <select id="replacementProduct" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="">选择替换产品...</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">替换原因:</label>
                        <textarea id="replaceReason" class="w-full border border-gray-300 rounded-md px-3 py-2" rows="3" placeholder="请输入替换原因..."></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button onclick="closeReplaceModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                            取消
                        </button>
                        <button onclick="confirmReplace()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                            确认替换
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出按钮 -->
        <div class="fixed bottom-6 right-6">
            <button onclick="exportReplacements()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-full shadow-lg">
                <i class="fas fa-download mr-2"></i>导出今日替换
            </button>
        </div>
    </div>

    <script>
        // 全局数据存储
        let hierarchyData = {};
        let productLifecycleData = {};
        let replacementRecords = JSON.parse(localStorage.getItem('replacementRecords') || '[]');
        let currentConfig = 'high';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadInitialData();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('configType').addEventListener('change', function() {
                currentConfig = this.value;
                renderTabs();
                updateStatistics();
            });

            document.getElementById('lifecycleFilter').addEventListener('change', function() {
                filterProducts();
            });

            document.getElementById('searchInput').addEventListener('input', function() {
                searchProducts(this.value);
            });

            document.getElementById('fileUpload').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    loadExcelFile(e.target.files[0]);
                }
            });
        }

        // 加载初始数据
        function loadInitialData() {
            // 模拟初始层级数据
            hierarchyData = {
                "1": [
                    {code: "X08.01.01002", description: "高配-智慧监狱全面建设", level: "1"},
                    {code: "X08.01.01003", description: "低配-智慧监狱基础建设", level: "1"}
                ],
                "2": [
                    {code: "X08.01.00819", description: "一、视频监控系统-前端摄像机", level: "2"},
                    {code: "X08.01.00820", description: "二、视频监控系统-大屏显控系统", level: "2"},
                    {code: "X08.01.00821", description: "三、视频智能分析系统", level: "2"},
                    {code: "X08.01.00822", description: "四、智能门禁系统", level: "2"},
                    {code: "X08.01.00823", description: "五、电子巡查系统", level: "2"},
                    {code: "X08.01.00824", description: "六、应急报警系统", level: "2"}
                ],
                "3": [
                    {code: "X08.01.00843", description: "监室", level: "3", parent: "X08.01.00819"},
                    {code: "X08.01.00844", description: "周界", level: "3", parent: "X08.01.00819"},
                    {code: "X08.01.00845", description: "AB门", level: "3", parent: "X08.01.00822"}
                ],
                "4": [
                    {code: "X08.02.00079", description: "监室单目全景智能半球", level: "4", parent: "X08.01.00843"},
                    {code: "X08.02.00167", description: "枪型网络摄像机", level: "4", parent: "X08.01.00844"}
                ]
            };

            renderTabs();
            updateStatistics();
        }

        // 渲染页签
        function renderTabs() {
            const tabNav = document.getElementById('tabNavigation');
            const tabContent = document.getElementById('tabContent');

            // 获取当前配置的级别2数据（页签）
            const level2Items = hierarchyData["2"] || [];

            // 清空现有内容
            tabNav.innerHTML = '';
            tabContent.innerHTML = '';

            // 生成页签导航
            level2Items.forEach((item, index) => {
                const tabButton = document.createElement('button');
                tabButton.className = `py-4 px-1 border-b-2 font-medium text-sm ${index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`;
                tabButton.textContent = item.description;
                tabButton.onclick = () => switchTab(item.code, tabButton);
                tabNav.appendChild(tabButton);

                // 生成页签内容
                const tabPane = document.createElement('div');
                tabPane.id = `tab-${item.code}`;
                tabPane.className = `tab-content ${index === 0 ? 'active' : ''}`;
                tabPane.innerHTML = generateTabContent(item.code);
                tabContent.appendChild(tabPane);
            });
        }

        // 生成页签内容
        function generateTabContent(parentCode) {
            const level3Items = (hierarchyData["3"] || []).filter(item => item.parent === parentCode);
            const level4Items = hierarchyData["4"] || [];

            let html = '<div class="space-y-6">';

            level3Items.forEach(level3Item => {
                const childProducts = level4Items.filter(item => item.parent === level3Item.code);

                html += `
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">
                            <i class="fas fa-folder mr-2"></i>${level3Item.description}
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                `;

                childProducts.forEach(product => {
                    const lifecycle = getProductLifecycle(product.code);
                    const lifecycleClass = `lifecycle-${lifecycle}`;

                    html += `
                        <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow product-card" data-lifecycle="${lifecycle}">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-medium text-gray-800 text-sm">${product.description}</h4>
                                <span class="lifecycle-badge ${lifecycleClass}">${lifecycle}</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-3">${product.code}</p>
                            <div class="flex justify-between items-center">
                                <button onclick="viewProductDetails('${product.code}')"
                                        class="text-blue-600 hover:text-blue-800 text-xs">
                                    <i class="fas fa-eye mr-1"></i>详情
                                </button>
                                ${lifecycle === '停产' || lifecycle === '退市' ?
                                    `<button onclick="openReplaceModal('${product.code}', '${product.description}')"
                                             class="text-orange-600 hover:text-orange-800 text-xs">
                                        <i class="fas fa-exchange-alt mr-1"></i>替换
                                    </button>` : ''
                                }
                            </div>
                        </div>
                    `;
                });

                html += '</div></div>';
            });

            html += '</div>';
            return html;
        }

        // 切换页签
        function switchTab(code, button) {
            // 更新按钮状态
            document.querySelectorAll('#tabNavigation button').forEach(btn => {
                btn.className = btn.className.replace('border-blue-500 text-blue-600', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
            });
            button.className = button.className.replace('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'border-blue-500 text-blue-600');

            // 切换内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`tab-${code}`).classList.add('active');
        }

        // 获取产品生命周期
        function getProductLifecycle(productCode) {
            return productLifecycleData[productCode] || '量产';
        }

        // 更新统计信息
        function updateStatistics() {
            const allProducts = getAllProducts();
            const totalCount = allProducts.length;
            const activeCount = allProducts.filter(p => getProductLifecycle(p.code) === '量产').length;
            const sampleCount = allProducts.filter(p => getProductLifecycle(p.code) === '工程样机').length;
            const retiredCount = allProducts.filter(p => ['停产', '退市'].includes(getProductLifecycle(p.code))).length;
            const todayReplacements = replacementRecords.filter(r => isToday(new Date(r.date))).length;

            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('activeCount').textContent = activeCount;
            document.getElementById('sampleCount').textContent = sampleCount;
            document.getElementById('retiredCount').textContent = retiredCount;
            document.getElementById('replacedCount').textContent = todayReplacements;
        }

        // 获取所有产品
        function getAllProducts() {
            return hierarchyData["4"] || [];
        }

        // 筛选产品
        function filterProducts() {
            const lifecycle = document.getElementById('lifecycleFilter').value;
            const productCards = document.querySelectorAll('.product-card');

            productCards.forEach(card => {
                if (!lifecycle || card.dataset.lifecycle === lifecycle) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // 搜索产品
        function searchProducts(query) {
            const productCards = document.querySelectorAll('.product-card');

            productCards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (!query || text.includes(query.toLowerCase())) {
                    card.style.display = 'block';
                    // 高亮搜索结果
                    if (query) {
                        highlightText(card, query);
                    } else {
                        removeHighlight(card);
                    }
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // 高亮文本
        function highlightText(element, query) {
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            textNodes.forEach(textNode => {
                const text = textNode.textContent;
                const regex = new RegExp(`(${query})`, 'gi');
                if (regex.test(text)) {
                    const highlightedText = text.replace(regex, '<span class="search-highlight">$1</span>');
                    const span = document.createElement('span');
                    span.innerHTML = highlightedText;
                    textNode.parentNode.replaceChild(span, textNode);
                }
            });
        }

        // 移除高亮
        function removeHighlight(element) {
            const highlights = element.querySelectorAll('.search-highlight');
            highlights.forEach(highlight => {
                highlight.outerHTML = highlight.innerHTML;
            });
        }

        // 加载Excel文件
        function loadExcelFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {type: 'array'});

                    // 查找"方案产品表格"工作表
                    const sheetName = workbook.SheetNames.find(name => name.includes('方案产品表格')) || workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);

                    // 更新产品生命周期数据
                    updateProductLifecycleData(jsonData);

                    // 重新渲染
                    renderTabs();
                    updateStatistics();

                    alert('数据更新成功！');
                } catch (error) {
                    console.error('文件读取错误:', error);
                    alert('文件读取失败，请检查文件格式！');
                }
            };
            reader.readAsArrayBuffer(file);
        }

        // 更新产品生命周期数据
        function updateProductLifecycleData(jsonData) {
            productLifecycleData = {};
            jsonData.forEach(row => {
                const optionCode = row['选件编号'];
                const lifecycle = row['生命周期'];
                if (optionCode && lifecycle) {
                    productLifecycleData[optionCode] = lifecycle;
                }
            });

            // 保存到本地存储
            localStorage.setItem('productLifecycleData', JSON.stringify(productLifecycleData));
        }

        // 查看产品详情
        function viewProductDetails(productCode) {
            const product = getAllProducts().find(p => p.code === productCode);
            const lifecycle = getProductLifecycle(productCode);

            alert(`产品详情：\n编号：${productCode}\n描述：${product ? product.description : '未知'}\n生命周期：${lifecycle}`);
        }

        // 打开替换弹窗
        function openReplaceModal(productCode, productDesc) {
            document.getElementById('originalProduct').value = `${productCode} - ${productDesc}`;

            // 填充替换产品选项（只显示量产状态的产品）
            const replacementSelect = document.getElementById('replacementProduct');
            replacementSelect.innerHTML = '<option value="">选择替换产品...</option>';

            const activeProducts = getAllProducts().filter(p => getProductLifecycle(p.code) === '量产');
            activeProducts.forEach(product => {
                const option = document.createElement('option');
                option.value = product.code;
                option.textContent = `${product.code} - ${product.description}`;
                replacementSelect.appendChild(option);
            });

            document.getElementById('replaceModal').classList.remove('hidden');
        }

        // 关闭替换弹窗
        function closeReplaceModal() {
            document.getElementById('replaceModal').classList.add('hidden');
            document.getElementById('replacementProduct').value = '';
            document.getElementById('replaceReason').value = '';
        }

        // 确认替换
        function confirmReplace() {
            const originalProduct = document.getElementById('originalProduct').value;
            const replacementProduct = document.getElementById('replacementProduct').value;
            const reason = document.getElementById('replaceReason').value;

            if (!replacementProduct) {
                alert('请选择替换产品！');
                return;
            }

            if (!reason.trim()) {
                alert('请输入替换原因！');
                return;
            }

            // 记录替换
            const replacementRecord = {
                id: Date.now(),
                date: new Date().toISOString(),
                originalProduct: originalProduct,
                replacementProduct: replacementProduct,
                reason: reason.trim(),
                operator: '当前用户' // 可以根据需要修改
            };

            replacementRecords.push(replacementRecord);
            localStorage.setItem('replacementRecords', JSON.stringify(replacementRecords));

            // 更新统计
            updateStatistics();

            // 关闭弹窗
            closeReplaceModal();

            alert('替换记录已保存！');
        }

        // 导出今日替换记录
        function exportReplacements() {
            const today = new Date();
            const todayReplacements = replacementRecords.filter(r => isToday(new Date(r.date)));

            if (todayReplacements.length === 0) {
                alert('今日暂无替换记录！');
                return;
            }

            // 创建Excel数据
            const exportData = todayReplacements.map(record => ({
                '替换时间': new Date(record.date).toLocaleString('zh-CN'),
                '原产品': record.originalProduct,
                '替换产品': record.replacementProduct,
                '替换原因': record.reason,
                '操作人员': record.operator
            }));

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(exportData);

            // 设置列宽
            ws['!cols'] = [
                {wch: 20}, // 替换时间
                {wch: 40}, // 原产品
                {wch: 40}, // 替换产品
                {wch: 30}, // 替换原因
                {wch: 15}  // 操作人员
            ];

            XLSX.utils.book_append_sheet(wb, ws, '今日替换记录');

            // 下载文件
            const fileName = `设备替换记录_${today.getFullYear()}${(today.getMonth()+1).toString().padStart(2,'0')}${today.getDate().toString().padStart(2,'0')}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        // 判断是否为今天
        function isToday(date) {
            const today = new Date();
            return date.getDate() === today.getDate() &&
                   date.getMonth() === today.getMonth() &&
                   date.getFullYear() === today.getFullYear();
        }

        // 页面加载时恢复本地数据
        window.addEventListener('load', function() {
            const savedLifecycleData = localStorage.getItem('productLifecycleData');
            if (savedLifecycleData) {
                productLifecycleData = JSON.parse(savedLifecycleData);
            }
        });
    </script>
</body>
</html>
