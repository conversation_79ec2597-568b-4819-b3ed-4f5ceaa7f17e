<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案清单管理系统</title>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .lifecycle-badge { @apply px-2 py-1 rounded-full text-xs font-medium; }
        .lifecycle-量产 { @apply bg-green-100 text-green-800; }
        .lifecycle-工程样机 { @apply bg-yellow-100 text-yellow-800; }
        .lifecycle-停产 { @apply bg-red-100 text-red-800; }
        .lifecycle-退市 { @apply bg-gray-100 text-gray-800; }
        .lifecycle-开发 { @apply bg-blue-100 text-blue-800; }
        .table-container { max-height: 600px; overflow-y: auto; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 头部 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-list-alt mr-2"></i>方案清单管理系统
            </h1>
            
            <!-- 四级选择说明 -->
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>选择流程：</strong>
                            第一级：选择方案 → 第二级：选择高配/低配 → 第三级：选择模块页签 → 第四级：查看具体设备清单
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 三级选择 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">第一级-选择方案:</label>
                    <select id="schemeSelect" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                        <option value="">请选择方案...</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">第二级-配置类型:</label>
                    <select id="configType" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1" disabled>
                        <option value="">请先选择方案</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">筛选生命周期:</label>
                    <select id="lifecycleFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                        <option value="">全部</option>
                        <option value="量产">量产</option>
                        <option value="工程样机">工程样机</option>
                        <option value="停产">停产</option>
                        <option value="退市">退市</option>
                    </select>
                </div>
            </div>
            
            <!-- 搜索和更新 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">搜索设备:</label>
                    <input type="text" id="searchInput" placeholder="搜索设备名称或编号..." 
                           class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                </div>
                
                <div class="flex items-center space-x-2">
                    <label for="fileUpload" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md cursor-pointer text-sm">
                        <i class="fas fa-upload mr-1"></i>更新生命周期数据
                    </label>
                    <input type="file" id="fileUpload" accept=".xlsx,.xls" class="hidden">
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="grid grid-cols-4 gap-4 text-center">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                    <div class="text-sm text-gray-600">总设备数</div>
                </div>
                <div class="bg-green-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="activeCount">0</div>
                    <div class="text-sm text-gray-600">量产设备</div>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600" id="sampleCount">0</div>
                    <div class="text-sm text-gray-600">工程样机</div>
                </div>
                <div class="bg-red-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-red-600" id="retiredCount">0</div>
                    <div class="text-sm text-gray-600">停产/退市</div>
                </div>
            </div>
        </div>

        <!-- 第三级：页签导航 -->
        <div class="bg-white rounded-lg shadow-md mb-6" id="tabContainer" style="display: none;">
            <div class="border-b border-gray-200">
                <div class="px-6 py-3 bg-gray-50">
                    <h3 class="text-lg font-medium text-gray-800">第三级 - 模块页签选择</h3>
                </div>
                <nav class="flex space-x-2 px-6 overflow-x-auto" id="tabNavigation">
                    <!-- 动态生成页签 -->
                </nav>
            </div>
        </div>

        <!-- 第四级：设备清单 -->
        <div class="bg-white rounded-lg shadow-md p-6" id="productContainer" style="display: none;">
            <div class="mb-4">
                <h3 class="text-lg font-medium text-gray-800">第四级 - 具体设备清单</h3>
                <p class="text-sm text-gray-600">显示所选模块下的具体设备信息</p>
            </div>
            <div class="table-container">
                <div id="productTable">
                    <!-- 动态生成设备表格 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let schemesData = {
        "M200701.00024": {
                "code": "M200701.00024",
                "name": "司法SDT(司法行政)【主推】智慧监狱安防智能指挥解决方案",
                "hierarchy": {
                        "1": [
                                {
                                        "sequence": 1,
                                        "code": "X08.01.01002",
                                        "description": "高配-智慧监狱全面建设",
                                        "level": "1",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01002",
                                        "required": "是",
                                        "location": "高配-智慧监狱全面建设",
                                        "remark": ""
                                },
                                {
                                        "sequence": 2,
                                        "code": "X08.01.01003",
                                        "description": "低配-智慧监狱基础建设",
                                        "level": "1",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01003",
                                        "required": "是",
                                        "location": "低配-智慧监狱基础建设",
                                        "remark": ""
                                }
                        ],
                        "2": [
                                {
                                        "sequence": 1,
                                        "code": "X08.01.00819",
                                        "description": "一、视频监控系统-前端摄像机",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00819",
                                        "required": "是",
                                        "location": "一、视频监控系统-前端摄像机",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 2,
                                        "code": "X08.01.00820",
                                        "description": "二、视频监控系统-大屏显控系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00820",
                                        "required": "是",
                                        "location": "二、视频监控系统-大屏显控系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 3,
                                        "code": "X08.01.00821",
                                        "description": "三、视频智能分析系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00821",
                                        "required": "是",
                                        "location": "三、视频智能分析系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 4,
                                        "code": "X08.01.00822",
                                        "description": "四、智能门禁系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00822",
                                        "required": "是",
                                        "location": "四、智能门禁系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 5,
                                        "code": "X08.01.00823",
                                        "description": "五、电子巡查系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00823",
                                        "required": "是",
                                        "location": "五、电子巡查系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 6,
                                        "code": "X08.01.00824",
                                        "description": "六、应急报警系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00824",
                                        "required": "是",
                                        "location": "六、应急报警系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 7,
                                        "code": "X08.01.01908",
                                        "description": "七、监听对讲系统及监仓屏",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01908",
                                        "required": "是",
                                        "location": "七、监听对讲系统及监仓屏",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 8,
                                        "code": "X08.01.02082",
                                        "description": "八、广播系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02082",
                                        "required": "是",
                                        "location": "八、广播系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 9,
                                        "code": "X08.01.00827",
                                        "description": "九、审讯谈话系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00827",
                                        "required": "是",
                                        "location": "九、审讯谈话系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 10,
                                        "code": "X08.01.00828",
                                        "description": "十、安检系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00828",
                                        "required": "是",
                                        "location": "十、安检系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 11,
                                        "code": "X08.01.00829",
                                        "description": "十一、AB门管理系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00829",
                                        "required": "是",
                                        "location": "十一、AB门管理系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 12,
                                        "code": "X08.01.00830",
                                        "description": "十二、人车区域管控",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00830",
                                        "required": "是",
                                        "location": "十二、人车区域管控",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 13,
                                        "code": "X08.01.00831",
                                        "description": "十三、人员智能点名",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00831",
                                        "required": "是",
                                        "location": "十三、人员智能点名",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 14,
                                        "code": "X08.01.00833",
                                        "description": "十五、服药&夜巡系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00833",
                                        "required": "是",
                                        "location": "十五、服药&夜巡系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 15,
                                        "code": "X08.01.00834",
                                        "description": "十六、应急指挥系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00834",
                                        "required": "是",
                                        "location": "十六、应急指挥系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 16,
                                        "code": "X08.01.00835",
                                        "description": "十七、中心-配套服务器",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00835",
                                        "required": "是",
                                        "location": "十七、中心-配套服务器",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 17,
                                        "code": "X08.01.00836",
                                        "description": "十八、中心-校时服务器",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00836",
                                        "required": "是",
                                        "location": "十八、中心-校时服务器",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 18,
                                        "code": "X08.01.00837",
                                        "description": "十九、中心-存储设备（视频云）",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00837",
                                        "required": "是",
                                        "location": "十九、中心-存储设备（视频云）",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 19,
                                        "code": "X08.01.00838",
                                        "description": "二十、中心-国标联网网关",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00838",
                                        "required": "是",
                                        "location": "二十、中心-国标联网网关",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 20,
                                        "code": "X08.01.00839",
                                        "description": "二十一、中心-报警门禁对讲接入网关",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00839",
                                        "required": "是",
                                        "location": "二十一、中心-报警门禁对讲接入网关",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 21,
                                        "code": "X08.01.00840",
                                        "description": "二十二、中心-P9500软件模块",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00840",
                                        "required": "是",
                                        "location": "二十二、中心-P9500软件模块",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 22,
                                        "code": "X08.01.00841",
                                        "description": "二十三、中心-运维软件平台",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00841",
                                        "required": "是",
                                        "location": "二十三、中心-运维软件平台",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 23,
                                        "code": "X08.01.00842",
                                        "description": "二十四、中心-视频质量诊断",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00842",
                                        "required": "是",
                                        "location": "二十四、中心-视频质量诊断",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 24,
                                        "code": "X08.01.01666",
                                        "description": "二十五、国产化",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01666",
                                        "required": "是",
                                        "location": "二十五、国产化",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 25,
                                        "code": "X08.01.01932",
                                        "description": "二十六、无人机及反制",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01932",
                                        "required": "是",
                                        "location": "二十六、无人机及反制",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 26,
                                        "code": "X08.01.01934",
                                        "description": "二十七、数通-交换机",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01934",
                                        "required": "是",
                                        "location": "二十七、数通-交换机",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 27,
                                        "code": "X08.01.01939",
                                        "description": "二十八、数通-安全产品",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01939",
                                        "required": "是",
                                        "location": "二十八、数通-安全产品",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 28,
                                        "code": "X08.01.00991",
                                        "description": "一、视频监控系统-前端摄像机",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00991",
                                        "required": "是",
                                        "location": "一、视频监控系统-前端摄像机",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 29,
                                        "code": "X08.01.00990",
                                        "description": "二、视频监控系统-大屏显控系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00990",
                                        "required": "是",
                                        "location": "二、视频监控系统-大屏显控系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 30,
                                        "code": "X08.01.00992",
                                        "description": "三、视频智能分析系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00992",
                                        "required": "是",
                                        "location": "三、视频智能分析系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 31,
                                        "code": "X08.01.00993",
                                        "description": "四、智能门禁系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00993",
                                        "required": "是",
                                        "location": "四、智能门禁系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 32,
                                        "code": "X08.01.00994",
                                        "description": "五、电子巡查系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00994",
                                        "required": "是",
                                        "location": "五、电子巡查系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 33,
                                        "code": "X08.01.00995",
                                        "description": "六、应急报警系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00995",
                                        "required": "是",
                                        "location": "六、应急报警系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 34,
                                        "code": "X08.01.01916",
                                        "description": "七、监听对讲系统及监仓屏",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01916",
                                        "required": "是",
                                        "location": "七、监听对讲系统及监仓屏",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 35,
                                        "code": "X08.01.02072",
                                        "description": "八、广播系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02072",
                                        "required": "是",
                                        "location": "八、广播系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 36,
                                        "code": "X08.01.00998",
                                        "description": "九、审讯谈话系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00998",
                                        "required": "是",
                                        "location": "九、审讯谈话系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 37,
                                        "code": "X08.01.00999",
                                        "description": "十、安检系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00999",
                                        "required": "是",
                                        "location": "十、安检系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 38,
                                        "code": "X08.01.01000",
                                        "description": "十一、AB门管理系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01000",
                                        "required": "是",
                                        "location": "十一、AB门管理系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 39,
                                        "code": "X08.01.01001",
                                        "description": "十二、人车区域管控",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01001",
                                        "required": "是",
                                        "location": "十二、人车区域管控",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 40,
                                        "code": "X08.01.01664",
                                        "description": "十三、人员智能点名",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01664",
                                        "required": "是",
                                        "location": "十三、人员智能点名",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 41,
                                        "code": "X08.01.01665",
                                        "description": "十六、应急指挥系统",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01665",
                                        "required": "是",
                                        "location": "十六、应急指挥系统",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 42,
                                        "code": "X08.01.02011",
                                        "description": "十七、中心-配套服务器",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02011",
                                        "required": "是",
                                        "location": "十七、中心-配套服务器",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 43,
                                        "code": "X08.01.02012",
                                        "description": "十八、中心-校时服务器",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02012",
                                        "required": "是",
                                        "location": "十八、中心-校时服务器",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 44,
                                        "code": "X08.01.02013",
                                        "description": "十九、中心-存储设备（视频云）",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02013",
                                        "required": "是",
                                        "location": "十九、中心-存储设备（视频云）",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 45,
                                        "code": "X08.01.02014",
                                        "description": "二十、中心-国标联网网关",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02014",
                                        "required": "是",
                                        "location": "二十、中心-国标联网网关",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 46,
                                        "code": "X08.01.02015",
                                        "description": "二十一、中心-报警门禁对讲接入网关",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02015",
                                        "required": "是",
                                        "location": "二十一、中心-报警门禁对讲接入网关",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 47,
                                        "code": "X08.01.02016",
                                        "description": "二十二、中心-P9500软件模块",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02016",
                                        "required": "是",
                                        "location": "二十二、中心-P9500软件模块",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 48,
                                        "code": "X08.01.02017",
                                        "description": "二十三、中心-运维软件平台",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02017",
                                        "required": "是",
                                        "location": "二十三、中心-运维软件平台",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 49,
                                        "code": "X08.01.02018",
                                        "description": "二十四、中心-视频质量诊断",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02018",
                                        "required": "是",
                                        "location": "二十四、中心-视频质量诊断",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 50,
                                        "code": "X08.01.01950",
                                        "description": "二十五、数通-交换机",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01950",
                                        "required": "是",
                                        "location": "二十五、数通-交换机",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                },
                                {
                                        "sequence": 51,
                                        "code": "X08.01.01955",
                                        "description": "二十六、数通-安全产品",
                                        "level": "2",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01955",
                                        "required": "是",
                                        "location": "二十六、数通-安全产品",
                                        "remark": "",
                                        "parent": "X08.01.01002"
                                }
                        ],
                        "3": [
                                {
                                        "sequence": 1,
                                        "code": "X08.01.00843",
                                        "description": "监室",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00843",
                                        "required": "是",
                                        "location": "监室",
                                        "remark": "",
                                        "parent": "X08.01.00819"
                                },
                                {
                                        "sequence": 2,
                                        "code": "X08.01.00844",
                                        "description": "周界",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00844",
                                        "required": "是",
                                        "location": "周界",
                                        "remark": "",
                                        "parent": "X08.01.00819"
                                },
                                {
                                        "sequence": 3,
                                        "code": "X08.01.00845",
                                        "description": "AB门",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00845",
                                        "required": "是",
                                        "location": "AB门",
                                        "remark": "",
                                        "parent": "X08.01.00819"
                                },
                                {
                                        "sequence": 4,
                                        "code": "X08.01.00846",
                                        "description": "制高点AR云景",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00846",
                                        "required": "是",
                                        "location": "制高点AR云景",
                                        "remark": "",
                                        "parent": "X08.01.00820"
                                },
                                {
                                        "sequence": 5,
                                        "code": "X08.01.00847",
                                        "description": "厂房",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00847",
                                        "required": "是",
                                        "location": "厂房",
                                        "remark": "",
                                        "parent": "X08.01.00820"
                                },
                                {
                                        "sequence": 6,
                                        "code": "X08.01.00848",
                                        "description": "监外公共区域",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00848",
                                        "required": "是",
                                        "location": "监外公共区域",
                                        "remark": "",
                                        "parent": "X08.01.00820"
                                },
                                {
                                        "sequence": 7,
                                        "code": "X08.01.00849",
                                        "description": "会见区域",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00849",
                                        "required": "是",
                                        "location": "会见区域",
                                        "remark": "",
                                        "parent": "X08.01.00821"
                                },
                                {
                                        "sequence": 8,
                                        "code": "X08.01.00850",
                                        "description": "伙房",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00850",
                                        "required": "是",
                                        "location": "伙房",
                                        "remark": "",
                                        "parent": "X08.01.00821"
                                },
                                {
                                        "sequence": 9,
                                        "code": "X08.01.02060",
                                        "description": "指挥中心/分控中心",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02060",
                                        "required": "是",
                                        "location": "指挥中心/分控中心",
                                        "remark": "",
                                        "parent": "X08.01.00821"
                                },
                                {
                                        "sequence": 10,
                                        "code": "X08.01.00854",
                                        "description": "事件监测智能分析",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00854",
                                        "required": "是",
                                        "location": "事件监测智能分析",
                                        "remark": "",
                                        "parent": "X08.01.00822"
                                },
                                {
                                        "sequence": 11,
                                        "code": "X08.01.00855",
                                        "description": "门锁控制",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00855",
                                        "required": "是",
                                        "location": "门锁控制",
                                        "remark": "",
                                        "parent": "X08.01.00822"
                                },
                                {
                                        "sequence": 12,
                                        "code": "X08.01.00856",
                                        "description": "公共设备",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00856",
                                        "required": "是",
                                        "location": "公共设备",
                                        "remark": "",
                                        "parent": "X08.01.00822"
                                },
                                {
                                        "sequence": 13,
                                        "code": "X08.01.00857",
                                        "description": "人脸巡更",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00857",
                                        "required": "是",
                                        "location": "人脸巡更",
                                        "remark": "",
                                        "parent": "X08.01.00823"
                                },
                                {
                                        "sequence": 14,
                                        "code": "X08.01.00858",
                                        "description": "传统门禁巡更",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00858",
                                        "required": "是",
                                        "location": "传统门禁巡更",
                                        "remark": "",
                                        "parent": "X08.01.00823"
                                },
                                {
                                        "sequence": 15,
                                        "code": "X08.01.00859",
                                        "description": "紧急报警",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00859",
                                        "required": "是",
                                        "location": "紧急报警",
                                        "remark": "",
                                        "parent": "X08.01.00823"
                                },
                                {
                                        "sequence": 16,
                                        "code": "X08.01.00860",
                                        "description": "电子围栏",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00860",
                                        "required": "是",
                                        "location": "电子围栏",
                                        "remark": "",
                                        "parent": "X08.01.00824"
                                },
                                {
                                        "sequence": 17,
                                        "code": "X08.01.00861",
                                        "description": "红外对射",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00861",
                                        "required": "是",
                                        "location": "红外对射",
                                        "remark": "",
                                        "parent": "X08.01.00824"
                                },
                                {
                                        "sequence": 18,
                                        "code": "X08.01.01909",
                                        "description": "对讲终端",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01909",
                                        "required": "是",
                                        "location": "对讲终端",
                                        "remark": "",
                                        "parent": "X08.01.00824"
                                },
                                {
                                        "sequence": 19,
                                        "code": "X08.01.01921",
                                        "description": "监仓内屏（10.2）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01921",
                                        "required": "是",
                                        "location": "监仓内屏（10.2）",
                                        "remark": "",
                                        "parent": "X08.01.01908"
                                },
                                {
                                        "sequence": 20,
                                        "code": "X08.01.01922",
                                        "description": "监仓内屏（15.6）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01922",
                                        "required": "是",
                                        "location": "监仓内屏（15.6）",
                                        "remark": "",
                                        "parent": "X08.01.01908"
                                },
                                {
                                        "sequence": 21,
                                        "code": "X08.01.01923",
                                        "description": "监仓外屏",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01923",
                                        "required": "是",
                                        "location": "监仓外屏",
                                        "remark": "",
                                        "parent": "X08.01.01908"
                                },
                                {
                                        "sequence": 22,
                                        "code": "X08.01.01911",
                                        "description": "管理机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01911",
                                        "required": "是",
                                        "location": "管理机",
                                        "remark": "",
                                        "parent": "X08.01.02082"
                                },
                                {
                                        "sequence": 23,
                                        "code": "X08.01.01912",
                                        "description": "管理平台",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01912",
                                        "required": "是",
                                        "location": "管理平台",
                                        "remark": "",
                                        "parent": "X08.01.02082"
                                },
                                {
                                        "sequence": 24,
                                        "code": "X08.01.02083",
                                        "description": "指挥中心机房/分控中心",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02083",
                                        "required": "是",
                                        "location": "指挥中心机房/分控中心",
                                        "remark": "",
                                        "parent": "X08.01.02082"
                                },
                                {
                                        "sequence": 25,
                                        "code": "X08.01.02084",
                                        "description": "监室方案一（IP功放+模拟音箱）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02084",
                                        "required": "是",
                                        "location": "监室方案一（IP功放+模拟音箱）",
                                        "remark": "",
                                        "parent": "X08.01.00827"
                                },
                                {
                                        "sequence": 26,
                                        "code": "X08.01.02085",
                                        "description": "监室方案二（纯网络IP音箱，可单点控制）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02085",
                                        "required": "是",
                                        "location": "监室方案二（纯网络IP音箱，可单点控制）",
                                        "remark": "",
                                        "parent": "X08.01.00827"
                                },
                                {
                                        "sequence": 27,
                                        "code": "X08.01.02086",
                                        "description": "监室外室内放风场",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02086",
                                        "required": "是",
                                        "location": "监室外室内放风场",
                                        "remark": "",
                                        "parent": "X08.01.00827"
                                },
                                {
                                        "sequence": 28,
                                        "code": "X08.01.02087",
                                        "description": "室外放风场/劳改工厂",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02087",
                                        "required": "是",
                                        "location": "室外放风场/劳改工厂",
                                        "remark": "",
                                        "parent": "X08.01.00828"
                                },
                                {
                                        "sequence": 29,
                                        "code": "X08.01.02088",
                                        "description": "综合行政楼",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02088",
                                        "required": "是",
                                        "location": "综合行政楼",
                                        "remark": "",
                                        "parent": "X08.01.00828"
                                },
                                {
                                        "sequence": 30,
                                        "code": "X08.01.02089",
                                        "description": "食堂/伙房",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02089",
                                        "required": "是",
                                        "location": "食堂/伙房",
                                        "remark": "",
                                        "parent": "X08.01.00828"
                                },
                                {
                                        "sequence": 31,
                                        "code": "X08.01.02090",
                                        "description": "周界",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02090",
                                        "required": "是",
                                        "location": "周界",
                                        "remark": "",
                                        "parent": "X08.01.00829"
                                },
                                {
                                        "sequence": 32,
                                        "code": "X08.01.02091",
                                        "description": "室外草坪",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02091",
                                        "required": "是",
                                        "location": "室外草坪",
                                        "remark": "",
                                        "parent": "X08.01.00829"
                                },
                                {
                                        "sequence": 33,
                                        "code": "X08.01.00877",
                                        "description": "相机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00877",
                                        "required": "是",
                                        "location": "相机",
                                        "remark": "",
                                        "parent": "X08.01.00829"
                                },
                                {
                                        "sequence": 34,
                                        "code": "X08.01.00879",
                                        "description": "拾音器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00879",
                                        "required": "是",
                                        "location": "拾音器",
                                        "remark": "",
                                        "parent": "X08.01.00830"
                                },
                                {
                                        "sequence": 35,
                                        "code": "X08.01.00880",
                                        "description": "温湿度",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00880",
                                        "required": "是",
                                        "location": "温湿度",
                                        "remark": "",
                                        "parent": "X08.01.00830"
                                },
                                {
                                        "sequence": 36,
                                        "code": "X08.01.00881",
                                        "description": "审讯机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00881",
                                        "required": "是",
                                        "location": "审讯机",
                                        "remark": "",
                                        "parent": "X08.01.00830"
                                },
                                {
                                        "sequence": 37,
                                        "code": "X08.01.00882",
                                        "description": "示证",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00882",
                                        "required": "是",
                                        "location": "示证",
                                        "remark": "",
                                        "parent": "X08.01.00831"
                                },
                                {
                                        "sequence": 38,
                                        "code": "X08.01.00883",
                                        "description": "报警",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00883",
                                        "required": "是",
                                        "location": "报警",
                                        "remark": "",
                                        "parent": "X08.01.00831"
                                },
                                {
                                        "sequence": 39,
                                        "code": "X08.01.00884",
                                        "description": "平台",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00884",
                                        "required": "是",
                                        "location": "平台",
                                        "remark": "",
                                        "parent": "X08.01.00831"
                                },
                                {
                                        "sequence": 40,
                                        "code": "X08.01.00885",
                                        "description": "情绪识别",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00885",
                                        "required": "是",
                                        "location": "情绪识别",
                                        "remark": "",
                                        "parent": "X08.01.00833"
                                },
                                {
                                        "sequence": 41,
                                        "code": "X08.01.00865",
                                        "description": "安检系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00865",
                                        "required": "是",
                                        "location": "安检系统",
                                        "remark": "",
                                        "parent": "X08.01.00833"
                                },
                                {
                                        "sequence": 42,
                                        "code": "X08.01.00886",
                                        "description": "登记审批办证",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00886",
                                        "required": "是",
                                        "location": "登记审批办证",
                                        "remark": "",
                                        "parent": "X08.01.00833"
                                },
                                {
                                        "sequence": 43,
                                        "code": "X08.01.00887",
                                        "description": "辊闸门",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00887",
                                        "required": "是",
                                        "location": "辊闸门",
                                        "remark": "",
                                        "parent": "X08.01.00834"
                                },
                                {
                                        "sequence": 44,
                                        "code": "X08.01.00888",
                                        "description": "人行通道AB门及虚拟换证（项目定制虚拟换证逻辑，如四门控制器控制ABCD四门，B门接换卡进出读卡器，换卡进刷了后，删除A门权限，下发CD门门禁权限。换卡出刷了后，删除CD门权限，下发A门权限。（南京女子监狱项目已定制）实际根据ACD门分布调整接线即可。若AB门超过三个门，则需要八门控制器重新定制。）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00888",
                                        "required": "是",
                                        "location": "人行通道AB门及虚拟换证（项目定制虚拟换证逻辑，如四门控制器控制ABCD四门，B门接换卡进出读卡器，换卡进刷了后，删除A门权限，下发CD门门禁权限。换卡出刷了后，删除CD门权限，下发A门权限。（南京女子监狱项目已定制）实际根据ACD门分布调整接线即可。若AB门超过三个门，则需要八门控制器重新定制。）",
                                        "remark": "",
                                        "parent": "X08.01.00834"
                                },
                                {
                                        "sequence": 45,
                                        "code": "X08.01.00889",
                                        "description": "其他门禁设备",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00889",
                                        "required": "是",
                                        "location": "其他门禁设备",
                                        "remark": "",
                                        "parent": "X08.01.00834"
                                },
                                {
                                        "sequence": 46,
                                        "code": "X08.01.00890",
                                        "description": "车牌识别及车底扫描成像系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00890",
                                        "required": "是",
                                        "location": "车牌识别及车底扫描成像系统",
                                        "remark": "",
                                        "parent": "X08.01.00835"
                                },
                                {
                                        "sequence": 47,
                                        "code": "X08.01.00891",
                                        "description": "升降柱",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00891",
                                        "required": "是",
                                        "location": "升降柱",
                                        "remark": "",
                                        "parent": "X08.01.00835"
                                },
                                {
                                        "sequence": 48,
                                        "code": "X08.01.00892",
                                        "description": "AB门人行值班室",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00892",
                                        "required": "是",
                                        "location": "AB门人行值班室",
                                        "remark": "",
                                        "parent": "X08.01.00835"
                                },
                                {
                                        "sequence": 49,
                                        "code": "X08.01.00893",
                                        "description": "车辆进出管理系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00893",
                                        "required": "是",
                                        "location": "车辆进出管理系统",
                                        "remark": "",
                                        "parent": "X08.01.00836"
                                },
                                {
                                        "sequence": 50,
                                        "code": "X08.01.00894",
                                        "description": "罪犯出监防误放",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00894",
                                        "required": "是",
                                        "location": "罪犯出监防误放",
                                        "remark": "",
                                        "parent": "X08.01.00836"
                                },
                                {
                                        "sequence": 51,
                                        "code": "X08.01.00895",
                                        "description": "罪犯防脱逃预警系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00895",
                                        "required": "是",
                                        "location": "罪犯防脱逃预警系统",
                                        "remark": "",
                                        "parent": "X08.01.00836"
                                },
                                {
                                        "sequence": 52,
                                        "code": "X08.01.02042",
                                        "description": "车辆生命探测系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02042",
                                        "required": "是",
                                        "location": "车辆生命探测系统",
                                        "remark": "",
                                        "parent": "X08.01.00837"
                                },
                                {
                                        "sequence": 53,
                                        "code": "X08.01.00898",
                                        "description": "人车区域管控",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00898",
                                        "required": "是",
                                        "location": "人车区域管控",
                                        "remark": "",
                                        "parent": "X08.01.00837"
                                },
                                {
                                        "sequence": 54,
                                        "code": "X08.01.00899",
                                        "description": "监舍点名(三选一)",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00899",
                                        "required": "是",
                                        "location": "监舍点名(三选一)",
                                        "remark": "",
                                        "parent": "X08.01.00837"
                                },
                                {
                                        "sequence": 55,
                                        "code": "X08.01.00900",
                                        "description": "工间点名",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00900",
                                        "required": "是",
                                        "location": "工间点名",
                                        "remark": "",
                                        "parent": "X08.01.00838"
                                },
                                {
                                        "sequence": 56,
                                        "code": "X08.01.00901",
                                        "description": "出收工点名（二选一）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00901",
                                        "required": "是",
                                        "location": "出收工点名（二选一）",
                                        "remark": "",
                                        "parent": "X08.01.00838"
                                },
                                {
                                        "sequence": 57,
                                        "code": "X08.01.00902",
                                        "description": "零星流动",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00902",
                                        "required": "是",
                                        "location": "零星流动",
                                        "remark": "",
                                        "parent": "X08.01.00838"
                                },
                                {
                                        "sequence": 58,
                                        "code": "X08.01.00904",
                                        "description": "管控终端 （服药及夜巡APP载体）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00904",
                                        "required": "是",
                                        "location": "管控终端 （服药及夜巡APP载体）",
                                        "remark": "",
                                        "parent": "X08.01.00839"
                                },
                                {
                                        "sequence": 59,
                                        "code": "X08.01.00906",
                                        "description": "罪犯夜巡巡查点",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00906",
                                        "required": "是",
                                        "location": "罪犯夜巡巡查点",
                                        "remark": "",
                                        "parent": "X08.01.00839"
                                },
                                {
                                        "sequence": 60,
                                        "code": "X08.01.00907",
                                        "description": "后端设备",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00907",
                                        "required": "是",
                                        "location": "后端设备",
                                        "remark": "",
                                        "parent": "X08.01.00839"
                                },
                                {
                                        "sequence": 61,
                                        "code": "X08.01.00908",
                                        "description": "融合通信平台软件X9000相关模块",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00908",
                                        "required": "是",
                                        "location": "融合通信平台软件X9000相关模块",
                                        "remark": "",
                                        "parent": "X08.01.00840"
                                },
                                {
                                        "sequence": 62,
                                        "code": "X08.01.02051",
                                        "description": "X9000服务器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02051",
                                        "required": "是",
                                        "location": "X9000服务器",
                                        "remark": "",
                                        "parent": "X08.01.00840"
                                },
                                {
                                        "sequence": 63,
                                        "code": "X08.01.00909",
                                        "description": "MCU",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00909",
                                        "required": "是",
                                        "location": "MCU",
                                        "remark": "",
                                        "parent": "X08.01.00840"
                                },
                                {
                                        "sequence": 64,
                                        "code": "X08.01.00910",
                                        "description": "高性能融合通信网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00910",
                                        "required": "是",
                                        "location": "高性能融合通信网关",
                                        "remark": "",
                                        "parent": "X08.01.00841"
                                },
                                {
                                        "sequence": 65,
                                        "code": "X08.01.00911",
                                        "description": "经济型融合通信网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00911",
                                        "required": "是",
                                        "location": "经济型融合通信网关",
                                        "remark": "",
                                        "parent": "X08.01.00841"
                                },
                                {
                                        "sequence": 66,
                                        "code": "X08.01.00912",
                                        "description": "集群音频接入网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00912",
                                        "required": "是",
                                        "location": "集群音频接入网关",
                                        "remark": "",
                                        "parent": "X08.01.00841"
                                },
                                {
                                        "sequence": 67,
                                        "code": "X08.01.00913",
                                        "description": "调度台",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00913",
                                        "required": "是",
                                        "location": "调度台",
                                        "remark": "",
                                        "parent": "X08.01.00842"
                                },
                                {
                                        "sequence": 68,
                                        "code": "X08.01.00914",
                                        "description": "普通机架式服务器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00914",
                                        "required": "是",
                                        "location": "普通机架式服务器",
                                        "remark": "",
                                        "parent": "X08.01.00842"
                                },
                                {
                                        "sequence": 69,
                                        "code": "X08.01.00915",
                                        "description": "超融合私有云",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00915",
                                        "required": "是",
                                        "location": "超融合私有云",
                                        "remark": "",
                                        "parent": "X08.01.00842"
                                },
                                {
                                        "sequence": 70,
                                        "code": "X08.01.00916",
                                        "description": "服务器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00916",
                                        "required": "是",
                                        "location": "服务器",
                                        "remark": "",
                                        "parent": "X08.01.01666"
                                },
                                {
                                        "sequence": 71,
                                        "code": "X08.01.00917",
                                        "description": "云存储",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00917",
                                        "required": "是",
                                        "location": "云存储",
                                        "remark": "",
                                        "parent": "X08.01.01666"
                                },
                                {
                                        "sequence": 72,
                                        "code": "X08.01.00918",
                                        "description": "EVS集中存储",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00918",
                                        "required": "是",
                                        "location": "EVS集中存储",
                                        "remark": "",
                                        "parent": "X08.01.01666"
                                },
                                {
                                        "sequence": 73,
                                        "code": "X08.01.00919",
                                        "description": "国标联网网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00919",
                                        "required": "是",
                                        "location": "国标联网网关",
                                        "remark": "",
                                        "parent": "X08.01.01932"
                                },
                                {
                                        "sequence": 74,
                                        "code": "X08.01.00920",
                                        "description": "非视频设备接入网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00920",
                                        "required": "是",
                                        "location": "非视频设备接入网关",
                                        "remark": "",
                                        "parent": "X08.01.01932"
                                },
                                {
                                        "sequence": 75,
                                        "code": "X08.01.00921",
                                        "description": "业务数据对接网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00921",
                                        "required": "是",
                                        "location": "业务数据对接网关",
                                        "remark": "",
                                        "parent": "X08.01.01932"
                                },
                                {
                                        "sequence": 76,
                                        "code": "X08.01.00922",
                                        "description": "定制物料",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00922",
                                        "required": "是",
                                        "location": "定制物料",
                                        "remark": "",
                                        "parent": "X08.01.01934"
                                },
                                {
                                        "sequence": 77,
                                        "code": "X08.01.00923",
                                        "description": "安防管理",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00923",
                                        "required": "是",
                                        "location": "安防管理",
                                        "remark": "",
                                        "parent": "X08.01.01934"
                                },
                                {
                                        "sequence": 78,
                                        "code": "X08.01.00924",
                                        "description": "智能管控",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00924",
                                        "required": "是",
                                        "location": "智能管控",
                                        "remark": "",
                                        "parent": "X08.01.01934"
                                },
                                {
                                        "sequence": 79,
                                        "code": "X08.01.00925",
                                        "description": "应急指挥",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00925",
                                        "required": "是",
                                        "location": "应急指挥",
                                        "remark": "",
                                        "parent": "X08.01.01939"
                                },
                                {
                                        "sequence": 80,
                                        "code": "X08.01.01661",
                                        "description": "三维",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01661",
                                        "required": "是",
                                        "location": "三维",
                                        "remark": "",
                                        "parent": "X08.01.01939"
                                },
                                {
                                        "sequence": 81,
                                        "code": "X08.01.01989",
                                        "description": "其他",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01989",
                                        "required": "是",
                                        "location": "其他",
                                        "remark": "",
                                        "parent": "X08.01.01939"
                                },
                                {
                                        "sequence": 82,
                                        "code": "X08.01.00927",
                                        "description": "纯软模块化",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00927",
                                        "required": "是",
                                        "location": "纯软模块化",
                                        "remark": "",
                                        "parent": "X08.01.00991"
                                },
                                {
                                        "sequence": 83,
                                        "code": "X08.01.00928",
                                        "description": "纯软平台",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00928",
                                        "required": "是",
                                        "location": "纯软平台",
                                        "remark": "",
                                        "parent": "X08.01.00991"
                                },
                                {
                                        "sequence": 84,
                                        "code": "X08.01.00929",
                                        "description": "服务器纯硬件",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00929",
                                        "required": "是",
                                        "location": "服务器纯硬件",
                                        "remark": "",
                                        "parent": "X08.01.00991"
                                },
                                {
                                        "sequence": 85,
                                        "code": "X08.01.00930",
                                        "description": "软硬一体",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00930",
                                        "required": "是",
                                        "location": "软硬一体",
                                        "remark": "",
                                        "parent": "X08.01.00990"
                                },
                                {
                                        "sequence": 86,
                                        "code": "X08.01.00931",
                                        "description": "纯软",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00931",
                                        "required": "是",
                                        "location": "纯软",
                                        "remark": "",
                                        "parent": "X08.01.00990"
                                },
                                {
                                        "sequence": 87,
                                        "code": "X08.01.00932",
                                        "description": "纯硬件",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00932",
                                        "required": "是",
                                        "location": "纯硬件",
                                        "remark": "",
                                        "parent": "X08.01.00990"
                                },
                                {
                                        "sequence": 88,
                                        "code": "X08.01.01913",
                                        "description": "边缘计算",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01913",
                                        "required": "是",
                                        "location": "边缘计算",
                                        "remark": "",
                                        "parent": "X08.01.00992"
                                },
                                {
                                        "sequence": 89,
                                        "code": "X08.01.01915",
                                        "description": "集中存储",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01915",
                                        "required": "是",
                                        "location": "集中存储",
                                        "remark": "",
                                        "parent": "X08.01.00992"
                                },
                                {
                                        "sequence": 90,
                                        "code": "X08.01.01667",
                                        "description": "服务器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01667",
                                        "required": "是",
                                        "location": "服务器",
                                        "remark": "",
                                        "parent": "X08.01.00992"
                                },
                                {
                                        "sequence": 91,
                                        "code": "X08.01.01668",
                                        "description": "操作系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01668",
                                        "required": "是",
                                        "location": "操作系统",
                                        "remark": "",
                                        "parent": "X08.01.00993"
                                },
                                {
                                        "sequence": 92,
                                        "code": "X08.01.01669",
                                        "description": "国产化PC主机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01669",
                                        "required": "是",
                                        "location": "国产化PC主机",
                                        "remark": "",
                                        "parent": "X08.01.00993"
                                },
                                {
                                        "sequence": 93,
                                        "code": "X08.01.01670",
                                        "description": "桌面操作系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01670",
                                        "required": "是",
                                        "location": "桌面操作系统",
                                        "remark": "",
                                        "parent": "X08.01.00993"
                                },
                                {
                                        "sequence": 94,
                                        "code": "X08.01.01672",
                                        "description": "人脸门禁指纹发卡器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01672",
                                        "required": "是",
                                        "location": "人脸门禁指纹发卡器",
                                        "remark": "",
                                        "parent": "X08.01.00994"
                                },
                                {
                                        "sequence": 95,
                                        "code": "X08.01.01933",
                                        "description": "无人机机库",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01933",
                                        "required": "是",
                                        "location": "无人机机库",
                                        "remark": "",
                                        "parent": "X08.01.00994"
                                },
                                {
                                        "sequence": 96,
                                        "code": "X08.01.01935",
                                        "description": "核心交换机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01935",
                                        "required": "是",
                                        "location": "核心交换机",
                                        "remark": "",
                                        "parent": "X08.01.00994"
                                },
                                {
                                        "sequence": 97,
                                        "code": "X08.01.01936",
                                        "description": "汇聚交换机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01936",
                                        "required": "是",
                                        "location": "汇聚交换机",
                                        "remark": "",
                                        "parent": "X08.01.00995"
                                },
                                {
                                        "sequence": 98,
                                        "code": "X08.01.01937",
                                        "description": "接入交换机-室内",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01937",
                                        "required": "是",
                                        "location": "接入交换机-室内",
                                        "remark": "",
                                        "parent": "X08.01.00995"
                                },
                                {
                                        "sequence": 99,
                                        "code": "X08.01.01938",
                                        "description": "接入交换机-室外",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01938",
                                        "required": "是",
                                        "location": "接入交换机-室外",
                                        "remark": "",
                                        "parent": "X08.01.00995"
                                },
                                {
                                        "sequence": 100,
                                        "code": "X08.01.01941",
                                        "description": "下一代防火墙",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01941",
                                        "required": "是",
                                        "location": "下一代防火墙",
                                        "remark": "",
                                        "parent": "X08.01.01916"
                                },
                                {
                                        "sequence": 101,
                                        "code": "X08.01.01942",
                                        "description": "EDR杀毒软件",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01942",
                                        "required": "是",
                                        "location": "EDR杀毒软件",
                                        "remark": "",
                                        "parent": "X08.01.01916"
                                },
                                {
                                        "sequence": 102,
                                        "code": "X08.01.01943",
                                        "description": "日志审计",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01943",
                                        "required": "是",
                                        "location": "日志审计",
                                        "remark": "",
                                        "parent": "X08.01.01916"
                                },
                                {
                                        "sequence": 103,
                                        "code": "X08.01.01945",
                                        "description": "运维审计",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01945",
                                        "required": "是",
                                        "location": "运维审计",
                                        "remark": "",
                                        "parent": "X08.01.02072"
                                },
                                {
                                        "sequence": 104,
                                        "code": "X08.01.01947",
                                        "description": "漏洞扫描系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01947",
                                        "required": "是",
                                        "location": "漏洞扫描系统",
                                        "remark": "",
                                        "parent": "X08.01.02072"
                                },
                                {
                                        "sequence": 105,
                                        "code": "X08.01.01949",
                                        "description": "WEB应用防火墙",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01949",
                                        "required": "是",
                                        "location": "WEB应用防火墙",
                                        "remark": "",
                                        "parent": "X08.01.02072"
                                },
                                {
                                        "sequence": 106,
                                        "code": "X08.01.00936",
                                        "description": "监室",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00936",
                                        "required": "是",
                                        "location": "监室",
                                        "remark": "",
                                        "parent": "X08.01.00998"
                                },
                                {
                                        "sequence": 107,
                                        "code": "X08.01.00937",
                                        "description": "周界",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00937",
                                        "required": "是",
                                        "location": "周界",
                                        "remark": "",
                                        "parent": "X08.01.00998"
                                },
                                {
                                        "sequence": 108,
                                        "code": "X08.01.00938",
                                        "description": "AB门",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00938",
                                        "required": "是",
                                        "location": "AB门",
                                        "remark": "",
                                        "parent": "X08.01.00998"
                                },
                                {
                                        "sequence": 109,
                                        "code": "X08.01.00939",
                                        "description": "厂房",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00939",
                                        "required": "是",
                                        "location": "厂房",
                                        "remark": "",
                                        "parent": "X08.01.00999"
                                },
                                {
                                        "sequence": 110,
                                        "code": "X08.01.00940",
                                        "description": "监外公共区域",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00940",
                                        "required": "是",
                                        "location": "监外公共区域",
                                        "remark": "",
                                        "parent": "X08.01.00999"
                                },
                                {
                                        "sequence": 111,
                                        "code": "X08.01.00941",
                                        "description": "会见区域",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00941",
                                        "required": "是",
                                        "location": "会见区域",
                                        "remark": "",
                                        "parent": "X08.01.00999"
                                },
                                {
                                        "sequence": 112,
                                        "code": "X08.01.00942",
                                        "description": "伙房",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00942",
                                        "required": "是",
                                        "location": "伙房",
                                        "remark": "",
                                        "parent": "X08.01.01000"
                                },
                                {
                                        "sequence": 113,
                                        "code": "X08.01.02061",
                                        "description": "指挥中心/分控中心",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02061",
                                        "required": "是",
                                        "location": "指挥中心/分控中心",
                                        "remark": "",
                                        "parent": "X08.01.01000"
                                },
                                {
                                        "sequence": 114,
                                        "code": "X08.01.00946",
                                        "description": "事件监测智能分析",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00946",
                                        "required": "是",
                                        "location": "事件监测智能分析",
                                        "remark": "",
                                        "parent": "X08.01.01000"
                                },
                                {
                                        "sequence": 115,
                                        "code": "X08.01.00947",
                                        "description": "门锁控制",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00947",
                                        "required": "是",
                                        "location": "门锁控制",
                                        "remark": "",
                                        "parent": "X08.01.01001"
                                },
                                {
                                        "sequence": 116,
                                        "code": "X08.01.00948",
                                        "description": "公共设备",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00948",
                                        "required": "是",
                                        "location": "公共设备",
                                        "remark": "",
                                        "parent": "X08.01.01001"
                                },
                                {
                                        "sequence": 117,
                                        "code": "X08.01.00949",
                                        "description": "人脸巡更",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00949",
                                        "required": "是",
                                        "location": "人脸巡更",
                                        "remark": "",
                                        "parent": "X08.01.01001"
                                },
                                {
                                        "sequence": 118,
                                        "code": "X08.01.00950",
                                        "description": "传统门禁巡更",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00950",
                                        "required": "是",
                                        "location": "传统门禁巡更",
                                        "remark": "",
                                        "parent": "X08.01.01664"
                                },
                                {
                                        "sequence": 119,
                                        "code": "X08.01.00951",
                                        "description": "紧急报警",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00951",
                                        "required": "是",
                                        "location": "紧急报警",
                                        "remark": "",
                                        "parent": "X08.01.01664"
                                },
                                {
                                        "sequence": 120,
                                        "code": "X08.01.00952",
                                        "description": "电子围栏",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00952",
                                        "required": "是",
                                        "location": "电子围栏",
                                        "remark": "",
                                        "parent": "X08.01.01664"
                                },
                                {
                                        "sequence": 121,
                                        "code": "X08.01.00953",
                                        "description": "红外对射",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00953",
                                        "required": "是",
                                        "location": "红外对射",
                                        "remark": "",
                                        "parent": "X08.01.01665"
                                },
                                {
                                        "sequence": 122,
                                        "code": "X08.01.02039",
                                        "description": "对讲终端",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02039",
                                        "required": "是",
                                        "location": "对讲终端",
                                        "remark": "",
                                        "parent": "X08.01.01665"
                                },
                                {
                                        "sequence": 123,
                                        "code": "X08.01.01919",
                                        "description": "监仓内屏（10.2）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01919",
                                        "required": "是",
                                        "location": "监仓内屏（10.2）",
                                        "remark": "",
                                        "parent": "X08.01.01665"
                                },
                                {
                                        "sequence": 124,
                                        "code": "X08.01.01920",
                                        "description": "监仓内屏（15.6）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01920",
                                        "required": "是",
                                        "location": "监仓内屏（15.6）",
                                        "remark": "",
                                        "parent": "X08.01.02011"
                                },
                                {
                                        "sequence": 125,
                                        "code": "X08.01.02040",
                                        "description": "管理机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02040",
                                        "required": "是",
                                        "location": "管理机",
                                        "remark": "",
                                        "parent": "X08.01.02011"
                                },
                                {
                                        "sequence": 126,
                                        "code": "X08.01.02041",
                                        "description": "管理平台",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02041",
                                        "required": "是",
                                        "location": "管理平台",
                                        "remark": "",
                                        "parent": "X08.01.02011"
                                },
                                {
                                        "sequence": 127,
                                        "code": "X08.01.02073",
                                        "description": "指挥中心机房/分控中心",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02073",
                                        "required": "是",
                                        "location": "指挥中心机房/分控中心",
                                        "remark": "",
                                        "parent": "X08.01.02012"
                                },
                                {
                                        "sequence": 128,
                                        "code": "X08.01.02074",
                                        "description": "监室方案一（IP功放+模拟音箱）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02074",
                                        "required": "是",
                                        "location": "监室方案一（IP功放+模拟音箱）",
                                        "remark": "",
                                        "parent": "X08.01.02012"
                                },
                                {
                                        "sequence": 129,
                                        "code": "X08.01.02075",
                                        "description": "监室方案二（纯网络IP音箱，可单点控制）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02075",
                                        "required": "是",
                                        "location": "监室方案二（纯网络IP音箱，可单点控制）",
                                        "remark": "",
                                        "parent": "X08.01.02012"
                                },
                                {
                                        "sequence": 130,
                                        "code": "X08.01.02076",
                                        "description": "监室外室内放风场",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02076",
                                        "required": "是",
                                        "location": "监室外室内放风场",
                                        "remark": "",
                                        "parent": "X08.01.02013"
                                },
                                {
                                        "sequence": 131,
                                        "code": "X08.01.02077",
                                        "description": "室外放风场/劳改工厂",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02077",
                                        "required": "是",
                                        "location": "室外放风场/劳改工厂",
                                        "remark": "",
                                        "parent": "X08.01.02013"
                                },
                                {
                                        "sequence": 132,
                                        "code": "X08.01.02078",
                                        "description": "综合行政楼",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02078",
                                        "required": "是",
                                        "location": "综合行政楼",
                                        "remark": "",
                                        "parent": "X08.01.02013"
                                },
                                {
                                        "sequence": 133,
                                        "code": "X08.01.02079",
                                        "description": "食堂/伙房",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02079",
                                        "required": "是",
                                        "location": "食堂/伙房",
                                        "remark": "",
                                        "parent": "X08.01.02014"
                                },
                                {
                                        "sequence": 134,
                                        "code": "X08.01.02080",
                                        "description": "周界",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02080",
                                        "required": "是",
                                        "location": "周界",
                                        "remark": "",
                                        "parent": "X08.01.02014"
                                },
                                {
                                        "sequence": 135,
                                        "code": "X08.01.02081",
                                        "description": "室外草坪",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02081",
                                        "required": "是",
                                        "location": "室外草坪",
                                        "remark": "",
                                        "parent": "X08.01.02014"
                                },
                                {
                                        "sequence": 136,
                                        "code": "X08.01.00969",
                                        "description": "相机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00969",
                                        "required": "是",
                                        "location": "相机",
                                        "remark": "",
                                        "parent": "X08.01.02015"
                                },
                                {
                                        "sequence": 137,
                                        "code": "X08.01.00971",
                                        "description": "拾音器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00971",
                                        "required": "是",
                                        "location": "拾音器",
                                        "remark": "",
                                        "parent": "X08.01.02015"
                                },
                                {
                                        "sequence": 138,
                                        "code": "X08.01.00972",
                                        "description": "温湿度",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00972",
                                        "required": "是",
                                        "location": "温湿度",
                                        "remark": "",
                                        "parent": "X08.01.02015"
                                },
                                {
                                        "sequence": 139,
                                        "code": "X08.01.00973",
                                        "description": "审讯机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00973",
                                        "required": "是",
                                        "location": "审讯机",
                                        "remark": "",
                                        "parent": "X08.01.02016"
                                },
                                {
                                        "sequence": 140,
                                        "code": "X08.01.00974",
                                        "description": "示证",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00974",
                                        "required": "是",
                                        "location": "示证",
                                        "remark": "",
                                        "parent": "X08.01.02016"
                                },
                                {
                                        "sequence": 141,
                                        "code": "X08.01.00975",
                                        "description": "报警",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00975",
                                        "required": "是",
                                        "location": "报警",
                                        "remark": "",
                                        "parent": "X08.01.02016"
                                },
                                {
                                        "sequence": 142,
                                        "code": "X08.01.00976",
                                        "description": "平台",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00976",
                                        "required": "是",
                                        "location": "平台",
                                        "remark": "",
                                        "parent": "X08.01.02017"
                                },
                                {
                                        "sequence": 143,
                                        "code": "X08.01.00977",
                                        "description": "情绪识别",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00977",
                                        "required": "是",
                                        "location": "情绪识别",
                                        "remark": "",
                                        "parent": "X08.01.02017"
                                },
                                {
                                        "sequence": 144,
                                        "code": "X08.01.00978",
                                        "description": "安检系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00978",
                                        "required": "是",
                                        "location": "安检系统",
                                        "remark": "",
                                        "parent": "X08.01.02017"
                                },
                                {
                                        "sequence": 145,
                                        "code": "X08.01.00979",
                                        "description": "登记审批办证",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00979",
                                        "required": "是",
                                        "location": "登记审批办证",
                                        "remark": "",
                                        "parent": "X08.01.02018"
                                },
                                {
                                        "sequence": 146,
                                        "code": "X08.01.00980",
                                        "description": "辊闸门",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00980",
                                        "required": "是",
                                        "location": "辊闸门",
                                        "remark": "",
                                        "parent": "X08.01.02018"
                                },
                                {
                                        "sequence": 147,
                                        "code": "X08.01.00981",
                                        "description": "人行通道AB门及虚拟换证",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00981",
                                        "required": "是",
                                        "location": "人行通道AB门及虚拟换证",
                                        "remark": "",
                                        "parent": "X08.01.02018"
                                },
                                {
                                        "sequence": 148,
                                        "code": "X08.01.00982",
                                        "description": "其他门禁设备",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00982",
                                        "required": "是",
                                        "location": "其他门禁设备",
                                        "remark": "",
                                        "parent": "X08.01.01950"
                                },
                                {
                                        "sequence": 149,
                                        "code": "X08.01.00983",
                                        "description": "车牌识别及车底扫描成像系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00983",
                                        "required": "是",
                                        "location": "车牌识别及车底扫描成像系统",
                                        "remark": "",
                                        "parent": "X08.01.01950"
                                },
                                {
                                        "sequence": 150,
                                        "code": "X08.01.00984",
                                        "description": "升降柱",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00984",
                                        "required": "是",
                                        "location": "升降柱",
                                        "remark": "",
                                        "parent": "X08.01.01950"
                                },
                                {
                                        "sequence": 151,
                                        "code": "X08.01.00985",
                                        "description": "AB门人行值班室",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00985",
                                        "required": "是",
                                        "location": "AB门人行值班室",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 152,
                                        "code": "X08.01.00986",
                                        "description": "车辆进出管理系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00986",
                                        "required": "是",
                                        "location": "车辆进出管理系统",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 153,
                                        "code": "X08.01.00987",
                                        "description": "罪犯出监防误放",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00987",
                                        "required": "是",
                                        "location": "罪犯出监防误放",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 154,
                                        "code": "X08.01.00988",
                                        "description": "罪犯防脱逃预警系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00988",
                                        "required": "是",
                                        "location": "罪犯防脱逃预警系统",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 155,
                                        "code": "X08.01.00989",
                                        "description": "人车区域管控",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00989",
                                        "required": "是",
                                        "location": "人车区域管控",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 156,
                                        "code": "X08.01.01662",
                                        "description": "监舍点名(三选一）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01662",
                                        "required": "是",
                                        "location": "监舍点名(三选一）",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 157,
                                        "code": "X08.01.00900",
                                        "description": "工间点名",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00900",
                                        "required": "是",
                                        "location": "工间点名",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 158,
                                        "code": "X08.01.00901",
                                        "description": "出收工点名（二选一）",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00901",
                                        "required": "是",
                                        "location": "出收工点名（二选一）",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 159,
                                        "code": "X08.01.00902",
                                        "description": "零星流动",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00902",
                                        "required": "是",
                                        "location": "零星流动",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 160,
                                        "code": "X08.01.01663",
                                        "description": "融合通信平台软件X9000相关模块",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01663",
                                        "required": "是",
                                        "location": "融合通信平台软件X9000相关模块",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 161,
                                        "code": "X08.01.02052",
                                        "description": "X9000服务器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02052",
                                        "required": "是",
                                        "location": "X9000服务器",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 162,
                                        "code": "X08.01.00909",
                                        "description": "MCU",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00909",
                                        "required": "是",
                                        "location": "MCU",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 163,
                                        "code": "X08.01.00910",
                                        "description": "高性能融合通信网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00910",
                                        "required": "是",
                                        "location": "高性能融合通信网关",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 164,
                                        "code": "X08.01.00911",
                                        "description": "经济型融合通信网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00911",
                                        "required": "是",
                                        "location": "经济型融合通信网关",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 165,
                                        "code": "X08.01.00912",
                                        "description": "集群音频接入网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00912",
                                        "required": "是",
                                        "location": "集群音频接入网关",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 166,
                                        "code": "X08.01.00913",
                                        "description": "调度台",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.00913",
                                        "required": "是",
                                        "location": "调度台",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 167,
                                        "code": "X08.01.02019",
                                        "description": "普通机架式服务器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02019",
                                        "required": "是",
                                        "location": "普通机架式服务器",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 168,
                                        "code": "X08.01.02020",
                                        "description": "超融合私有云",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02020",
                                        "required": "是",
                                        "location": "超融合私有云",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 169,
                                        "code": "X08.01.02021",
                                        "description": "服务器",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02021",
                                        "required": "是",
                                        "location": "服务器",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 170,
                                        "code": "X08.01.02022",
                                        "description": "云存储",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02022",
                                        "required": "是",
                                        "location": "云存储",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 171,
                                        "code": "X08.01.02023",
                                        "description": "EVS集中存储",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02023",
                                        "required": "是",
                                        "location": "EVS集中存储",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 172,
                                        "code": "X08.01.02024",
                                        "description": "国标联网网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02024",
                                        "required": "是",
                                        "location": "国标联网网关",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 173,
                                        "code": "X08.01.02025",
                                        "description": "非视频设备接入网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02025",
                                        "required": "是",
                                        "location": "非视频设备接入网关",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 174,
                                        "code": "X08.01.02026",
                                        "description": "业务数据对接网关",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02026",
                                        "required": "是",
                                        "location": "业务数据对接网关",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 175,
                                        "code": "X08.01.02027",
                                        "description": "定制物料",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02027",
                                        "required": "是",
                                        "location": "定制物料",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 176,
                                        "code": "X08.01.02028",
                                        "description": "安防管理",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02028",
                                        "required": "是",
                                        "location": "安防管理",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 177,
                                        "code": "X08.01.02029",
                                        "description": "智能管控",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02029",
                                        "required": "是",
                                        "location": "智能管控",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 178,
                                        "code": "X08.01.02030",
                                        "description": "应急指挥",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02030",
                                        "required": "是",
                                        "location": "应急指挥",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 179,
                                        "code": "X08.01.02031",
                                        "description": "纯软模块化",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02031",
                                        "required": "是",
                                        "location": "纯软模块化",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 180,
                                        "code": "X08.01.02032",
                                        "description": "纯软平台",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02032",
                                        "required": "是",
                                        "location": "纯软平台",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 181,
                                        "code": "X08.01.02033",
                                        "description": "服务器纯硬件",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02033",
                                        "required": "是",
                                        "location": "服务器纯硬件",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 182,
                                        "code": "X08.01.02034",
                                        "description": "软硬一体",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02034",
                                        "required": "是",
                                        "location": "软硬一体",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 183,
                                        "code": "X08.01.02035",
                                        "description": "纯软",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02035",
                                        "required": "是",
                                        "location": "纯软",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 184,
                                        "code": "X08.01.02036",
                                        "description": "纯硬件",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02036",
                                        "required": "是",
                                        "location": "纯硬件",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 185,
                                        "code": "X08.01.02037",
                                        "description": "三维",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02037",
                                        "required": "是",
                                        "location": "三维",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 186,
                                        "code": "X08.01.02038",
                                        "description": "其他",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.02038",
                                        "required": "是",
                                        "location": "其他",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 187,
                                        "code": "X08.01.01951",
                                        "description": "核心交换机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01951",
                                        "required": "是",
                                        "location": "核心交换机",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 188,
                                        "code": "X08.01.01952",
                                        "description": "汇聚交换机",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01952",
                                        "required": "是",
                                        "location": "汇聚交换机",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 189,
                                        "code": "X08.01.01953",
                                        "description": "接入交换机-室内",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01953",
                                        "required": "是",
                                        "location": "接入交换机-室内",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 190,
                                        "code": "X08.01.01954",
                                        "description": "接入交换机-室外",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01954",
                                        "required": "是",
                                        "location": "接入交换机-室外",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 191,
                                        "code": "X08.01.01957",
                                        "description": "下一代防火墙",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01957",
                                        "required": "是",
                                        "location": "下一代防火墙",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 192,
                                        "code": "X08.01.01958",
                                        "description": "EDR杀毒软件",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01958",
                                        "required": "是",
                                        "location": "EDR杀毒软件",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 193,
                                        "code": "X08.01.01959",
                                        "description": "日志审计",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01959",
                                        "required": "是",
                                        "location": "日志审计",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 194,
                                        "code": "X08.01.01961",
                                        "description": "运维审计",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01961",
                                        "required": "是",
                                        "location": "运维审计",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 195,
                                        "code": "X08.01.01964",
                                        "description": "漏洞扫描系统",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01964",
                                        "required": "是",
                                        "location": "漏洞扫描系统",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                },
                                {
                                        "sequence": 196,
                                        "code": "X08.01.01966",
                                        "description": "WEB应用防火墙",
                                        "level": "3",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.01.01966",
                                        "required": "是",
                                        "location": "WEB应用防火墙",
                                        "remark": "",
                                        "parent": "X08.01.01955"
                                }
                        ],
                        "4": [
                                {
                                        "sequence": 1,
                                        "code": "X08.02.00079",
                                        "description": "监室单目全景智能半球",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00079",
                                        "required": "是",
                                        "location": "监室单目全景智能半球",
                                        "remark": "",
                                        "parent": "X08.01.00843"
                                },
                                {
                                        "sequence": 2,
                                        "code": "X08.02.00167",
                                        "description": "枪型网络摄像机",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00167",
                                        "required": "是",
                                        "location": "枪型网络摄像机",
                                        "remark": "",
                                        "parent": "X08.01.00844"
                                },
                                {
                                        "sequence": 3,
                                        "code": "X08.02.00168",
                                        "description": "球机",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00168",
                                        "required": "是",
                                        "location": "球机",
                                        "remark": "",
                                        "parent": "X08.01.00845"
                                },
                                {
                                        "sequence": 4,
                                        "code": "X08.02.00162",
                                        "description": "液晶",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00162",
                                        "required": "是",
                                        "location": "液晶",
                                        "remark": "",
                                        "parent": "X08.01.00846"
                                },
                                {
                                        "sequence": 5,
                                        "code": "X08.02.00163",
                                        "description": "LED",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00163",
                                        "required": "是",
                                        "location": "LED",
                                        "remark": "",
                                        "parent": "X08.01.00847"
                                },
                                {
                                        "sequence": 6,
                                        "code": "X08.02.00081",
                                        "description": "事件监测智能分析",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00081",
                                        "required": "是",
                                        "location": "事件监测智能分析",
                                        "remark": "",
                                        "parent": "X08.01.00848"
                                },
                                {
                                        "sequence": 7,
                                        "code": "X08.02.00091",
                                        "description": "门禁",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00091",
                                        "required": "是",
                                        "location": "门禁",
                                        "remark": "",
                                        "parent": "X08.01.00849"
                                },
                                {
                                        "sequence": 8,
                                        "code": "X08.02.00161",
                                        "description": "对讲终端",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00161",
                                        "required": "是",
                                        "location": "对讲终端",
                                        "remark": "",
                                        "parent": "X08.01.00850"
                                },
                                {
                                        "sequence": 9,
                                        "code": "X08.03.00262",
                                        "description": "*********.23240",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00262",
                                        "required": "是",
                                        "location": "*********.23240",
                                        "remark": "",
                                        "parent": "X08.01.02060"
                                },
                                {
                                        "sequence": 10,
                                        "code": "X08.03.00263",
                                        "description": "*********.23275",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00263",
                                        "required": "是",
                                        "location": "*********.23275",
                                        "remark": "",
                                        "parent": "X08.01.00854"
                                },
                                {
                                        "sequence": 11,
                                        "code": "X08.03.00264",
                                        "description": "*********.23276",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00264",
                                        "required": "是",
                                        "location": "*********.23276",
                                        "remark": "",
                                        "parent": "X08.01.00855"
                                },
                                {
                                        "sequence": 12,
                                        "code": "X08.03.00265",
                                        "description": "*********.23250",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00265",
                                        "required": "是",
                                        "location": "*********.23250",
                                        "remark": "",
                                        "parent": "X08.01.00856"
                                },
                                {
                                        "sequence": 13,
                                        "code": "X08.03.00266",
                                        "description": "*********.23268",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00266",
                                        "required": "是",
                                        "location": "*********.23268",
                                        "remark": "",
                                        "parent": "X08.01.00857"
                                },
                                {
                                        "sequence": 14,
                                        "code": "X08.03.00263",
                                        "description": "*********.23275",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00263",
                                        "required": "是",
                                        "location": "*********.23275",
                                        "remark": "",
                                        "parent": "X08.01.00858"
                                },
                                {
                                        "sequence": 15,
                                        "code": "X08.03.00261",
                                        "description": "*********.23242",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00261",
                                        "required": "是",
                                        "location": "*********.23242",
                                        "remark": "",
                                        "parent": "X08.01.00859"
                                },
                                {
                                        "sequence": 16,
                                        "code": "X08.03.00263",
                                        "description": "*********.23275",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00263",
                                        "required": "是",
                                        "location": "*********.23275",
                                        "remark": "",
                                        "parent": "X08.01.00860"
                                },
                                {
                                        "sequence": 17,
                                        "code": "X08.03.00264",
                                        "description": "*********.23276",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00264",
                                        "required": "是",
                                        "location": "*********.23276",
                                        "remark": "",
                                        "parent": "X08.01.00861"
                                },
                                {
                                        "sequence": 18,
                                        "code": "X08.03.00265",
                                        "description": "*********.23250",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00265",
                                        "required": "是",
                                        "location": "*********.23250",
                                        "remark": "",
                                        "parent": "X08.01.01909"
                                },
                                {
                                        "sequence": 19,
                                        "code": "X08.03.00263",
                                        "description": "*********.23275",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00263",
                                        "required": "是",
                                        "location": "*********.23275",
                                        "remark": "",
                                        "parent": "X08.01.01921"
                                },
                                {
                                        "sequence": 20,
                                        "code": "X08.02.00085",
                                        "description": "罪犯出监人脸核验终端",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00085",
                                        "required": "是",
                                        "location": "罪犯出监人脸核验终端",
                                        "remark": "",
                                        "parent": "X08.01.01922"
                                },
                                {
                                        "sequence": 21,
                                        "code": "X08.02.00086",
                                        "description": "摄像机",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00086",
                                        "required": "是",
                                        "location": "摄像机",
                                        "remark": "",
                                        "parent": "X08.01.01923"
                                },
                                {
                                        "sequence": 22,
                                        "code": "X08.02.00087",
                                        "description": "摄像机",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00087",
                                        "required": "是",
                                        "location": "摄像机",
                                        "remark": "",
                                        "parent": "X08.01.01911"
                                },
                                {
                                        "sequence": 23,
                                        "code": "X08.03.00365",
                                        "description": "*********.43293",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00365",
                                        "required": "是",
                                        "location": "*********.43293",
                                        "remark": "",
                                        "parent": "X08.01.01912"
                                },
                                {
                                        "sequence": 24,
                                        "code": "X08.02.00089",
                                        "description": "EVS集中存储",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00089",
                                        "required": "是",
                                        "location": "EVS集中存储",
                                        "remark": "",
                                        "parent": "X08.01.02083"
                                },
                                {
                                        "sequence": 25,
                                        "code": "X08.02.00090",
                                        "description": "非视频设备接入网关",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00090",
                                        "required": "是",
                                        "location": "非视频设备接入网关",
                                        "remark": "",
                                        "parent": "X08.01.02084"
                                },
                                {
                                        "sequence": 26,
                                        "code": "X08.03.00272",
                                        "description": "*********.3026",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00272",
                                        "required": "是",
                                        "location": "*********.3026",
                                        "remark": "",
                                        "parent": "X08.01.02085"
                                },
                                {
                                        "sequence": 27,
                                        "code": "X08.02.00162",
                                        "description": "液晶",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00162",
                                        "required": "是",
                                        "location": "液晶",
                                        "remark": "",
                                        "parent": "X08.01.02086"
                                },
                                {
                                        "sequence": 28,
                                        "code": "X08.02.00163",
                                        "description": "LED",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00163",
                                        "required": "是",
                                        "location": "LED",
                                        "remark": "",
                                        "parent": "X08.01.02087"
                                },
                                {
                                        "sequence": 29,
                                        "code": "X08.02.00091",
                                        "description": "门禁",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00091",
                                        "required": "是",
                                        "location": "门禁",
                                        "remark": "",
                                        "parent": "X08.01.02088"
                                },
                                {
                                        "sequence": 30,
                                        "code": "X08.02.00161",
                                        "description": "对讲终端",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00161",
                                        "required": "是",
                                        "location": "对讲终端",
                                        "remark": "",
                                        "parent": "X08.01.02089"
                                },
                                {
                                        "sequence": 31,
                                        "code": "X08.03.00262",
                                        "description": "*********.23240",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00262",
                                        "required": "是",
                                        "location": "*********.23240",
                                        "remark": "",
                                        "parent": "X08.01.02090"
                                },
                                {
                                        "sequence": 32,
                                        "code": "X08.03.00263",
                                        "description": "*********.23275",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00263",
                                        "required": "是",
                                        "location": "*********.23275",
                                        "remark": "",
                                        "parent": "X08.01.02091"
                                },
                                {
                                        "sequence": 33,
                                        "code": "X08.03.00264",
                                        "description": "*********.23276",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00264",
                                        "required": "是",
                                        "location": "*********.23276",
                                        "remark": "",
                                        "parent": "X08.01.00877"
                                },
                                {
                                        "sequence": 34,
                                        "code": "X08.03.00265",
                                        "description": "*********.23250",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00265",
                                        "required": "是",
                                        "location": "*********.23250",
                                        "remark": "",
                                        "parent": "X08.01.00879"
                                },
                                {
                                        "sequence": 35,
                                        "code": "X08.03.00266",
                                        "description": "*********.23268",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00266",
                                        "required": "是",
                                        "location": "*********.23268",
                                        "remark": "",
                                        "parent": "X08.01.00880"
                                },
                                {
                                        "sequence": 36,
                                        "code": "X08.03.00263",
                                        "description": "*********.23275",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00263",
                                        "required": "是",
                                        "location": "*********.23275",
                                        "remark": "",
                                        "parent": "X08.01.00881"
                                },
                                {
                                        "sequence": 37,
                                        "code": "X08.03.00261",
                                        "description": "*********.23242",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00261",
                                        "required": "是",
                                        "location": "*********.23242",
                                        "remark": "",
                                        "parent": "X08.01.00882"
                                },
                                {
                                        "sequence": 38,
                                        "code": "X08.03.00263",
                                        "description": "*********.23275",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00263",
                                        "required": "是",
                                        "location": "*********.23275",
                                        "remark": "",
                                        "parent": "X08.01.00883"
                                },
                                {
                                        "sequence": 39,
                                        "code": "X08.03.00264",
                                        "description": "*********.23276",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00264",
                                        "required": "是",
                                        "location": "*********.23276",
                                        "remark": "",
                                        "parent": "X08.01.00884"
                                },
                                {
                                        "sequence": 40,
                                        "code": "X08.03.00265",
                                        "description": "*********.23250",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00265",
                                        "required": "是",
                                        "location": "*********.23250",
                                        "remark": "",
                                        "parent": "X08.01.00885"
                                },
                                {
                                        "sequence": 41,
                                        "code": "X08.03.00263",
                                        "description": "*********.23275",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00263",
                                        "required": "是",
                                        "location": "*********.23275",
                                        "remark": "",
                                        "parent": "X08.01.00865"
                                },
                                {
                                        "sequence": 42,
                                        "code": "X08.02.00085",
                                        "description": "罪犯出监人脸核验终端",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00085",
                                        "required": "是",
                                        "location": "罪犯出监人脸核验终端",
                                        "remark": "",
                                        "parent": "X08.01.00886"
                                },
                                {
                                        "sequence": 43,
                                        "code": "X08.02.00086",
                                        "description": "摄像机",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00086",
                                        "required": "是",
                                        "location": "摄像机",
                                        "remark": "",
                                        "parent": "X08.01.00887"
                                },
                                {
                                        "sequence": 44,
                                        "code": "X08.02.00087",
                                        "description": "摄像机",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00087",
                                        "required": "是",
                                        "location": "摄像机",
                                        "remark": "",
                                        "parent": "X08.01.00888"
                                },
                                {
                                        "sequence": 45,
                                        "code": "X08.03.00365",
                                        "description": "*********.43293",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00365",
                                        "required": "是",
                                        "location": "*********.43293",
                                        "remark": "",
                                        "parent": "X08.01.00889"
                                },
                                {
                                        "sequence": 46,
                                        "code": "X08.02.00089",
                                        "description": "EVS集中存储",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00089",
                                        "required": "是",
                                        "location": "EVS集中存储",
                                        "remark": "",
                                        "parent": "X08.01.00890"
                                },
                                {
                                        "sequence": 47,
                                        "code": "X08.02.00090",
                                        "description": "非视频设备接入网关",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.02.00090",
                                        "required": "是",
                                        "location": "非视频设备接入网关",
                                        "remark": "",
                                        "parent": "X08.01.00891"
                                },
                                {
                                        "sequence": 48,
                                        "code": "X08.03.00272",
                                        "description": "*********.3026",
                                        "level": "4",
                                        "quantity": "1",
                                        "unit": "个",
                                        "brand": "大华",
                                        "model": "X08.03.00272",
                                        "required": "是",
                                        "location": "*********.3026",
                                        "remark": "",
                                        "parent": "X08.01.00892"
                                }
                        ]
                }
        }
};
        let lifecycleData = {
        "X08.01.01306": "量产",
        "*********.10080": "量产",
        "X08.01.01204": "即将停售",
        "*********.14532": "工程样机",
        "X08.01.00910": "量产",
        "*********.10039": "量产",
        "X08.01.01307": "量产",
        "*********.10219": "量产",
        "X08.01.01505": "量产",
        "1.0.99.02.11036": "量产",
        "X08.02.00160": "量产",
        "1.0.01.01.15970": "量产",
        "X08.01.02065": "量产",
        "1.0.01.01.15025": "量产",
        "X08.01.01578": "量产",
        "*********.10210": "量产",
        "X08.01.00888": "量产",
        "1.0.01.25.11495": "量产",
        "X08.01.02054": "量产",
        "1.0.01.99.10277": "量产",
        "X08.01.01792": "量产",
        "1.0.99.80.10033": "量产",
        "X08.01.00666": "量产",
        "1.0.99.68.10144": "量产",
        "X08.01.01847": "量产",
        "1.9.10.01.10554": "量产",
        "X08.01.00939": "量产",
        "1.0.01.04.40017": "量产",
        "X08.01.00678": "量产",
        "1.0.01.02.10548": "量产",
        "X08.01.02101": "即将停售",
        "1.0.01.04.46580": "工程样机",
        "X08.01.01784": "量产",
        "X08.01.01391": "量产",
        "1.0.01.12.20609": "量产",
        "X08.01.01490": "量产",
        "1.0.01.01.15971": "量产",
        "X08.01.01305": "量产",
        "2.9.02.01.10161": "量产",
        "X08.01.00927": "量产",
        "*********.10211": "量产",
        "X08.01.00930": "量产",
        "1.0.01.18.10514": "量产",
        "X08.01.01561": "量产",
        "2.9.02.02.11264": "量产",
        "X08.02.00079": "量产",
        "1.0.01.04.37518": "量产",
        "X08.01.01374": "量产",
        "1.9.10.01.10727": "量产",
        "X08.01.01822": "量产",
        "1.0.01.04.46579": "工程样机",
        "X08.01.01900": "量产",
        "1.2.01.27.10158": "量产",
        "X08.01.02060": "量产",
        "1.0.01.01.15893": "量产",
        "X08.01.00975": "量产",
        "1.2.01.23.10079": "量产",
        "X08.01.01118": "量产",
        "1.0.01.04.38909": "量产",
        "*********.10062": "量产",
        "X08.01.01653": "量产",
        "1.0.01.25.12406": "量产",
        "X08.01.01663": "量产",
        "2.9.02.02.11043": "量产",
        "X08.01.00924": "量产",
        "2.9.02.02.11261": "量产",
        "X08.01.00856": "量产",
        "1.0.01.25.11419": "量产",
        "X08.01.01544": "量产",
        "*********.10081": "量产",
        "X08.01.01593": "量产",
        "1.0.01.14.12308": "量产",
        "X08.01.00680": "量产",
        "2.9.02.12.10097": "量产",
        "X08.02.00165": "量产",
        "1.0.01.01.16031": "量产",
        "X08.01.01808": "量产",
        "1.0.01.05.10201": "量产",
        "X08.01.01022": "量产",
        "X08.01.01882": "量产",
        "1.2.01.33.10039": "量产",
        "X08.01.01569": "量产",
        "X08.01.01591": "开发",
        "1.0.01.02.10854": "量产",
        "X08.01.01730": "量产",
        "X08.01.01531": "即将停售",
        "1.2.01.33.10261": "量产",
        "X08.01.01143": "即将停售",
        "1.0.99.12.10121": "即将停售",
        "X08.01.01327": "量产",
        "2.9.02.02.11991": "量产",
        "X08.01.01356": "开发",
        "1.2.01.23.10045-001": "开发",
        "1.2.23.02.10285": "量产",
        "X08.01.01402": "量产",
        "1.0.01.13.12600": "量产",
        "X08.02.00081": "量产",
        "1.0.01.18.10916": "量产",
        "1.0.01.01.14979": "量产",
        "X08.01.01892": "量产",
        "1.2.44.01.17792-000": "量产",
        "X08.01.01570": "量产",
        "2.9.02.02.11674": "量产",
        "1.0.01.25.12404": "量产",
        "X08.01.01186": "工程样机",
        "*********.14533": "工程样机",
        "X08.01.01079": "量产",
        "1.0.01.18.10774": "量产",
        "X08.01.02028": "量产",
        "2.9.02.02.11253": "量产",
        "X08.01.00674": "量产",
        "1.0.99.50.10120": "即将停售",
        "X08.01.01319": "量产",
        "1.0.99.01.10195": "量产",
        "X08.01.01970": "量产",
        "1.0.01.20.10745": "即将停售",
        "X08.01.01069": "量产",
        "1.0.01.19.10019": "量产",
        "X08.01.00861": "开发",
        "1.0.01.19.10197": "量产",
        "X08.01.01013": "量产",
        "1.9.10.01.10553": "量产",
        "X08.01.02109": "工程样机",
        "1.0.01.01.15582": "量产",
        "X08.01.00748": "量产",
        "1.0.01.14.11509": "量产",
        "X08.01.00889": "量产",
        "1.2.01.27.10275": "量产",
        "X08.01.01046": "量产",
        "1.2.01.33.10045": "量产",
        "X08.01.01371": "量产",
        "1.0.01.25.12128": "量产",
        "X08.01.00936": "量产",
        "X08.01.01752": "量产",
        "1.2.50.10.12910-001": "量产",
        "X08.01.01317": "量产",
        "1.0.01.05.10426": "量产",
        "X08.01.01670": "开发",
        "2.3.01.01.10769": "开发",
        "2.9.02.02.11739": "量产",
        "X08.01.01898": "量产",
        "X08.02.00127": "量产",
        "*********.10145": "量产",
        "X08.01.01930": "量产",
        "1.2.01.23.10031": "开发",
        "X08.01.01358": "开发",
        "1.2.01.67.10490": "量产",
        "X08.01.01120": "量产",
        "1.0.01.46.10108": "量产",
        "X08.01.01736": "量产",
        "1.9.10.01.10521": "量产",
        "X08.01.01634": "量产",
        "1.0.01.25.11618": "量产",
        "X08.02.00124": "量产",
        "1.0.01.25.12253": "量产",
        "1.2.01.23.10148": "开发",
        "1.0.99.69.10015": "量产",
        "X08.01.01756": "量产",
        "1.2.19.07.10143-001": "量产",
        "X08.01.01609": "量产",
        "1.0.01.36.15207": "量产",
        "X08.01.00917": "含风险发布",
        "1.0.01.26.10848": "含风险发布",
        "X08.01.00847": "量产",
        "X08.01.01439": "量产",
        "1.2.23.02.10283": "量产",
        "X08.01.01057": "量产",
        "*********.10252": "量产",
        "X08.01.01370": "量产",
        "1.0.01.46.10105": "量产",
        "X08.01.01451": "量产",
        "1.0.01.01.15143": "量产",
        "X08.01.01592": "量产",
        "X08.01.01156": "量产",
        "2.9.02.02.11986": "量产",
        "X08.01.01099": "量产",
        "1.0.01.25.11638": "量产",
        "X08.01.00667": "量产",
        "1.9.10.01.10179": "量产",
        "X08.01.00890": "量产",
        "1.9.03.09.10325": "量产",
        "1.2.01.66.10689": "量产",
        "X08.01.01818": "开发",
        "X08.01.01330": "量产",
        "1.0.01.26.10545": "量产",
        "X08.01.01047": "量产",
        "1.2.01.23.10061": "量产",
        "1.2.01.23.10052": "量产",
        "X08.01.00887": "量产",
        "1.0.01.25.11831": "量产",
        "X08.01.01872": "工程样机",
        "X08.01.01874": "量产",
        "1.0.01.15.12377": "量产",
        "1.0.01.04.45220": "量产",
        "X08.01.02062": "量产",
        "1.0.01.14.12157": "量产",
        "X08.01.02099": "量产",
        "1.2.50.10.12263-000": "量产",
        "X08.01.01535": "开发",
        "1.9.10.01.10899": "开发",
        "X08.01.02061": "即将停售",
        "1.0.01.01.15125": "量产",
        "2.9.02.02.11263": "原型机",
        "X08.01.02071": "量产",
        "X08.02.00140": "量产",
        "1.0.01.01.15969": "量产",
        "X08.01.01696": "量产",
        "1.0.01.04.38567": "量产",
        "X08.01.01334": "开发",
        "*********.13789": "量产",
        "X08.01.00914": "量产",
        "X08.01.01952": "量产",
        "1.0.01.20.11882": "量产",
        "X08.01.00895": "量产",
        "X08.01.01481": "量产",
        "X08.02.00119": "量产",
        "1.0.01.36.14872": "量产",
        "1.0.01.46.10312": "量产",
        "X08.01.00909": "量产",
        "1.0.99.22.10063": "量产",
        "X08.01.01941": "量产",
        "1.9.04.04.10248": "量产",
        "X08.01.02032": "量产",
        "*********.10012": "量产",
        "X08.01.00952": "开发",
        "1.0.01.19.10544": "量产",
        "X08.01.01011": "量产",
        "X08.01.01743": "量产",
        "X08.01.00855": "量产",
        "1.2.01.27.10286": "量产",
        "X08.01.01839": "量产",
        "X08.01.00737": "量产",
        "1.0.01.25.12255": "量产",
        "X08.01.00865": "量产",
        "*********.14284": "量产",
        "X08.01.01622": "量产",
        "1.2.44.01.10053-000": "量产",
        "X08.01.00978": "开发",
        "1.0.01.01.15115": "量产",
        "X08.01.01889": "开发",
        "X08.01.00844": "量产",
        "1.0.01.21.10249": "量产",
        "1.0.01.01.16428": "量产",
        "X08.01.01302": "量产",
        "1.0.01.13.12436": "量产",
        "X08.01.01837": "量产",
        "1.0.01.07.16166": "量产",
        "X08.01.01357": "开发",
        "X08.01.01984": "量产",
        "1.0.99.85.10082": "即将停售",
        "X08.01.01094": "量产",
        "1.0.99.01.13093": "量产",
        "2.9.02.02.11648": "量产",
        "X08.01.01472": "量产",
        "X08.01.01161": "即将停售",
        "1.0.01.09.12918": "量产",
        "X08.01.01936": "量产",
        "1.0.99.85.10102": "量产",
        "1.1.02.08.10709": "量产",
        "X08.01.01412": "量产",
        "1.0.01.04.42324": "量产",
        "X08.01.01986": "即将停售",
        "1.0.01.20.11053": "量产",
        "X08.01.00982": "量产",
        "1.2.01.27.10159": "量产",
        "X08.01.01843": "即将停售",
        "1.2.19.07.10157": "量产",
        "X08.01.01777": "开发",
        "2.3.02.01.10283": "开发",
        "X08.01.00915": "含风险发布",
        "1.0.01.26.10435": "量产",
        "X08.01.01446": "含风险发布",
        "1.0.99.12.10124": "量产",
        "1.0.99.12.10224": "量产",
        "*********.10345": "量产",
        "X08.02.00138": "量产",
        "1.0.01.13.11869": "量产",
        "1.0.01.25.11620": "量产",
        "*********.10080": "量产",
        "X08.01.01541": "量产",
        "*********.0071": "量产",
        "X08.01.01650": "开发",
        "1.0.01.25.11859": "量产",
        "X08.01.01750": "量产",
        "X08.01.00946": "量产",
        "1.0.01.18.10923": "量产",
        "1.0.01.25.11494": "量产",
        "X08.01.00981": "量产",
        "X08.01.01554": "量产",
        "2.9.02.02.11248": "量产",
        "X08.01.01318": "量产",
        "X08.01.01572": "量产",
        "1.0.01.04.42325": "量产",
        "1.0.01.01.15968": "量产",
        "X08.01.01394": "量产",
        "2.3.02.01.10297": "量产",
        "X08.01.01493": "量产",
        "X08.01.01188": "量产",
        "1.0.01.07.14824": "量产",
        "X08.01.01421": "量产",
        "*********.10181": "量产",
        "X08.01.01470": "量产",
        "X08.01.01167": "量产",
        "1.0.99.81.10010": "量产",
        "X08.01.01798": "开发",
        "2.3.02.01.10411": "开发",
        "X08.01.01449": "量产",
        "X08.01.00919": "量产",
        "2.9.02.02.11247": "量产",
        "X08.01.01461": "量产",
        "X08.01.02029": "量产",
        "2.9.02.02.11265": "量产",
        "X08.01.01558": "量产",
        "2.9.02.02.11677": "量产",
        "X08.01.00941": "即将停售",
        "1.0.01.04.34960": "即将停售",
        "X08.02.00137": "量产",
        "X08.01.02034": "量产",
        "1.0.01.18.10513": "量产",
        "X08.02.00161": "量产",
        "X08.01.02105": "量产",
        "2.9.02.02.11045": "量产",
        "1.0.01.05.10297": "量产",
        "X08.01.00898": "量产",
        "1.0.01.09.13396": "量产",
        "X08.01.01621": "量产",
        "1.0.01.07.14746": "量产",
        "X08.01.01633": "量产",
        "1.0.99.69.10007": "量产",
        "X08.01.01810": "量产",
        "X08.01.01051": "量产",
        "X08.01.01080": "量产",
        "2.9.02.02.11292": "量产",
        "X08.01.00732": "量产",
        "1.2.01.27.10282": "即将停售",
        "1.0.01.02.11544": "量产",
        "X08.01.00766": "量产",
        "1.0.01.14.11383": "即将停售",
        "X08.01.01054": "量产",
        "X08.01.01081": "量产",
        "1.2.23.02.10286": "量产",
        "X08.01.01328": "量产",
        "1.0.01.13.12643": "量产",
        "X08.01.01827": "量产",
        "X08.01.01913": "量产",
        "*********.14920": "量产",
        "1.2.01.23.10151": "开发",
        "1.2.01.33.10262": "开发",
        "2.9.02.01.10142": "量产",
        "X08.01.01912": "量产",
        "X08.01.01180": "量产",
        "1.0.01.07.14450": "量产",
        "X08.01.01048": "量产",
        "1.0.99.12.10239": "量产",
        "X08.01.01803": "量产",
        "X08.01.00988": "量产",
        "1.0.01.04.38910": "量产",
        "X08.02.00094": "量产",
        "1.0.01.04.45226": "量产",
        "2.9.02.02.11988": "量产",
        "X08.01.01027": "量产",
        "1.0.01.13.12642": "量产",
        "X08.01.01880": "量产",
        "1.0.01.04.36322": "量产",
        "X08.01.01475": "即将停售",
        "X08.01.01668": "开发",
        "2.3.02.01.10331": "开发",
        "X08.01.01125": "量产",
        "2.9.02.02.12008": "量产",
        "*********.10064": "量产",
        "2.9.02.02.11250": "量产",
        "1.9.10.01.10726": "量产",
        "1.0.01.25.11535": "量产",
        "X08.01.02103": "量产",
        "1.0.01.19.10573": "量产",
        "1.2.01.27.10252": "即将停售",
        "1.0.01.02.11524": "量产",
        "X08.01.01501": "量产",
        "1.0.01.04.35322": "即将停售",
        "X08.01.01926": "量产",
        "1.0.01.01.15165": "量产",
        "1.2.01.33.10048": "量产",
        "1.9.03.07.10030": "量产",
        "2.3.02.01.10595": "开发",
        "X08.01.00901": "量产",
        "X08.01.02058": "量产",
        "1.0.01.14.11115": "量产",
        "1.2.01.23.10044-001": "开发",
        "1.0.01.13.13137": "量产",
        "X08.01.02008": "量产",
        "1.9.04.04.10262": "量产",
        "X08.02.00162": "量产",
        "1.0.01.14.10779": "量产",
        "X08.02.00110": "量产",
        "1.0.01.04.42383": "量产",
        "X08.01.00747": "即将停售",
        "X08.01.01573": "量产",
        "*********.15766": "即将停售",
        "X08.01.02097": "量产",
        "X08.01.01360": "量产",
        "1.9.01.01.10044": "量产",
        "X08.01.01491": "量产",
        "X08.01.00739": "量产",
        "X08.01.01111": "量产",
        "1.2.01.27.10267": "开发",
        "X08.01.00677": "量产",
        "2.9.02.02.11652": "量产",
        "X08.01.00953": "量产",
        "1.2.01.67.10096": "开发",
        "1.2.01.23.10060": "量产",
        "X08.01.01315": "量产",
        "1.0.01.13.12784": "量产",
        "1.0.99.01.10227": "量产",
        "X08.01.00989": "量产",
        "1.0.01.25.12306": "量产",
        "X08.01.01190": "即将停售",
        "1.9.10.01.10022": "量产",
        "X08.01.00986": "量产",
        "X08.01.01496": "量产",
        "1.9.03.01.10006": "量产",
        "X08.01.00880": "量产",
        "X08.02.00169": "量产",
        "1.9.02.01.10250": "量产",
        "1.0.99.01.10228": "量产",
        "X08.01.01806": "开发",
        "2.3.02.01.10339": "开发",
        "X08.01.01476": "量产",
        "X08.01.00904": "量产",
        "1.0.01.14.11204": "量产",
        "X08.02.00111": "量产",
        "1.0.01.04.38912": "量产",
        "X08.01.01635": "量产",
        "1.9.03.05.10726": "量产",
        "X08.01.00859": "量产",
        "1.0.01.19.10571": "量产",
        "1.0.99.12.10546": "量产",
        "2.9.02.01.10196": "量产",
        "1.0.01.01.15014": "即将停售",
        "1.0.01.15.12638": "量产",
        "1.9.10.01.10390": "量产",
        "2.9.02.02.11672": "量产",
        "X08.01.01924": "量产",
        "2.9.02.01.11381": "量产",
        "X08.01.00907": "量产",
        "X08.01.01629": "量产",
        "X08.01.01747": "即将停售",
        "X08.01.00860": "量产",
        "1.0.01.19.10018": "量产",
        "1.0.99.69.10017": "量产",
        "X08.01.01517": "量产",
        "1.0.01.18.10689": "量产",
        "X08.02.00134": "量产",
        "1.0.01.01.16069": "量产",
        "*********.10086": "量产",
        "X08.01.01738": "量产",
        "X08.01.01288": "量产",
        "1.0.99.12.10106": "量产",
        "X08.01.00740": "量产",
        "1.0.01.15.12312": "量产",
        "1.0.01.15.12581": "量产",
        "X08.01.00948": "量产",
        "1.2.01.27.10005": "量产",
        "X08.01.01367": "量产",
        "X08.01.01342": "量产",
        "1.0.01.04.39513": "量产",
        "1.0.01.14.11935": "量产",
        "X08.01.01539": "量产",
        "X08.01.01211": "量产",
        "X08.01.00850": "量产",
        "1.0.01.04.38482": "量产",
        "X08.01.01745": "工程样机",
        "2.9.02.02.11257": "量产",
        "X08.01.00899": "量产",
        "X08.01.01214": "量产",
        "X08.01.01498": "量产",
        "X08.01.01883": "量产",
        "X08.01.01824": "即将停售",
        "1.0.01.04.40903": "量产",
        "X08.01.01074": "量产",
        "1.0.99.88.10088": "量产",
        "X08.01.01852": "量产",
        "1.0.99.12.10057": "即将停售",
        "1.0.01.18.10515": "量产",
        "X08.01.00881": "量产",
        "1.0.01.01.13043": "量产",
        "X08.01.01418": "量产",
        "X08.01.01289": "量产",
        "X08.01.01400": "量产",
        "1.0.01.25.11670": "量产",
        "X08.01.01217": "量产",
        "*********.10101": "量产",
        "X08.01.02067": "量产",
        "1.0.01.01.14639": "即将停售",
        "1.0.01.04.40968": "即将停售",
        "X08.01.01925": "量产",
        "1.0.01.04.38158": "量产",
        "X08.01.00759": "量产",
        "2.9.02.02.11243": "量产",
        "X08.01.01699": "量产",
        "1.0.01.36.13791": "含风险发布",
        "1.0.01.25.12400": "量产",
        "X08.01.00780": "量产",
        "1.0.01.18.10512": "量产",
        "X08.01.01133": "开发",
        "X08.01.01577": "量产",
        "1.0.01.18.10791": "量产",
        "1.0.99.01.10397": "量产",
        "1.9.10.01.10895": "开发",
        "X08.01.00883": "量产",
        "1.2.01.23.10080": "量产",
        "X08.01.01503": "量产",
        "X08.01.01887": "量产",
        "X08.01.01647": "量产",
        "X08.01.00668": "量产",
        "2.9.02.02.11653": "量产",
        "X08.01.01078": "即将停售",
        "*********.15767": "量产",
        "X08.01.01353": "量产",
        "2.3.02.01.10284": "开发",
        "X08.01.01778": "量产",
        "1.0.01.13.13040": "量产",
        "X08.01.00761": "量产",
        "1.0.01.25.12036": "量产",
        "1.0.01.07.16101": "量产",
        "1.0.01.04.43211": "量产",
        "X08.01.01753": "开发",
        "X08.01.01077": "量产",
        "1.0.01.05.10348": "即将停售",
        "X08.01.01115": "量产",
        "1.0.01.25.12127": "量产",
        "1.9.07.01.10035": "量产",
        "1.2.50.10.13355-000": "量产",
        "1.2.01.07.10125": "量产",
        "X08.02.00120": "量产",
        "2.3.02.01.10294": "量产",
        "X08.01.00758": "量产",
        "1.0.01.04.38561": "量产",
        "X08.01.01355": "量产",
        "X08.01.00817": "量产",
        "1.0.01.14.11465": "即将停售",
        "X08.01.01762": "量产",
        "X08.01.01014": "量产",
        "X08.02.00170": "量产",
        "X08.01.01362": "开发",
        "1.2.01.67.10488": "开发",
        "X08.01.01218": "量产",
        "2.9.02.01.10209": "量产",
        "X08.01.01807": "量产",
        "X08.01.01863": "工程样机",
        "X08.01.01450": "量产",
        "X08.01.01154": "即将停售",
        "1.0.01.09.14503": "量产",
        "X08.01.01838": "量产",
        "1.9.03.07.10031": "量产",
        "X08.01.02057": "量产",
        "1.0.99.41.10039": "量产",
        "X08.01.00776": "量产",
        "1.2.01.27.10111": "量产",
        "X08.01.01642": "量产",
        "1.9.03.09.10026": "量产",
        "X08.01.01891": "量产",
        "X08.01.01276": "量产",
        "X08.01.02001": "量产",
        "X08.01.02022": "含风险发布",
        "X08.01.02048": "量产",
        "2.9.02.10.11087": "量产",
        "X08.01.01624": "量产",
        "1.0.01.04.42243": "量产",
        "X08.01.00937": "量产",
        "1.0.01.04.39768": "量产",
        "X08.01.01061": "量产",
        "1.9.10.01.10518": "量产",
        "X08.01.01262": "即将停售",
        "1.2.01.23.10145": "开发",
        "X08.01.01459": "量产",
        "1.2.19.07.10158": "量产",
        "1.2.01.23.10153": "开发",
        "X08.02.00095": "量产",
        "2.9.02.02.11255": "量产",
        "X08.01.01850": "量产",
        "X08.01.00923": "量产",
        "X08.01.00925": "量产",
        "2.9.02.02.11281": "量产",
        "X08.01.01560": "量产",
        "2.9.02.02.11254": "量产",
        "X08.02.00132": "量产",
        "X08.01.01467": "量产",
        "X08.01.01445": "量产",
        "1.0.01.15.12637": "量产",
        "X08.01.01158": "量产",
        "X08.01.00669": "量产",
        "1.0.99.44.10203": "量产",
        "X08.01.00857": "量产",
        "1.9.10.01.11286": "量产",
        "X08.01.00746": "量产",
        "X08.01.02064": "开发",
        "X08.01.01546": "量产",
        "1.0.01.13.12556": "量产",
        "X08.01.01065": "量产",
        "X08.01.01981": "量产",
        "1.0.01.20.11597": "量产",
        "X08.01.01611": "量产",
        "X08.01.01365": "量产",
        "X08.01.00908": "量产",
        "X08.01.01857": "量产",
        "X08.01.01637": "量产",
        "*********.14645": "即将停售",
        "1.0.99.22.10054": "量产",
        "X08.01.01616": "量产",
        "2.9.02.01.10302": "量产",
        "X08.01.01625": "量产",
        "1.1.02.44.10240-001": "开发",
        "X08.01.01567": "量产",
        "1.0.01.13.12362": "量产",
        "1.0.01.01.15973": "量产",
        "X08.01.01922": "量产",
        "X08.01.01068": "量产",
        "*********.15185": "量产",
        "X08.01.00979": "即将停售",
        "1.0.01.25.11503": "量产",
        "X08.01.01785": "量产",
        "1.2.23.02.10278": "量产",
        "X08.01.01165": "量产",
        "1.0.01.04.37379": "量产",
        "1.0.01.01.16307": "量产",
        "X08.01.01873": "开发",
        "X08.01.01349": "量产",
        "X08.01.00756": "量产",
        "1.0.01.02.11187": "量产",
        "2.9.02.01.10210": "量产",
        "1.0.01.04.38911": "量产",
        "X08.01.01406": "开发",
        "1.0.01.15.12317": "量产",
        "X08.01.01089": "量产",
        "2.9.02.02.11270": "原型机",
        "X08.01.01122": "量产",
        "1.9.09.01.10144": "量产",
        "X08.01.01182": "量产",
        "1.0.01.02.11126": "量产",
        "2.9.02.02.11258": "量产",
        "1.2.01.23.10049": "量产",
        "1.0.01.19.10549": "量产",
        "X08.01.01610": "量产",
        "X08.01.01542": "量产",
        "X08.01.01691": "即将停售",
        "2.9.02.01.10144": "量产",
        "X08.01.01178": "量产",
        "1.0.01.04.39254": "量产",
        "1.0.01.01.16523": "量产",
        "X08.01.02031": "量产",
        "2.9.02.02.11479": "量产",
        "1.0.01.19.10572": "量产",
        "X08.02.00166": "量产",
        "1.0.01.18.10830": "量产",
        "X08.01.00675": "量产",
        "1.0.01.07.15093": "即将停售",
        "*********.10571": "量产",
        "X08.01.00767": "开发",
        "X08.01.01614": "量产",
        "X08.01.01667": "量产",
        "1.0.01.13.12785": "量产",
        "X08.01.00973": "量产",
        "X08.02.00118": "量产",
        "X08.01.01293": "即将停售",
        "X08.01.01021": "量产",
        "*********.10182": "量产",
        "X08.02.00159": "量产",
        "X08.01.01980": "即将停售",
        "1.0.01.20.11879": "量产",
        "X08.01.01652": "量产",
        "1.0.01.25.11727": "量产",
        "1.0.01.01.14872": "含风险发布",
        "X08.01.01669": "量产",
        "1.2.01.19.10036": "开发",
        "X08.01.01284": "量产",
        "X08.01.01997": "量产",
        "X08.01.01351": "量产",
        "1.2.19.07.10010": "量产",
        "1.0.99.12.10552": "量产",
        "X08.01.01363": "开发",
        "X08.01.00818": "即将停售",
        "1.0.01.13.10718": "量产",
        "1.0.99.01.10396": "量产",
        "1.0.01.34.13042": "量产",
        "X08.01.01145": "量产",
        "1.0.01.02.11078": "量产",
        "X08.01.01861": "量产",
        "1.0.01.18.10915": "量产",
        "X08.01.02063": "量产",
        "X08.01.00983": "量产",
        "1.9.03.09.10296": "量产",
        "X08.01.00891": "量产",
        "1.9.10.01.10172": "量产",
        "X08.01.01958": "废弃",
        "2.3.02.01.10828": "废弃",
        "X08.01.01301": "原型机",
        "*********.13996": "即将停售",
        "*********.15076": "量产",
        "1.2.01.23.10155": "量产",
        "X08.01.01617": "量产",
        "1.0.01.01.14768": "量产",
        "X08.01.00984": "量产",
        "X08.01.01390": "量产",
        "2.3.02.01.10298": "量产",
        "X08.01.01919": "量产",
        "X08.01.01739": "工程样机",
        "2.9.02.10.11150": "量产",
        "X08.01.00743": "量产",
        "1.0.01.18.10510": "量产",
        "X08.01.01359": "量产",
        "X08.01.00971": "量产",
        "X08.01.01890": "量产",
        "1.0.99.41.10038": "量产",
        "X08.01.01462": "量产",
        "1.0.01.09.14523": "量产",
        "1.0.01.18.10831": "量产",
        "X08.01.01904": "量产",
        "X08.01.01202": "量产",
        "1.0.01.07.14883": "量产",
        "X08.01.02035": "量产",
        "1.0.01.25.12409": "量产",
        "X08.01.01162": "量产",
        "X08.01.01576": "量产",
        "1.0.01.13.12599": "量产",
        "X08.01.01026": "量产",
        "2.9.02.02.11639": "量产",
        "X08.01.01106": "量产",
        "1.0.01.25.11328": "即将停售",
        "X08.01.02107": "工程样机",
        "X08.01.00736": "量产",
        "2.9.02.02.11640": "量产",
        "X08.01.00902": "量产",
        "X08.01.01477": "量产",
        "X08.01.01399": "量产",
        "X08.01.00950": "量产",
        "X08.01.01332": "量产",
        "2.9.02.02.11984": "量产",
        "2.9.02.02.11983": "量产",
        "X08.01.01064": "量产",
        "X08.01.01012": "量产",
        "X08.01.00947": "量产",
        "1.2.01.27.10271": "开发",
        "X08.01.01888": "量产",
        "X08.01.01268": "量产",
        "X08.01.01341": "量产",
        "1.0.01.04.37628": "量产",
        "X08.01.01187": "量产",
        "1.9.10.01.10021": "量产",
        "X08.01.01422": "量产",
        "*********.10074": "量产",
        "X08.01.01140": "量产",
        "X08.01.00774": "量产",
        "X08.01.01566": "量产",
        "*********.10013": "量产",
        "X08.01.01310": "量产",
        "2.9.02.06.10030": "量产",
        "1.0.01.02.11072": "量产",
        "X08.01.01909": "量产",
        "X08.01.01618": "量产",
        "1.0.01.01.16487": "量产",
        "X08.01.01152": "量产",
        "X08.01.01216": "量产",
        "*********.10087": "量产",
        "X08.01.00886": "即将停售",
        "1.0.01.04.26108": "量产",
        "X08.01.01754": "量产",
        "1.0.01.15.12310": "量产",
        "X08.01.00882": "即将停售",
        "1.0.01.14.11365": "即将停售",
        "X08.01.01208": "量产",
        "1.0.01.04.36591": "量产",
        "X08.01.01953": "量产",
        "X08.01.01290": "量产",
        "1.0.01.04.38310": "量产",
        "X08.01.00951": "量产",
        "X08.01.01551": "量产",
        "1.0.01.26.10517": "量产",
        "1.0.01.09.15441": "量产",
        "X08.01.00879": "量产",
        "X08.01.01942": "废弃",
        "2.3.02.01.10821": "开发",
        "1.9.10.01.10724": "量产",
        "1.0.01.09.13477": "即将停售",
        "X08.01.00676": "量产",
        "1.0.01.05.10205": "量产",
        "X08.01.01781": "量产",
        "1.0.01.26.10593": "量产",
        "X08.01.01580": "量产",
        "1.0.01.20.11064": "量产",
        "X08.01.01589": "量产",
        "1.0.01.05.10488": "量产",
        "1.9.10.01.10532": "量产",
        "1.0.01.07.14444": "量产",
        "X08.01.01523": "即将停售",
        "X08.01.01937": "量产",
        "X08.01.01728": "量产",
        "1.2.01.23.2303": "量产",
        "X08.01.02069": "量产",
        "1.0.01.09.13550": "即将停售",
        "1.9.03.06.10158": "量产",
        "X08.01.01985": "量产",
        "X08.01.01468": "量产",
        "X08.01.01200": "量产",
        "X08.01.00762": "量产",
        "1.0.01.14.12266": "量产",
        "1.9.01.01.10046": "量产",
        "X08.01.00777": "量产",
        "1.0.01.13.12641": "量产",
        "X08.02.00071": "量产",
        "1.0.01.04.41412": "即将停售",
        "2.9.02.02.11260": "量产",
        "1.2.19.07.10044": "即将停售",
        "X08.01.00940": "量产",
        "1.0.01.07.13895": "量产",
        "X08.01.00877": "工程样机",
        "X08.01.01113": "量产",
        "1.0.99.12.10060": "量产",
        "X08.01.00745": "量产",
        "1.0.01.07.13463": "量产",
        "X08.01.01136": "量产",
        "1.0.01.14.11101-D001": "量产",
        "X08.01.01321": "开发",
        "2.3.02.01.10587": "开发",
        "X08.01.01831": "即将停售",
        "1.0.99.69.10123": "量产",
        "X08.01.01760": "量产",
        "X08.01.02040": "量产",
        "1.0.01.15.12112": "量产",
        "1.0.99.88.10136": "量产",
        "1.2.01.66.10693": "量产",
        "X08.01.01759": "量产",
        "X08.01.00845": "量产",
        "X08.01.01097": "量产",
        "X08.01.00976": "量产",
        "2.9.02.01.10604": "量产",
        "1.0.01.01.15929": "量产",
        "2.3.02.01.10601": "开发",
        "X08.01.01524": "即将停售",
        "1.0.01.09.13551": "即将停售",
        "1.0.01.02.11522": "量产",
        "X08.01.02024": "量产",
        "X08.01.01816": "量产",
        "X08.01.01095": "即将停售",
        "2.9.02.02.11256": "量产",
        "X08.01.01072": "量产",
        "2.9.02.01.11382": "量产",
        "1.0.99.12.10222": "量产",
        "X08.02.00101": "量产",
        "X08.01.02070": "量产",
        "X08.01.00735": "量产",
        "X08.01.02020": "量产",
        "2.9.02.08.10129": "含风险发布",
        "*********.10085": "量产",
        "X08.01.02066": "量产",
        "X08.01.01644": "量产",
        "1.9.10.01.10171": "量产",
        "X08.01.01879": "量产",
        "X08.01.01369": "量产",
        "X08.01.01215": "量产",
        "X08.01.01929": "量产",
        "1.9.10.01.10173": "量产",
        "X08.02.00100": "即将停售",
        "X08.01.02039": "量产",
        "1.2.01.23.10066": "量产",
        "X08.01.01662": "即将停售",
        "1.0.01.04.40138": "量产",
        "1.0.01.04.45575": "量产",
        "X08.01.01219": "量产",
        "2.9.02.06.10027": "量产",
        "X08.01.01540": "量产",
        "X08.01.01344": "量产",
        "1.0.01.04.40116": "量产",
        "X08.01.01392": "即将停售",
        "1.0.01.01.15138": "量产",
        "1.0.01.02.11073": "量产",
        "X08.01.01921": "量产",
        "2.9.02.01.10164": "量产",
        "X08.01.00931": "量产",
        "2.9.02.02.11673": "量产",
        "X08.01.01207": "量产",
        "1.0.01.04.39228": "量产",
        "1.0.01.07.13891": "工程样机",
        "X08.01.01845": "量产",
        "1.0.01.05.10373": "量产",
        "X08.01.01851": "量产",
        "X08.01.01733": "工程样机",
        "X08.02.00089": "量产",
        "X08.01.01938": "量产",
        "1.2.01.23.10064": "开发",
        "1.2.01.66.10690": "量产",
        "X08.01.01298": "量产",
        "X08.01.01758": "量产",
        "X08.01.01729": "量产",
        "1.0.01.26.10793": "量产",
        "X08.02.00121": "量产",
        "X08.01.01996": "量产",
        "1.9.03.09.10323": "量产",
        "X08.01.01772": "量产",
        "1.0.01.25.12467": "量产",
        "X08.01.02055": "量产",
        "1.0.01.14.12182": "量产",
        "X08.01.01813": "量产",
        "*********.10066": "量产",
        "X08.01.01612": "量产",
        "X08.01.01263": "量产",
        "1.0.01.04.44804": "量产",
        "1.2.01.23.10055": "量产",
        "1.0.01.25.12403": "量产",
        "X08.01.01915": "量产",
        "1.2.02.18.10031": "量产",
        "X08.01.01749": "量产",
        "1.0.99.12.10414": "量产",
        "X08.01.01856": "开发",
        "X08.01.02044": "开发",
        "1.0.01.09.14516": "量产",
        "X08.02.00073": "即将停售",
        "1.0.01.07.14614": "量产",
        "X08.01.01518": "量产",
        "X08.01.01562": "量产",
        "2.9.02.02.11274": "量产",
        "X08.01.00768": "量产",
        "*********.10480": "量产",
        "X08.01.00670": "量产",
        "1.2.01.23.10059": "量产",
        "X08.01.01661": "量产",
        "2.9.02.02.11708": "量产",
        "2.3.02.01.10597": "开发",
        "X08.01.00729": "量产",
        "1.0.01.01.16072": "量产",
        "X08.01.01308": "量产",
        "*********.10088": "量产",
        "X08.02.00115": "量产",
        "1.2.50.10.12261-000": "量产",
        "1.0.99.88.10064": "量产",
        "X08.02.00070": "即将停售",
        "1.0.01.25.11502": "量产",
        "1.9.03.05.10251": "量产",
        "X08.01.01169": "量产",
        "X08.01.01933": "量产",
        "1.9.09.01.10085": "量产",
        "X08.01.01627": "量产",
        "X08.01.01134": "量产",
        "X08.01.00764": "量产",
        "2.9.02.02.11646": "量产",
        "X08.01.00906": "量产",
        "1.0.01.25.12470": "量产",
        "X08.01.01482": "量产",
        "1.0.01.01.16486": "量产",
        "X08.01.01458": "量产",
        "X08.01.01299": "量产",
        "X08.01.00679": "量产",
        "X08.01.00928": "量产",
        "X08.01.01823": "量产",
        "X08.01.01509": "开发",
        "1.0.01.04.43304": "量产",
        "X08.01.01868": "量产",
        "X08.01.01931": "量产",
        "1.2.01.25.10005": "开发",
        "1.0.01.15.12582": "量产",
        "X08.01.01748": "开发",
        "X08.01.01964": "量产",
        "X08.01.01213": "量产",
        "1.0.01.13.12292": "量产",
        "X08.01.01266": "量产",
        "X08.01.01075": "量产",
        "1.9.10.01.10024": "量产",
        "1.0.01.25.11352": "量产",
        "X08.01.00755": "量产",
        "1.0.01.01.10869": "量产",
        "*********.10103": "量产",
        "X08.01.00773": "即将停售",
        "X08.01.01030": "量产",
        "X08.01.01149": "量产",
        "X08.01.01309": "量产",
        "X08.01.01512": "量产",
        "2.3.02.01.10330": "开发",
        "X08.01.01547": "含风险发布",
        "X08.01.01324": "开发",
        "2.3.02.01.10603": "开发",
        "X08.01.01478": "即将停售",
        "1.0.01.34.12858": "量产",
        "X08.01.01510": "量产",
        "X08.02.00117": "量产",
        "X08.01.01885": "量产",
        "2.3.01.01.10820": "开发",
        "2.9.02.06.10025": "量产",
        "*********.10104": "量产",
        "X08.01.01506": "量产",
        "X08.01.01782": "量产",
        "2.3.02.01.10598": "开发",
        "X08.01.01199": "量产",
        "1.0.01.14.11922": "量产",
        "X08.01.01989": "量产",
        "2.9.02.02.11987": "量产",
        "X08.02.00126": "量产",
        "1.0.99.22.10062": "量产",
        "X08.01.00980": "量产",
        "X08.01.01692": "量产",
        "X08.01.01192": "量产",
        "X08.01.00969": "工程样机",
        "1.0.99.19.10139": "量产",
        "1.9.10.01.10897": "开发",
        "1.0.01.04.42200": "量产",
        "X08.01.01649": "开发",
        "1.0.01.07.13514": "量产",
        "2.9.02.02.11644": "量产",
        "1.0.01.13.13138": "量产",
        "X08.01.01420": "量产",
        "1.0.01.18.10829": "量产",
        "1.0.01.07.14723": "量产",
        "1.0.01.14.11872": "量产",
        "1.0.01.04.44805": "量产",
        "X08.01.01947": "量产",
        "X08.01.01333": "量产",
        "X08.01.01117": "量产",
        "1.0.01.26.10528": "量产",
        "1.2.01.23.10030": "开发",
        "*********.10137": "量产",
        "X08.01.01431": "量产",
        "1.9.10.01.10038": "量产",
        "X08.01.00858": "量产",
        "1.0.01.04.38569": "量产",
        "X08.01.01866": "量产",
        "1.0.99.12.10123": "量产",
        "*********.13789-0003": "量产",
        "X08.01.01588": "量产",
        "X08.01.01639": "即将停售",
        "*********.14725": "即将停售",
        "X08.01.01693": "量产",
        "*********.15177": "废弃",
        "X08.01.01056": "量产",
        "X08.02.00090": "量产",
        "1.0.01.13.10607": "量产",
        "X08.01.01197": "量产",
        "1.0.01.46.10098": "量产",
        "X08.02.00164": "量产",
        "*********.10084": "量产",
        "X08.01.01597": "量产",
        "2.9.02.01.10073": "量产",
        "X08.01.01780": "量产",
        "X08.01.00942": "量产",
        "1.0.01.07.15520": "量产",
        "X08.01.01084": "量产",
        "*********.10011": "量产",
        "X08.01.01440": "量产",
        "2.9.02.12.10061": "量产",
        "X08.01.01923": "量产",
        "X08.01.01275": "量产",
        "2.9.02.02.11641": "量产",
        "X08.01.02068": "量产",
        "X08.02.00092": "量产",
        "2.9.02.01.10130": "量产",
        "*********.10175": "量产",
        "1.0.01.05.10365": "量产",
        "X08.01.01130": "量产",
        "X08.01.01103": "量产",
        "1.2.01.23.10047": "量产",
        "X08.01.01304": "量产",
        "X08.01.00779": "量产",
        "X08.01.01210": "原型机",
        "X08.01.00938": "量产",
        "X08.01.00848": "量产",
        "1.0.01.04.43301": "量产",
        "2.3.01.01.10770": "开发",
        "X08.02.00112": "量产",
        "1.0.01.25.10474": "量产",
        "X08.01.00913": "量产",
        "1.0.01.14.12181": "量产",
        "X08.01.01920": "量产",
        "1.0.01.25.11665": "量产",
        "X08.01.01688": "量产",
        "X08.02.00139": "量产",
        "1.0.99.01.13097": "量产",
        "X08.01.01779": "量产",
        "1.0.01.13.13022": "量产",
        "X08.01.01087": "量产",
        "X08.01.01742": "量产",
        "1.9.03.09.10543": "量产",
        "X08.01.01968": "量产",
        "1.0.01.05.10213": "量产",
        "X08.01.01473": "量产",
        "X08.01.01050": "量产",
        "1.0.01.18.10920": "量产",
        "X08.01.01281": "量产",
        "1.2.01.23.10065": "量产",
        "X08.01.01335": "开发",
        "1.2.01.67.10047": "开发",
        "X08.01.01457": "量产",
        "X08.01.01414": "开发",
        "2.3.02.01.10590": "开发",
        "X08.01.01737": "量产",
        "X08.01.01297": "量产",
        "X08.01.01859": "量产",
        "X08.01.01463": "量产",
        "X08.02.00087": "量产",
        "1.0.01.25.11836": "量产",
        "1.9.10.01.10896": "开发",
        "X08.01.01465": "量产",
        "1.0.01.04.43556": "量产",
        "X08.01.01871": "即将停售",
        "X08.01.01323": "开发",
        "2.3.02.01.10602": "开发",
        "X08.01.01864": "量产",
        "1.9.10.01.10520": "量产",
        "X08.01.01641": "量产",
        "X08.01.01530": "量产",
        "1.0.01.09.15198": "量产",
        "1.0.01.04.40616": "量产",
        "X08.01.02026": "量产",
        "1.0.01.14.11373": "量产",
        "X08.01.00972": "量产",
        "X08.01.02050": "开发",
        "1.0.01.18.10896": "原型机",
        "1.0.01.14.11873": "量产",
        "X08.02.00168": "量产",
        "X08.01.00985": "即将停售",
        "X08.01.01982": "即将停售",
        "X08.01.01741": "即将停售",
        "X08.01.00921": "量产",
        "2.3.02.01.10596": "开发",
        "X08.01.02006": "量产",
        "1.9.04.04.10270": "量产",
        "2.9.02.01.10319": "量产",
        "X08.02.00106": "量产",
        "1.0.01.04.38558": "量产",
        "X08.01.00741": "量产",
        "X08.01.01954": "即将停售",
        "X08.01.01361": "量产",
        "X08.01.01053": "量产",
        "2.9.02.02.11651": "量产",
        "X08.01.00843": "量产",
        "1.2.01.33.10019": "量产",
        "X08.01.01495": "量产",
        "1.2.01.19.10040": "开发",
        "1.0.99.12.10412": "量产",
        "X08.01.01819": "量产",
        "1.0.01.04.42404": "量产",
        "1.0.01.04.42242": "量产",
        "X08.01.01794": "即将停售",
        "*********.14209": "量产",
        "X08.02.00091": "量产",
        "X08.01.01710": "量产",
        "2.9.02.02.11266": "量产",
        "X08.01.00892": "即将停售",
        "1.0.01.04.40123": "量产",
        "X08.01.00672": "量产",
        "X08.01.02041": "量产",
        "X08.01.01132": "量产",
        "X08.01.01565": "量产",
        "X08.01.01485": "量产",
        "1.0.01.34.14271": "量产",
        "X08.01.01966": "量产",
        "1.9.04.04.10259": "量产",
        "1.0.99.12.10227": "量产",
        "1.2.01.23.10069": "量产",
        "2.3.02.01.10822": "废弃",
        "X08.02.00113": "量产",
        "X08.01.01543": "量产",
        "X08.01.01672": "量产",
        "X08.01.01600": "量产",
        "X08.01.01516": "开发",
        "2.9.02.02.11291": "量产",
        "2.9.02.02.11643": "量产",
        "X08.01.01191": "量产",
        "X08.01.01083": "量产",
        "X08.01.00977": "量产",
        "X08.01.00893": "量产",
        "1.0.01.14.12150": "量产",
        "X08.01.00816": "量产",
        "1.0.01.25.11621": "量产",
        "1.9.10.01.10894": "开发",
        "X08.01.01343": "量产",
        "1.0.01.04.39222": "量产",
        "X08.01.01711": "废弃",
        "1.2.41.04.0934": "量产",
        "1.0.99.01.10235": "量产",
        "1.0.99.02.10034": "量产",
        "1.2.01.23.10068": "量产",
        "1.0.01.04.36694": "量产",
        "X08.01.01157": "量产",
        "1.0.01.14.11415": "量产",
        "X08.03.00365": "量产",
        "X08.01.01016": "量产",
        "2.9.02.02.11286": "原型机",
        "1.0.01.14.11102-D001": "量产",
        "2.9.02.02.11985": "量产",
        "X08.01.01127": "量产",
        "X08.01.00929": "量产",
        "1.0.01.13.12071": "量产",
        "X08.01.01867": "量产",
        "2.9.02.06.10026": "量产",
        "X08.01.01193": "量产",
        "2.9.02.02.11051": "量产",
        "X08.01.02003": "量产",
        "1.9.04.04.10254": "量产",
        "X08.01.01709": "量产",
        "X08.01.02036": "量产",
        "X08.01.01471": "量产",
        "1.9.10.01.11335": "量产",
        "X08.01.01404": "量产",
        "X08.01.01631": "量产",
        "1.0.01.14.12267": "量产",
        "X08.02.00125": "量产",
        "X08.01.01976": "量产",
        "X08.01.01273": "量产",
        "X08.01.00900": "量产",
        "X08.02.00105": "量产",
        "1.0.01.04.40107": "量产",
        "X08.01.00673": "量产",
        "X08.01.02056": "量产",
        "X08.01.01441": "量产",
        "1.0.01.09.12943": "量产",
        "X08.01.01840": "量产",
        "2.9.02.02.11576": "量产",
        "1.2.01.27.10248": "量产",
        "1.0.01.01.16106": "含风险发布",
        "X08.01.01205": "量产",
        "1.1.02.08.13450": "量产",
        "X08.02.00136": "量产",
        "*********.15710": "量产",
        "X08.01.01514": "量产",
        "1.0.01.34.13338": "量产",
        "X08.01.01020": "量产",
        "2.9.02.06.10203": "量产",
        "1.2.44.01.17604-000": "量产",
        "X08.01.01508": "量产",
        "1.9.10.01.10389": "量产",
        "X08.01.01098": "量产",
        "X08.01.01657": "量产",
        "X08.01.01969": "量产",
        "1.0.01.04.40608": "量产",
        "X08.01.02051": "量产",
        "1.0.01.13.12945": "量产",
        "1.0.99.36.10184": "即将停售",
        "1.0.01.02.11081": "量产",
        "1.0.01.07.11535": "即将停售",
        "2.3.02.01.10592": "开发",
        "X08.01.01464": "量产",
        "1.9.03.07.10052": "量产",
        "1.0.01.13.12787": "量产",
        "1.2.01.23.10067": "量产",
        "X08.01.02033": "量产",
        "X08.01.01479": "量产",
        "1.0.01.14.11581": "量产",
        "1.2.02.08.10018": "开发",
        "X08.01.01587": "量产",
        "1.0.01.18.10917": "量产",
        "1.0.01.13.12368": "量产",
        "X08.01.01486": "量产",
        "X08.01.01272": "量产",
        "1.0.01.21.10281": "量产",
        "X08.01.00949": "量产",
        "X08.01.00771": "量产",
        "1.0.01.01.15149": "含风险发布",
        "X08.01.01513": "量产",
        "X08.01.01029": "量产",
        "X08.01.01860": "量产",
        "X08.01.01770": "量产",
        "*********.10197": "量产",
        "X08.01.00849": "即将停售",
        "1.0.01.04.34983": "即将停售",
        "1.2.01.66.10694": "量产",
        "2.3.02.01.10594": "开发",
        "1.2.01.23.10062": "量产",
        "1.0.01.13.12288": "量产",
        "X08.01.01109": "即将停售",
        "1.0.01.01.15015": "即将停售",
        "1.0.01.13.12676": "量产",
        "X08.01.01173": "量产",
        "X08.01.01590": "含风险发布",
        "X08.02.00167": "量产",
        "X08.01.01209": "量产",
        "1.9.10.01.10023": "量产",
        "X08.01.01623": "量产",
        "1.0.01.18.10657": "即将停售",
        "X08.01.00974": "即将停售",
        "1.2.01.66.10701": "量产",
        "1.0.01.25.11212": "即将停售",
        "X08.01.01978": "量产",
        "X08.02.00099": "即将停售",
        "1.0.01.13.12946": "量产",
        "1.0.01.14.12151": "量产",
        "X08.01.00911": "量产",
        "*********.10176": "量产",
        "X08.01.01151": "量产",
        "X08.01.01329": "含风险发布",
        "1.9.10.01.10361": "量产",
        "1.0.01.25.11660": "量产",
        "1.9.10.01.10725": "量产",
        "1.0.99.12.10120": "即将停售",
        "1.2.44.01.17791-000": "量产",
        "X08.02.00069": "即将停售",
        "2.3.02.01.10527": "开发",
        "X08.02.00086": "量产",
        "1.2.01.66.10700": "量产",
        "X08.01.01373": "量产",
        "*********.16682": "量产",
        "1.9.01.01.10018": "量产",
        "X08.02.00144": "量产",
        "X08.01.01549": "开发",
        "2.9.02.02.11655": "量产",
        "1.0.01.26.10847": "含风险发布",
        "1.2.01.66.10698": "开发",
        "X08.01.01282": "量产",
        "2.3.02.01.10896": "开发",
        "X08.01.01137": "量产",
        "X08.02.00072": "即将停售",
        "X08.01.02037": "量产",
        "X08.01.01727": "量产",
        "X08.01.01432": "量产",
        "1.9.03.09.10575": "量产",
        "2.3.02.01.10591": "开发",
        "X08.01.01286": "量产",
        "X08.01.01655": "量产",
        "1.0.01.25.12307": "量产",
        "X08.01.02002": "废弃",
        "X08.01.01265": "量产",
        "1.0.01.09.12822": "量产",
        "1.0.01.18.10918": "量产",
        "2.9.02.02.11057": "量产",
        "2.3.02.01.10410": "开发",
        "X08.01.02038": "量产",
        "X08.01.02052": "量产",
        "*********.14208": "工程样机",
        "1.2.01.23.10152": "开发",
        "X08.01.01300": "量产",
        "X08.01.01017": "量产",
        "X08.01.01138": "量产",
        "X08.01.01104": "量产",
        "X08.01.00730": "量产",
        "X08.01.01206": "量产",
        "1.9.03.07.10029": "量产",
        "1.0.01.02.10627": "量产",
        "X08.01.01198": "量产",
        "X08.01.01707": "量产",
        "1.0.01.04.39733": "量产",
        "X08.01.01279": "量产",
        "1.0.01.04.37892": "量产",
        "1.9.09.01.10084": "量产",
        "1.0.01.25.11383": "量产",
        "1.2.01.23.10146": "开发",
        "2.3.02.01.10589": "开发",
        "X08.01.01706": "量产",
        "X08.01.01177": "量产",
        "X08.02.00093": "量产",
        "1.9.10.01.10363-001": "开发",
        "X08.01.01893": "量产",
        "X08.01.01927": "即将停售",
        "1.0.01.02.11724": "量产",
        "X08.01.02030": "量产",
        "X08.01.02093": "工程样机",
        "X08.01.02019": "即将停售",
        "X08.01.01444": "量产",
        "2.9.02.01.10204": "量产",
        "X08.01.01945": "量产",
        "1.0.01.04.40609": "量产",
        "X08.01.01195": "量产",
        "X08.01.00775": "量产",
        "X08.01.01488": "量产",
        "1.0.01.04.37519": "量产",
        "X08.01.01269": "量产",
        "X08.01.01283": "量产",
        "X08.01.01571": "量产",
        "X08.01.01858": "量产",
        "X08.01.01185": "量产",
        "1.2.01.27.10283": "即将停售",
        "X08.01.01526": "即将停售",
        "1.0.01.19.10280": "量产",
        "X08.01.01689": "量产",
        "1.2.50.10.12262-000": "量产",
        "1.0.01.04.40106": "量产",
        "1.0.01.46.10136": "含风险发布",
        "1.9.03.09.10295-002": "开发",
        "X08.02.00102": "量产",
        "X08.01.01640": "量产",
        "1.9.03.09.10025": "量产",
        "X08.01.01179": "量产",
        "1.0.01.25.11351": "量产",
        "X08.01.02059": "量产",
        "X08.01.00665": "量产",
        "*********.14287": "量产",
        "X08.01.01575": "量产",
        "1.0.01.34.11821": "量产",
        "X08.01.01325": "量产",
        "X08.02.00171": "工程样机",
        "X08.01.01295": "量产",
        "1.2.01.66.10699": "量产",
        "*********.13543": "即将停售",
        "X08.01.01949": "量产",
        "1.2.01.26.10012": "开发",
        "X08.01.01875": "含风险发布",
        "1.9.10.01.10728": "量产",
        "1.0.01.01.15134": "量产",
        "X08.01.01507": "量产",
        "1.0.01.02.10529": "量产",
        "1.2.02.10.10017": "量产",
        "2.9.02.02.11657": "量产",
        "X08.01.01291": "量产",
        "X08.01.01494": "量产",
        "X08.01.01102": "量产",
        "1.1.03.25.10340": "量产",
        "2.3.02.01.10600": "开发",
        "X08.01.02042": "量产",
        "1.0.01.04.42641": "量产",
        "1.0.01.14.12180": "量产",
        "X08.01.01331": "量产",
        "X08.01.01031": "量产",
        "X08.01.00846": "量产",
        "1.2.23.02.10429": "量产",
        "X08.01.01070": "量产",
        "1.0.01.25.11605": "量产",
        "1.0.01.14.11852": "即将停售",
        "X08.01.01789": "量产",
        "1.2.01.66.10688": "量产",
        "X08.02.00085": "量产",
        "X08.01.01961": "量产",
        "1.0.01.04.33464": "量产",
        "*********.14173": "量产",
        "1.0.01.04.38559": "量产",
        "X08.01.01447": "量产",
        "1.2.01.66.10695": "量产",
        "X08.01.01865": "量产",
        "X08.01.02095": "量产",
        "X08.01.01698": "量产",
        "1.0.01.13.12463": "即将停售",
        "X08.01.01951": "量产",
        "1.0.01.20.11228": "量产",
        "1.0.01.04.37517": "量产",
        "X08.02.00143": "量产",
        "*********.13795": "即将停售",
        "X08.01.01393": "开发",
        "1.0.01.46.10104": "原型机",
        "1.9.10.01.10733": "量产",
        "X08.01.01598": "量产",
        "X08.01.01386": "量产",
        "X08.01.01654": "量产",
        "1.0.01.04.40121": "量产",
        "X08.01.01277": "量产",
        "1.0.01.04.44340": "量产",
        "2.9.02.01.10211": "量产",
        "X08.01.01147": "量产",
        "X08.01.01645": "量产",
        "X08.01.01492": "量产",
        "X08.01.00916": "开发",
        "X08.01.02021": "开发",
        "X08.01.00671": "量产",
        "X08.01.00932": "量产",
        "X08.01.01483": "量产",
        "1.9.03.09.10016": "量产",
        "X08.01.00769": "量产",
        "1.0.01.46.10273": "工程样机",
        "1.0.01.04.39766": "量产",
        "1.0.01.09.14548": "量产",
        "1.2.01.19.10038": "开发",
        "X08.01.01911": "量产",
        "X08.01.01126": "量产",
        "X08.01.01957": "量产",
        "X08.01.02010": "量产",
        "X08.02.00098": "即将停售",
        "X08.01.01322": "开发",
        "2.3.02.01.10588": "开发",
        "X08.01.01164": "量产",
        "X08.01.01172": "量产",
        "1.2.02.05.10063": "开发",
        "1.2.01.23.10063": "开发",
        "1.0.01.09.14546": "量产",
        "1.0.01.04.44806": "量产",
        "1.0.01.18.10828": "量产",
        "*********.14210": "量产",
        "X08.01.01345": "量产",
        "X08.01.01499": "量产",
        "1.0.01.04.40310": "量产",
        "X08.01.01935": "量产",
        "X08.01.01316": "量产",
        "1.0.99.22.10057": "量产",
        "X08.01.01487": "量产",
        "1.2.01.66.10691": "量产",
        "X08.01.00912": "量产",
        "X08.01.01443": "量产",
        "X08.01.01194": "量产",
        "1.2.01.67.10489": "量产",
        "X08.01.01285": "量产",
        "1.2.01.66.10692": "量产",
        "1.9.10.01.10898": "开发",
        "2.9.01.11.10004": "量产",
        "1.2.01.23.10046": "开发",
        "X08.01.01959": "量产",
        "1.0.99.01.10236": "量产",
        "1.2.01.20.10109": "即将停售",
        "X08.01.00885": "量产",
        "X08.01.00884": "量产",
        "X08.01.01829": "量产",
        "X08.01.01296": "量产",
        "1.0.01.09.15199": "量产",
        "2.3.02.01.10599": "开发",
        "2.3.02.01.10593": "开发",
        "X08.01.01943": "量产",
        "X08.01.01532": "量产"
};
        let currentScheme = null;
        let currentConfig = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSchemeSelect();
            setupEventListeners();
        });

        function initializeSchemeSelect() {
            const select = document.getElementById('schemeSelect');
            select.innerHTML = '<option value="">请选择方案...</option>';
            
            for (const [code, scheme] of Object.entries(schemesData)) {
                const option = document.createElement('option');
                option.value = code;
                option.textContent = scheme.name;
                select.appendChild(option);
            }
        }

        function setupEventListeners() {
            document.getElementById('schemeSelect').addEventListener('change', onSchemeChange);
            document.getElementById('configType').addEventListener('change', onConfigChange);
            document.getElementById('lifecycleFilter').addEventListener('change', filterProducts);
            document.getElementById('searchInput').addEventListener('input', function() {
                searchProducts(this.value);
            });
            document.getElementById('fileUpload').addEventListener('change', handleFileUpload);
        }

        function onSchemeChange() {
            const schemeCode = document.getElementById('schemeSelect').value;
            if (!schemeCode) {
                document.getElementById('configType').disabled = true;
                document.getElementById('configType').innerHTML = '<option value="">请先选择方案</option>';
                hideTabsAndProducts();
                return;
            }

            currentScheme = schemesData[schemeCode];
            
            // 更新配置选择
            const configSelect = document.getElementById('configType');
            configSelect.disabled = false;
            configSelect.innerHTML = '<option value="">请选择配置...</option>';
            
            // 查找高配/低配选项
            const level1Items = currentScheme.hierarchy["1"] || [];
            level1Items.forEach(item => {
                const option = document.createElement('option');
                option.value = item.code;
                option.textContent = item.description;
                configSelect.appendChild(option);
            });
            
            hideTabsAndProducts();
        }

        function onConfigChange() {
            const configCode = document.getElementById('configType').value;
            if (!configCode || !currentScheme) {
                hideTabsAndProducts();
                return;
            }

            currentConfig = configCode;
            renderTabs();
            showTabsAndProducts();
            updateStatistics();
        }

        function renderTabs() {
            const tabNav = document.getElementById('tabNavigation');
            tabNav.innerHTML = '';
            
            const level2Items = currentScheme.hierarchy["2"] || [];
            
            level2Items.forEach((item, index) => {
                const tabButton = document.createElement('button');
                tabButton.className = `py-3 px-3 border-b-2 font-medium text-xs whitespace-nowrap ${index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`;
                tabButton.textContent = item.description;
                tabButton.onclick = () => switchTab(item.code, tabButton);
                tabNav.appendChild(tabButton);
            });
            
            // 默认显示第一个页签
            if (level2Items.length > 0) {
                renderProductTable(level2Items[0].code);
            }
        }

        function switchTab(moduleCode, button) {
            // 更新按钮状态
            document.querySelectorAll('#tabNavigation button').forEach(btn => {
                btn.className = btn.className.replace('border-blue-500 text-blue-600', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
            });
            button.className = button.className.replace('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'border-blue-500 text-blue-600');
            
            renderProductTable(moduleCode);
        }

        function renderProductTable(moduleCode) {
            // 第三级：页签对应的子场景
            const level3Items = (currentScheme.hierarchy["3"] || []).filter(item => item.parent === moduleCode);
            // 第四级：具体设备清单
            const level4Items = currentScheme.hierarchy["4"] || [];
            
            let html = `
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0">
                        <tr>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">序号</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">子场景</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">产品简称</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">物料代码</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">型号</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">品牌</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">数量</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">单位</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">是否必配</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">安装位置及用途</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">生命周期</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">备注说明</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
            `;
            
            let sequence = 1;
            
            // 遍历第三级（子场景）
            level3Items.forEach(level3Item => {
                // 查找属于这个子场景的第四级设备
                const childDevices = level4Items.filter(item => item.parent === level3Item.code);
                
                if (childDevices.length > 0) {
                    // 显示第四级设备清单
                    childDevices.forEach(device => {
                        const lifecycle = getProductLifecycle(device.code);
                        const lifecycleClass = `lifecycle-${lifecycle}`;
                        
                        html += `
                            <tr class="product-row hover:bg-gray-50" data-lifecycle="${lifecycle}">
                                <td class="px-3 py-2 text-xs text-gray-900">${sequence}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${level3Item.description}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.description}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.code}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.model}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.brand}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.quantity}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.unit}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.required}</td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.location}</td>
                                <td class="px-3 py-2">
                                    <span class="lifecycle-badge ${lifecycleClass}">${lifecycle}</span>
                                </td>
                                <td class="px-3 py-2 text-xs text-gray-900">${device.remark}</td>
                            </tr>
                        `;
                        sequence++;
                    });
                } else {
                    // 如果第三级没有对应的第四级设备，显示第三级本身作为设备
                    const lifecycle = getProductLifecycle(level3Item.code);
                    const lifecycleClass = `lifecycle-${lifecycle}`;
                    
                    html += `
                        <tr class="product-row hover:bg-gray-50" data-lifecycle="${lifecycle}">
                            <td class="px-3 py-2 text-xs text-gray-900">${sequence}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.description}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.description}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.code}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.model}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.brand}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.quantity}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.unit}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.required}</td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.location}</td>
                            <td class="px-3 py-2">
                                <span class="lifecycle-badge ${lifecycleClass}">${lifecycle}</span>
                            </td>
                            <td class="px-3 py-2 text-xs text-gray-900">${level3Item.remark}</td>
                        </tr>
                    `;
                    sequence++;
                }
            });
            
            // 如果没有找到任何数据，显示提示信息
            if (sequence === 1) {
                html += `
                    <tr>
                        <td colspan="12" class="px-3 py-8 text-center text-gray-500">
                            <i class="fas fa-inbox text-2xl mb-2"></i>
                            <div>该模块暂无设备清单数据</div>
                            <div class="text-xs mt-1">请检查数据或选择其他模块</div>
                        </td>
                    </tr>
                `;
            }
            
            html += `
                    </tbody>
                </table>
            `;
            
            document.getElementById('productTable').innerHTML = html;
        }

        function getProductLifecycle(productCode) {
            return lifecycleData[productCode] || '量产';
        }

        function updateStatistics() {
            if (!currentScheme) return;
            
            const allProducts = [
                ...(currentScheme.hierarchy["3"] || []),
                ...(currentScheme.hierarchy["4"] || [])
            ];
            
            const totalCount = allProducts.length;
            const activeCount = allProducts.filter(p => getProductLifecycle(p.code) === '量产').length;
            const sampleCount = allProducts.filter(p => getProductLifecycle(p.code) === '工程样机').length;
            const retiredCount = allProducts.filter(p => ['停产', '退市'].includes(getProductLifecycle(p.code))).length;
            
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('activeCount').textContent = activeCount;
            document.getElementById('sampleCount').textContent = sampleCount;
            document.getElementById('retiredCount').textContent = retiredCount;
        }

        function filterProducts() {
            const lifecycle = document.getElementById('lifecycleFilter').value;
            const rows = document.querySelectorAll('.product-row');
            
            rows.forEach(row => {
                if (!lifecycle || row.dataset.lifecycle === lifecycle) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function searchProducts(query) {
            const rows = document.querySelectorAll('.product-row');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (!query || text.includes(query.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {type: 'array'});
                    const sheetName = workbook.SheetNames.find(name => name.includes('方案产品表格')) || workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);
                    
                    // 更新生命周期数据
                    jsonData.forEach(row => {
                        const optionCode = row['选件编号'];
                        const productCode = row['产品编号'];
                        const lifecycle = row['生命周期'];
                        
                        if (optionCode && lifecycle) {
                            lifecycleData[optionCode] = lifecycle;
                        }
                        if (productCode && lifecycle) {
                            lifecycleData[productCode] = lifecycle;
                        }
                    });
                    
                    // 重新渲染当前页签
                    if (currentScheme && currentConfig) {
                        const activeTab = document.querySelector('#tabNavigation button.border-blue-500');
                        if (activeTab) {
                            const moduleCode = Array.from(document.querySelectorAll('#tabNavigation button')).indexOf(activeTab);
                            const level2Items = currentScheme.hierarchy["2"] || [];
                            if (level2Items[moduleCode]) {
                                renderProductTable(level2Items[moduleCode].code);
                            }
                        }
                        updateStatistics();
                    }
                    
                    alert('生命周期数据更新成功！');
                } catch (error) {
                    console.error('文件读取错误:', error);
                    alert('文件读取失败，请检查文件格式！');
                }
            };
            reader.readAsArrayBuffer(file);
        }

        function hideTabsAndProducts() {
            document.getElementById('tabContainer').style.display = 'none';
            document.getElementById('productContainer').style.display = 'none';
        }

        function showTabsAndProducts() {
            document.getElementById('tabContainer').style.display = 'block';
            document.getElementById('productContainer').style.display = 'block';
        }
    </script>
</body>
</html>