<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案清单可视化系统</title>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .lifecycle-badge { @apply px-2 py-1 rounded-full text-xs font-medium; }
        .lifecycle-量产 { @apply bg-green-100 text-green-800; }
        .lifecycle-工程样机 { @apply bg-yellow-100 text-yellow-800; }
        .lifecycle-停产 { @apply bg-red-100 text-red-800; }
        .lifecycle-退市 { @apply bg-gray-100 text-gray-800; }
        .lifecycle-开发 { @apply bg-blue-100 text-blue-800; }
        .search-highlight { background-color: yellow; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <!-- 头部 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-list-alt mr-2"></i>方案清单可视化系统
            </h1>
            
            <!-- 控制面板 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">配置类型:</label>
                    <select id="configType" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="high">高配</option>
                        <option value="low">低配</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">生命周期:</label>
                    <select id="lifecycleFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="">全部</option>
                        <option value="量产">量产</option>
                        <option value="工程样机">工程样机</option>
                        <option value="停产">停产</option>
                        <option value="退市">退市</option>
                        <option value="开发">开发</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">搜索:</label>
                    <input type="text" id="searchInput" placeholder="搜索产品..." 
                           class="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1">
                </div>
                
                <div class="flex items-center space-x-2">
                    <label for="fileUpload" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md cursor-pointer text-sm">
                        <i class="fas fa-upload mr-1"></i>更新数据
                    </label>
                    <input type="file" id="fileUpload" accept=".xlsx,.xls" class="hidden">
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                    <div class="text-sm text-gray-600">总产品数</div>
                </div>
                <div class="bg-green-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="activeCount">0</div>
                    <div class="text-sm text-gray-600">量产产品</div>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600" id="sampleCount">0</div>
                    <div class="text-sm text-gray-600">工程样机</div>
                </div>
                <div class="bg-red-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-red-600" id="retiredCount">0</div>
                    <div class="text-sm text-gray-600">停产/退市</div>
                </div>
                <div class="bg-purple-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600" id="replacedCount">0</div>
                    <div class="text-sm text-gray-600">今日替换</div>
                </div>
            </div>
        </div>

        <!-- 页签导航 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6 overflow-x-auto" id="tabNavigation">
                    <!-- 动态生成页签 -->
                </nav>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div id="tabContent">
                <!-- 动态生成内容 -->
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let hierarchyData = {"1": [{"code": "X08.01.02002", "description": "EDR杀毒软件", "level": "1"}, {"code": "X08.01.02003", "description": "日志审计", "level": "1"}], "2": [{"code": "X08.01.00665", "description": "登记室", "level": "2"}, {"code": "X08.01.00666", "description": "人行通道", "level": "2"}, {"code": "X08.01.00667", "description": "车行通道", "level": "2"}, {"code": "X08.01.00668", "description": "平台软件", "level": "2"}, {"code": "X08.01.00669", "description": "配套服务器", "level": "2"}, {"code": "X08.01.00670", "description": "视频智能分析系统", "level": "2"}, {"code": "X08.01.00671", "description": "存储", "level": "2"}, {"code": "X08.01.00672", "description": "网关", "level": "2"}, {"code": "X08.01.00673", "description": "1.1 本地部署&存储-防逃脱系统", "level": "2"}, {"code": "X08.01.00674", "description": "1.2 本地部署&存储-车载系统", "level": "2"}, {"code": "X08.01.00675", "description": "1.3 本地部署&存储-移动监控系统", "level": "2"}, {"code": "X08.01.00676", "description": "1.4 本地部署&存储-管理平台", "level": "2"}, {"code": "X08.01.00677", "description": "2.1 互联网部署&存储-防逃脱系统", "level": "2"}, {"code": "X08.01.00678", "description": "2.2 互联网部署&存储-车载系统", "level": "2"}, {"code": "X08.01.00679", "description": "2.3 互联网部署&存储-移动监控系统", "level": "2"}, {"code": "X08.01.00680", "description": "2.4 互联网部署&存储-管理平台", "level": "2"}, {"code": "X08.01.00729", "description": "音视频监控", "level": "2"}, {"code": "X08.01.00730", "description": "报警设备", "level": "2"}, {"code": "X08.01.00732", "description": "场所门禁", "level": "2"}, {"code": "X08.01.00735", "description": "音视频监控", "level": "2"}, {"code": "X08.01.00736", "description": "报警设备", "level": "2"}, {"code": "X08.01.00737", "description": "场所门禁", "level": "2"}, {"code": "X08.01.00739", "description": "音视频监控", "level": "2"}, {"code": "X08.01.00740", "description": "报警设备", "level": "2"}, {"code": "X08.01.00741", "description": "场所门禁", "level": "2"}, {"code": "X08.01.00743", "description": "情绪识别", "level": "2"}, {"code": "X08.01.00745", "description": "音视频监控", "level": "2"}, {"code": "X08.01.00746", "description": "报警设备", "level": "2"}, {"code": "X08.01.00747", "description": "场所门禁", "level": "2"}, {"code": "X08.01.00748", "description": "远程教育及教学辅助", "level": "2"}, {"code": "X08.01.00755", "description": "移动执法", "level": "2"}, {"code": "X08.01.00756", "description": "车载套件", "level": "2"}, {"code": "X08.01.00758", "description": "音视频监控", "level": "2"}, {"code": "X08.01.00759", "description": "报警设备", "level": "2"}, {"code": "X08.01.00761", "description": "场所门禁", "level": "2"}, {"code": "X08.01.00762", "description": "无感签到显示设备（人脸实时监控）", "level": "2"}, {"code": "X08.01.00764", "description": "显控产品", "level": "2"}, {"code": "X08.01.00766", "description": "视频会议", "level": "2"}, {"code": "X08.01.00767", "description": "报警设备", "level": "2"}, {"code": "X08.01.00768", "description": "融合通信网关", "level": "2"}, {"code": "X08.01.00769", "description": "调度台", "level": "2"}, {"code": "X08.01.00771", "description": "融合通信软件", "level": "2"}, {"code": "X08.01.00773", "description": "集中存储", "level": "2"}, {"code": "X08.01.00774", "description": "人脸比对服务器", "level": "2"}, {"code": "X08.01.00775", "description": "行为分析服务器", "level": "2"}, {"code": "X08.01.00776", "description": "智慧矫正远程视频督察平台", "level": "2"}, {"code": "X08.01.00777", "description": "平台服务器", "level": "2"}, {"code": "X08.01.00779", "description": "国标联网网关", "level": "2"}, {"code": "X08.01.00780", "description": "智能运维", "level": "2"}, {"code": "X08.01.00816", "description": "中心平台服务器", "level": "2"}, {"code": "X08.01.00817", "description": "共享法庭移动端", "level": "2"}, {"code": "X08.01.00818", "description": "安防监控系统", "level": "2"}, {"code": "X08.01.00843", "description": "监室", "level": "2"}, {"code": "X08.01.00844", "description": "周界", "level": "2"}, {"code": "X08.01.00845", "description": "AB门", "level": "2"}, {"code": "X08.01.00846", "description": "制高点AR云景", "level": "2"}, {"code": "X08.01.00847", "description": "厂房", "level": "2"}, {"code": "X08.01.00848", "description": "监外公共区域", "level": "2"}, {"code": "X08.01.00849", "description": "会见区域", "level": "2"}, {"code": "X08.01.00850", "description": "伙房", "level": "2"}, {"code": "X08.01.00855", "description": "门锁控制", "level": "2"}, {"code": "X08.01.00856", "description": "公共设备", "level": "2"}, {"code": "X08.01.00857", "description": "人脸巡更", "level": "2"}, {"code": "X08.01.00858", "description": "传统门禁巡更", "level": "2"}, {"code": "X08.01.00859", "description": "紧急报警", "level": "2"}, {"code": "X08.01.00860", "description": "电子围栏", "level": "2"}, {"code": "X08.01.00861", "description": "红外对射", "level": "2"}, {"code": "X08.01.00865", "description": "安检系统", "level": "2"}, {"code": "X08.01.00877", "description": "相机", "level": "2"}, {"code": "X08.01.00879", "description": "拾音器", "level": "2"}, {"code": "X08.01.00880", "description": "温湿度", "level": "2"}, {"code": "X08.01.00881", "description": "审讯机", "level": "2"}, {"code": "X08.01.00882", "description": "示证", "level": "2"}, {"code": "X08.01.00883", "description": "报警", "level": "2"}, {"code": "X08.01.00884", "description": "平台", "level": "2"}, {"code": "X08.01.00885", "description": "情绪识别", "level": "2"}, {"code": "X08.01.00886", "description": "登记审批办证", "level": "2"}, {"code": "X08.01.00887", "description": "辊闸门", "level": "2"}, {"code": "X08.01.00888", "description": "人行通道AB门及虚拟换证（项目定制虚拟换证逻辑，如四门控制器控制ABCD四门，B门接换卡进出读卡器，换卡进刷了后，删除A门权限，下发CD门门禁权限。换卡出刷了后，删除CD门权限，下发A门权限。（南京女子监狱项目已定制）实际根据ACD门分布调整接线即可。若AB门超过三个门，则需要八门控制器重新定制。）", "level": "2"}, {"code": "X08.01.00889", "description": "其他门禁设备", "level": "2"}, {"code": "X08.01.00890", "description": "车牌识别及车底扫描成像系统", "level": "2"}, {"code": "X08.01.00891", "description": "升降柱", "level": "2"}, {"code": "X08.01.00892", "description": "AB门人行值班室", "level": "2"}, {"code": "X08.01.00893", "description": "车辆进出管理系统", "level": "2"}, {"code": "X08.01.00895", "description": "罪犯防脱逃预警系统", "level": "2"}, {"code": "X08.01.00898", "description": "人车区域管控", "level": "2"}, {"code": "X08.01.00899", "description": "监舍点名(三选一)", "level": "2"}, {"code": "X08.01.00900", "description": "工间点名", "level": "2"}, {"code": "X08.01.00901", "description": "出收工点名（二选一）", "level": "2"}, {"code": "X08.01.00902", "description": "零星流动", "level": "2"}, {"code": "X08.01.00904", "description": "管控终端（服药及夜巡APP载体）", "level": "2"}, {"code": "X08.01.00906", "description": "罪犯夜巡巡查点", "level": "2"}, {"code": "X08.01.00907", "description": "后端设备", "level": "2"}, {"code": "X08.01.00908", "description": "融合通信平台软件X9000相关模块", "level": "2"}, {"code": "X08.01.00909", "description": "MCU", "level": "2"}, {"code": "X08.01.00910", "description": "高性能融合通信网关", "level": "2"}, {"code": "X08.01.00911", "description": "经济型融合通信网关", "level": "2"}, {"code": "X08.01.00912", "description": "集群音频接入网关", "level": "2"}, {"code": "X08.01.00913", "description": "调度台", "level": "2"}, {"code": "X08.01.00914", "description": "普通机架式服务器", "level": "2"}, {"code": "X08.01.00915", "description": "超融合私有云", "level": "2"}, {"code": "X08.01.00916", "description": "服务器", "level": "2"}, {"code": "X08.01.00917", "description": "云存储", "level": "2"}, {"code": "X08.01.00919", "description": "国标联网网关", "level": "2"}, {"code": "X08.01.00921", "description": "业务数据对接网关", "level": "2"}, {"code": "X08.01.00923", "description": "安防管理", "level": "2"}, {"code": "X08.01.00924", "description": "智能管控", "level": "2"}, {"code": "X08.01.00925", "description": "应急指挥", "level": "2"}, {"code": "X08.01.00927", "description": "纯软模块化", "level": "2"}, {"code": "X08.01.00928", "description": "纯软平台", "level": "2"}, {"code": "X08.01.00929", "description": "服务器纯硬件", "level": "2"}, {"code": "X08.01.00930", "description": "软硬一体", "level": "2"}, {"code": "X08.01.00931", "description": "纯软", "level": "2"}, {"code": "X08.01.00932", "description": "纯硬件", "level": "2"}, {"code": "X08.01.00936", "description": "监室", "level": "2"}, {"code": "X08.01.00937", "description": "周界", "level": "2"}, {"code": "X08.01.00938", "description": "AB门", "level": "2"}, {"code": "X08.01.00939", "description": "厂房", "level": "2"}, {"code": "X08.01.00940", "description": "监外公共区域", "level": "2"}, {"code": "X08.01.00941", "description": "会见区域", "level": "2"}, {"code": "X08.01.00942", "description": "伙房", "level": "2"}, {"code": "X08.01.00946", "description": "事件监测智能分析", "level": "2"}, {"code": "X08.01.00947", "description": "门锁控制", "level": "2"}, {"code": "X08.01.00948", "description": "公共设备", "level": "2"}, {"code": "X08.01.00949", "description": "人脸巡更", "level": "2"}, {"code": "X08.01.00950", "description": "传统门禁巡更", "level": "2"}, {"code": "X08.01.00951", "description": "紧急报警", "level": "2"}, {"code": "X08.01.00952", "description": "电子围栏", "level": "2"}, {"code": "X08.01.00953", "description": "红外对射", "level": "2"}, {"code": "X08.01.00969", "description": "相机", "level": "2"}, {"code": "X08.01.00971", "description": "拾音器", "level": "2"}, {"code": "X08.01.00972", "description": "温湿度", "level": "2"}, {"code": "X08.01.00973", "description": "审讯机", "level": "2"}, {"code": "X08.01.00974", "description": "示证", "level": "2"}, {"code": "X08.01.00975", "description": "报警", "level": "2"}, {"code": "X08.01.00976", "description": "平台", "level": "2"}, {"code": "X08.01.00977", "description": "情绪识别", "level": "2"}, {"code": "X08.01.00978", "description": "安检系统", "level": "2"}, {"code": "X08.01.00979", "description": "登记审批办证", "level": "2"}, {"code": "X08.01.00980", "description": "辊闸门", "level": "2"}, {"code": "X08.01.00981", "description": "人行通道AB门及虚拟换证", "level": "2"}, {"code": "X08.01.00982", "description": "其他门禁设备", "level": "2"}, {"code": "X08.01.00983", "description": "车牌识别及车底扫描成像系统", "level": "2"}, {"code": "X08.01.00984", "description": "升降柱", "level": "2"}, {"code": "X08.01.00985", "description": "AB门人行值班室", "level": "2"}, {"code": "X08.01.00986", "description": "车辆进出管理系统", "level": "2"}, {"code": "X08.01.00988", "description": "罪犯防脱逃预警系统", "level": "2"}, {"code": "X08.01.00989", "description": "人车区域管控", "level": "2"}, {"code": "X08.01.01011", "description": "固定谈话场景", "level": "2"}, {"code": "X08.01.01012", "description": "门禁系统", "level": "2"}, {"code": "X08.01.01013", "description": "外出办案谈话", "level": "2"}, {"code": "X08.01.01014", "description": "固定谈话场景", "level": "2"}, {"code": "X08.01.01016", "description": "平台软件模块", "level": "2"}, {"code": "X08.01.01017", "description": "配套服务器", "level": "2"}, {"code": "X08.01.01020", "description": "配套服务器", "level": "2"}, {"code": "X08.01.01021", "description": "平台软件模块", "level": "2"}, {"code": "X08.01.01022", "description": "视频质量诊断一体机", "level": "2"}, {"code": "X08.01.01026", "description": "平台软件模块", "level": "2"}, {"code": "X08.01.01027", "description": "配套服务器", "level": "2"}, {"code": "X08.01.01029", "description": "配套服务器", "level": "2"}, {"code": "X08.01.01030", "description": "平台软件模块", "level": "2"}, {"code": "X08.01.01031", "description": "视频质量诊断一体机", "level": "2"}, {"code": "X08.01.01046", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01047", "description": "报警设备", "level": "2"}, {"code": "X08.01.01048", "description": "无感签到屏", "level": "2"}, {"code": "X08.01.01050", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01051", "description": "报警设备", "level": "2"}, {"code": "X08.01.01053", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01054", "description": "报警设备", "level": "2"}, {"code": "X08.01.01056", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01057", "description": "报警设备", "level": "2"}, {"code": "X08.01.01061", "description": "心理矫正系统", "level": "2"}, {"code": "X08.01.01064", "description": "移动执法", "level": "2"}, {"code": "X08.01.01065", "description": "车载套件", "level": "2"}, {"code": "X08.01.01068", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01069", "description": "报警设备", "level": "2"}, {"code": "X08.01.01070", "description": "无感签到显示设备（人脸实时监控）", "level": "2"}, {"code": "X08.01.01072", "description": "显控产品", "level": "2"}, {"code": "X08.01.01074", "description": "视频会议", "level": "2"}, {"code": "X08.01.01075", "description": "报警设备", "level": "2"}, {"code": "X08.01.01077", "description": "集中存储", "level": "2"}, {"code": "X08.01.01078", "description": "人脸比对服务器", "level": "2"}, {"code": "X08.01.01079", "description": "行为分析服务器", "level": "2"}, {"code": "X08.01.01080", "description": "智慧矫正远程视频督察平台", "level": "2"}, {"code": "X08.01.01081", "description": "平台服务器", "level": "2"}, {"code": "X08.01.01083", "description": "国标联网网关", "level": "2"}, {"code": "X08.01.01084", "description": "智能运维", "level": "2"}, {"code": "X08.01.01087", "description": "无感签到屏", "level": "2"}, {"code": "X08.01.01089", "description": "心理矫正系统", "level": "2"}, {"code": "X08.01.01094", "description": "民警办证", "level": "2"}, {"code": "X08.01.01095", "description": "储物管理", "level": "2"}, {"code": "X08.01.01097", "description": "辊闸门", "level": "2"}, {"code": "X08.01.01098", "description": "人行通道AB门及虚拟换证", "level": "2"}, {"code": "X08.01.01099", "description": "其他门禁设备", "level": "2"}, {"code": "X08.01.01102", "description": "门内抓拍机", "level": "2"}, {"code": "X08.01.01103", "description": "门外抓拍机", "level": "2"}, {"code": "X08.01.01104", "description": "车底扫描", "level": "2"}, {"code": "X08.01.01106", "description": "升降柱", "level": "2"}, {"code": "X08.01.01109", "description": "值班室辅助监视器", "level": "2"}, {"code": "X08.01.01111", "description": "车行通道人员核验（2选1）", "level": "2"}, {"code": "X08.01.01113", "description": "车行通道辅助监视器", "level": "2"}, {"code": "X08.01.01115", "description": "罪犯出监", "level": "2"}, {"code": "X08.01.01117", "description": "后智能", "level": "2"}, {"code": "X08.01.01118", "description": "前智能", "level": "2"}, {"code": "X08.01.01120", "description": "智能安检", "level": "2"}, {"code": "X08.01.01122", "description": "生命探测仪", "level": "2"}, {"code": "X08.01.01125", "description": "平台模块", "level": "2"}, {"code": "X08.01.01126", "description": "集中存储ipsan", "level": "2"}, {"code": "X08.01.01127", "description": "服务器", "level": "2"}, {"code": "X08.01.01130", "description": "民警办证", "level": "2"}, {"code": "X08.01.01132", "description": "辊闸门", "level": "2"}, {"code": "X08.01.01133", "description": "人行通道AB门及虚拟换证", "level": "2"}, {"code": "X08.01.01134", "description": "其他门禁设备", "level": "2"}, {"code": "X08.01.01136", "description": "车底扫描", "level": "2"}, {"code": "X08.01.01137", "description": "门内抓拍机", "level": "2"}, {"code": "X08.01.01138", "description": "门外抓拍机", "level": "2"}, {"code": "X08.01.01140", "description": "升降柱", "level": "2"}, {"code": "X08.01.01143", "description": "值班室辅助监视器", "level": "2"}, {"code": "X08.01.01145", "description": "车行通道人员核验", "level": "2"}, {"code": "X08.01.01147", "description": "车行通道辅助监视器", "level": "2"}, {"code": "X08.01.01149", "description": "罪犯出监", "level": "2"}, {"code": "X08.01.01151", "description": "后智能", "level": "2"}, {"code": "X08.01.01152", "description": "前智能", "level": "2"}, {"code": "X08.01.01154", "description": "智能安检", "level": "2"}, {"code": "X08.01.01156", "description": "平台模块", "level": "2"}, {"code": "X08.01.01157", "description": "集中存储ipsan", "level": "2"}, {"code": "X08.01.01158", "description": "服务器", "level": "2"}, {"code": "X08.01.01161", "description": "箱面抓拍系统", "level": "2"}, {"code": "X08.01.01162", "description": "出入口道闸", "level": "2"}, {"code": "X08.01.01164", "description": "电子车牌读写器", "level": "2"}, {"code": "X08.01.01165", "description": "智能网络摄像机", "level": "2"}, {"code": "X08.01.01167", "description": "出入口道闸", "level": "2"}, {"code": "X08.01.01169", "description": "通道监控", "level": "2"}, {"code": "X08.01.01172", "description": "闸机头", "level": "2"}, {"code": "X08.01.01173", "description": "访客机", "level": "2"}, {"code": "X08.01.01177", "description": "结构化枪球一体", "level": "2"}, {"code": "X08.01.01178", "description": "结构化枪机", "level": "2"}, {"code": "X08.01.01179", "description": "人脸警戒枪机", "level": "2"}, {"code": "X08.01.01180", "description": "雷球联动", "level": "2"}, {"code": "X08.01.01182", "description": "智能网络摄像机", "level": "2"}, {"code": "X08.01.01185", "description": "网络硬盘录像机", "level": "2"}, {"code": "X08.01.01186", "description": "物流分析一体机", "level": "2"}, {"code": "X08.01.01187", "description": "月台显示屏", "level": "2"}, {"code": "X08.01.01188", "description": "查验区监管", "level": "2"}, {"code": "X08.01.01190", "description": "装卸区", "level": "2"}, {"code": "X08.01.01191", "description": "查验区", "level": "2"}, {"code": "X08.01.01192", "description": "理货区", "level": "2"}, {"code": "X08.01.01193", "description": "堆存区", "level": "2"}, {"code": "X08.01.01194", "description": "货物出入口", "level": "2"}, {"code": "X08.01.01195", "description": "人员进出通道", "level": "2"}, {"code": "X08.01.01197", "description": "出入口道闸", "level": "2"}, {"code": "X08.01.01198", "description": "视频监控", "level": "2"}, {"code": "X08.01.01199", "description": "全景守望者", "level": "2"}, {"code": "X08.01.01200", "description": "技术用房及出入口", "level": "2"}, {"code": "X08.01.01202", "description": "园区道路管理", "level": "2"}, {"code": "X08.01.01204", "description": "装卸区", "level": "2"}, {"code": "X08.01.01205", "description": "查验区", "level": "2"}, {"code": "X08.01.01206", "description": "理货区", "level": "2"}, {"code": "X08.01.01207", "description": "堆存区", "level": "2"}, {"code": "X08.01.01208", "description": "货物出入口", "level": "2"}, {"code": "X08.01.01209", "description": "人员进出通道", "level": "2"}, {"code": "X08.01.01210", "description": "保税仓安检", "level": "2"}, {"code": "X08.01.01211", "description": "保税仓监控室", "level": "2"}, {"code": "X08.01.01213", "description": "服务器", "level": "2"}, {"code": "X08.01.01214", "description": "智能物联综合管理平台（icc-b8900-u）", "level": "2"}, {"code": "X08.01.01215", "description": "增值模块-智能应用", "level": "2"}, {"code": "X08.01.01216", "description": "增值模块-安防应用", "level": "2"}, {"code": "X08.01.01217", "description": "增值应用-行政办公", "level": "2"}, {"code": "X08.01.01218", "description": "月台管理应用", "level": "2"}, {"code": "X08.01.01219", "description": "消防管理应用", "level": "2"}, {"code": "X08.01.01262", "description": "箱面抓拍系统", "level": "2"}, {"code": "X08.01.01263", "description": "出入口道闸", "level": "2"}, {"code": "X08.01.01265", "description": "电子车牌读写器", "level": "2"}, {"code": "X08.01.01266", "description": "智能网络摄像机", "level": "2"}, {"code": "X08.01.01268", "description": "出入口道闸", "level": "2"}, {"code": "X08.01.01269", "description": "通道监控", "level": "2"}, {"code": "X08.01.01272", "description": "闸机头", "level": "2"}, {"code": "X08.01.01273", "description": "访客机", "level": "2"}, {"code": "X08.01.01275", "description": "结构化枪球一体", "level": "2"}, {"code": "X08.01.01276", "description": "雷球联动", "level": "2"}, {"code": "X08.01.01277", "description": "智能网络摄像机", "level": "2"}, {"code": "X08.01.01279", "description": "查验区监管", "level": "2"}, {"code": "X08.01.01281", "description": "装卸区", "level": "2"}, {"code": "X08.01.01282", "description": "查验区", "level": "2"}, {"code": "X08.01.01283", "description": "理货区", "level": "2"}, {"code": "X08.01.01284", "description": "堆存区", "level": "2"}, {"code": "X08.01.01285", "description": "货物出入口", "level": "2"}, {"code": "X08.01.01286", "description": "人员进出通道", "level": "2"}, {"code": "X08.01.01288", "description": "出入口道闸", "level": "2"}, {"code": "X08.01.01289", "description": "视频监控", "level": "2"}, {"code": "X08.01.01290", "description": "全景守望者", "level": "2"}, {"code": "X08.01.01291", "description": "技术用房及出入口", "level": "2"}, {"code": "X08.01.01293", "description": "园区道路管理", "level": "2"}, {"code": "X08.01.01295", "description": "装卸区", "level": "2"}, {"code": "X08.01.01296", "description": "查验区", "level": "2"}, {"code": "X08.01.01297", "description": "理货区", "level": "2"}, {"code": "X08.01.01298", "description": "堆存区", "level": "2"}, {"code": "X08.01.01299", "description": "货物出入口", "level": "2"}, {"code": "X08.01.01300", "description": "人员进出通道", "level": "2"}, {"code": "X08.01.01301", "description": "保税仓安检", "level": "2"}, {"code": "X08.01.01302", "description": "保税仓监控室", "level": "2"}, {"code": "X08.01.01304", "description": "服务器", "level": "2"}, {"code": "X08.01.01305", "description": "智能物联综合管理平台（icc-b8900-u）", "level": "2"}, {"code": "X08.01.01306", "description": "增值模块-智能应用", "level": "2"}, {"code": "X08.01.01307", "description": "增值模块-安防应用", "level": "2"}, {"code": "X08.01.01308", "description": "增值应用-行政办公", "level": "2"}, {"code": "X08.01.01309", "description": "月台管理应用", "level": "2"}, {"code": "X08.01.01310", "description": "消防管理应用", "level": "2"}, {"code": "X08.01.01315", "description": "远程提讯平台", "level": "2"}, {"code": "X08.01.01316", "description": "MCU", "level": "2"}, {"code": "X08.01.01317", "description": "存储", "level": "2"}, {"code": "X08.01.01318", "description": "检察院远程提讯", "level": "2"}, {"code": "X08.01.01319", "description": "看守所远程提讯", "level": "2"}, {"code": "X08.01.01321", "description": "公安监管实战基础版", "level": "2"}, {"code": "X08.01.01322", "description": "公安监管实战监室数量", "level": "2"}, {"code": "X08.01.01323", "description": "公安监管实战加强版", "level": "2"}, {"code": "X08.01.01324", "description": "公安监管实战平台定制", "level": "2"}, {"code": "X08.01.01325", "description": "服务器", "level": "2"}, {"code": "X08.01.01327", "description": "平台软件模块", "level": "2"}, {"code": "X08.01.01328", "description": "监管平台软件配套服务器（机架式服务器）", "level": "2"}, {"code": "X08.01.01329", "description": "监管平台软件配套服务器（超融合私有云）", "level": "2"}, {"code": "X08.01.01330", "description": "存储（云存储）", "level": "2"}, {"code": "X08.01.01331", "description": "存储（集中存储）", "level": "2"}, {"code": "X08.01.01332", "description": "国标联网网关", "level": "2"}, {"code": "X08.01.01333", "description": "报警门禁接入网关", "level": "2"}, {"code": "X08.01.01334", "description": "国产化配套", "level": "2"}, {"code": "X08.01.01335", "description": "校时服务器", "level": "2"}, {"code": "X08.01.01341", "description": "监室&放风场", "level": "2"}, {"code": "X08.01.01342", "description": "AB门", "level": "2"}, {"code": "X08.01.01343", "description": "周界", "level": "2"}, {"code": "X08.01.01344", "description": "所内公共区域", "level": "2"}, {"code": "X08.01.01345", "description": "伙房", "level": "2"}, {"code": "X08.01.01349", "description": "采集设备", "level": "2"}, {"code": "X08.01.01351", "description": "控制及配套设备", "level": "2"}, {"code": "X08.01.01353", "description": "应急报警系统", "level": "2"}, {"code": "X08.01.01355", "description": "高压电网", "level": "2"}, {"code": "X08.01.01356", "description": "电子围栏前端设备", "level": "2"}, {"code": "X08.01.01357", "description": "电子围栏配套报警机房", "level": "2"}, {"code": "X08.01.01358", "description": "张力围栏前端设备", "level": "2"}, {"code": "X08.01.01359", "description": "张力围栏配套报警机房", "level": "2"}, {"code": "X08.01.01360", "description": "振动光纤前端设备", "level": "2"}, {"code": "X08.01.01361", "description": "振动光纤配套报警机房", "level": "2"}, {"code": "X08.01.01362", "description": "红外对射前端设备", "level": "2"}, {"code": "X08.01.01363", "description": "红外对射配套报警机房", "level": "2"}, {"code": "X08.01.01365", "description": "违禁品检测系统", "level": "2"}, {"code": "X08.01.01367", "description": "被监管人员报告系统", "level": "2"}, {"code": "X08.01.01369", "description": "登记室", "level": "2"}, {"code": "X08.01.01370", "description": "人行通道", "level": "2"}, {"code": "X08.01.01371", "description": "车行通道", "level": "2"}, {"code": "X08.01.01373", "description": "视频智能分析系统", "level": "2"}, {"code": "X08.01.01374", "description": "防自缢系统", "level": "2"}, {"code": "X08.01.01386", "description": "无人机反制", "level": "2"}, {"code": "X08.01.01390", "description": "防逃脱", "level": "2"}, {"code": "X08.01.01391", "description": "车载", "level": "2"}, {"code": "X08.01.01392", "description": "移动监控", "level": "2"}, {"code": "X08.01.01393", "description": "平台-本地部署", "level": "2"}, {"code": "X08.01.01394", "description": "平台-互联网部署", "level": "2"}, {"code": "X08.01.01399", "description": "人脸巡更", "level": "2"}, {"code": "X08.01.01400", "description": "读卡器巡更", "level": "2"}, {"code": "X08.01.01402", "description": "监所信息交互终端系统", "level": "2"}, {"code": "X08.01.01404", "description": "物品管理系统", "level": "2"}, {"code": "X08.01.01406", "description": "自助提讯会见系统", "level": "2"}, {"code": "X08.01.01412", "description": "讯问指挥系统", "level": "2"}, {"code": "X08.01.01414", "description": "电子水牌系统", "level": "2"}, {"code": "X08.01.01418", "description": "融合通信平台软件X9000相关模块", "level": "2"}, {"code": "X08.01.01420", "description": "高性能融合通信网关", "level": "2"}, {"code": "X08.01.01421", "description": "经济型融合通信网关", "level": "2"}, {"code": "X08.01.01422", "description": "集群音频接入网关", "level": "2"}, {"code": "X08.01.01431", "description": "智能床垫", "level": "2"}, {"code": "X08.01.01432", "description": "律师身份核验终端", "level": "2"}, {"code": "X08.01.01439", "description": "同录", "level": "2"}, {"code": "X08.01.01440", "description": "视频会议", "level": "2"}, {"code": "X08.01.01441", "description": "视频", "level": "2"}, {"code": "X08.01.01443", "description": "画面显示", "level": "2"}, {"code": "X08.01.01444", "description": "示证", "level": "2"}, {"code": "X08.01.01445", "description": "视频会议", "level": "2"}, {"code": "X08.01.01446", "description": "本地音视频系统", "level": "2"}, {"code": "X08.01.01447", "description": "配件", "level": "2"}, {"code": "X08.01.01449", "description": "视频会议", "level": "2"}, {"code": "X08.01.01450", "description": "便携本外出同录", "level": "2"}, {"code": "X08.01.01451", "description": "执法记录仪系统", "level": "2"}, {"code": "X08.01.01457", "description": "同录", "level": "2"}, {"code": "X08.01.01458", "description": "视频会议", "level": "2"}, {"code": "X08.01.01459", "description": "视频", "level": "2"}, {"code": "X08.01.01461", "description": "画面显示", "level": "2"}, {"code": "X08.01.01462", "description": "示证", "level": "2"}, {"code": "X08.01.01463", "description": "视频会议", "level": "2"}, {"code": "X08.01.01464", "description": "本地音视频系统", "level": "2"}, {"code": "X08.01.01465", "description": "配件", "level": "2"}, {"code": "X08.01.01467", "description": "便携本外出同录", "level": "2"}, {"code": "X08.01.01468", "description": "执法记录仪系统", "level": "2"}, {"code": "X08.01.01470", "description": "监控", "level": "2"}, {"code": "X08.01.01471", "description": "自助办案", "level": "2"}, {"code": "X08.01.01472", "description": "报警", "level": "2"}, {"code": "X08.01.01473", "description": "办公使用", "level": "2"}, {"code": "X08.01.01475", "description": "随身物品存储", "level": "2"}, {"code": "X08.01.01476", "description": "全景监控", "level": "2"}, {"code": "X08.01.01477", "description": "报警", "level": "2"}, {"code": "X08.01.01478", "description": "安检", "level": "2"}, {"code": "X08.01.01479", "description": "信息登记", "level": "2"}, {"code": "X08.01.01481", "description": "监控", "level": "2"}, {"code": "X08.01.01482", "description": "房间状态", "level": "2"}, {"code": "X08.01.01483", "description": "门禁", "level": "2"}, {"code": "X08.01.01485", "description": "监控", "level": "2"}, {"code": "X08.01.01486", "description": "房间状态", "level": "2"}, {"code": "X08.01.01487", "description": "对讲", "level": "2"}, {"code": "X08.01.01488", "description": "门禁", "level": "2"}, {"code": "X08.01.01490", "description": "审讯同录", "level": "2"}, {"code": "X08.01.01491", "description": "监控", "level": "2"}, {"code": "X08.01.01492", "description": "房间状态", "level": "2"}, {"code": "X08.01.01493", "description": "对讲", "level": "2"}, {"code": "X08.01.01494", "description": "门禁", "level": "2"}, {"code": "X08.01.01495", "description": "报警", "level": "2"}, {"code": "X08.01.01496", "description": "审讯", "level": "2"}, {"code": "X08.01.01498", "description": "监控", "level": "2"}, {"code": "X08.01.01499", "description": "门禁", "level": "2"}, {"code": "X08.01.01501", "description": "监控", "level": "2"}, {"code": "X08.01.01503", "description": "监控", "level": "2"}, {"code": "X08.01.01505", "description": "显控", "level": "2"}, {"code": "X08.01.01506", "description": "监控", "level": "2"}, {"code": "X08.01.01507", "description": "对讲", "level": "2"}, {"code": "X08.01.01508", "description": "办公使用", "level": "2"}, {"code": "X08.01.01509", "description": "语音识别系统使用要求较高，只能识别普通话；实际效果以实测为准，请谨慎下单；下单前请与客户沟通好应用效果，以免效果不如所期造成退货", "level": "2"}, {"code": "X08.01.01510", "description": "情绪识别", "level": "2"}, {"code": "X08.01.01512", "description": "软硬一体", "level": "2"}, {"code": "X08.01.01513", "description": "对讲", "level": "2"}, {"code": "X08.01.01514", "description": "存储", "level": "2"}, {"code": "X08.01.01516", "description": "校时", "level": "2"}, {"code": "X08.01.01517", "description": "行为分析", "level": "2"}, {"code": "X08.01.01518", "description": "报警", "level": "2"}, {"code": "X08.01.01523", "description": "监控", "level": "2"}, {"code": "X08.01.01524", "description": "涉案财物存放", "level": "2"}, {"code": "X08.01.01526", "description": "物品登记抓拍", "level": "2"}, {"code": "X08.01.01530", "description": "卷宗", "level": "2"}, {"code": "X08.01.01531", "description": "监控", "level": "2"}, {"code": "X08.01.01532", "description": "平台", "level": "2"}, {"code": "X08.01.01535", "description": "AOA蓝牙定位", "level": "2"}, {"code": "X08.01.01539", "description": "融合通信平台软件X9000相关模块", "level": "2"}, {"code": "X08.01.01540", "description": "MCU", "level": "2"}, {"code": "X08.01.01541", "description": "高性能融合通信网关", "level": "2"}, {"code": "X08.01.01542", "description": "经济型融合通信网关", "level": "2"}, {"code": "X08.01.01543", "description": "集群音频接入网关", "level": "2"}, {"code": "X08.01.01544", "description": "调度台", "level": "2"}, {"code": "X08.01.01546", "description": "普通机架式服务器", "level": "2"}, {"code": "X08.01.01547", "description": "超融合私有云", "level": "2"}, {"code": "X08.01.01549", "description": "中心-校时服务器", "level": "2"}, {"code": "X08.01.01551", "description": "云存储", "level": "2"}, {"code": "X08.01.01554", "description": "国标联网网关", "level": "2"}, {"code": "X08.01.01558", "description": "业务数据对接网关", "level": "2"}, {"code": "X08.01.01560", "description": "安防管理", "level": "2"}, {"code": "X08.01.01561", "description": "智能管控", "level": "2"}, {"code": "X08.01.01562", "description": "应急指挥", "level": "2"}, {"code": "X08.01.01565", "description": "纯软模块化", "level": "2"}, {"code": "X08.01.01566", "description": "纯软平台", "level": "2"}, {"code": "X08.01.01567", "description": "服务器纯硬件", "level": "2"}, {"code": "X08.01.01569", "description": "软硬一体", "level": "2"}, {"code": "X08.01.01570", "description": "纯软", "level": "2"}, {"code": "X08.01.01571", "description": "纯硬件", "level": "2"}, {"code": "X08.01.01572", "description": "监控", "level": "2"}, {"code": "X08.01.01573", "description": "大屏显示", "level": "2"}, {"code": "X08.01.01575", "description": "存储", "level": "2"}, {"code": "X08.01.01576", "description": "平台服务器", "level": "2"}, {"code": "X08.01.01577", "description": "智能分析", "level": "2"}, {"code": "X08.01.01578", "description": "运维系统", "level": "2"}, {"code": "X08.01.01580", "description": "软件平台", "level": "2"}, {"code": "X08.01.01587", "description": "平台软件", "level": "2"}, {"code": "X08.01.01588", "description": "平台服务器", "level": "2"}, {"code": "X08.01.01589", "description": "存储", "level": "2"}, {"code": "X08.01.01590", "description": "移动执法", "level": "2"}, {"code": "X08.01.01591", "description": "车载系统", "level": "2"}, {"code": "X08.01.01592", "description": "调度台", "level": "2"}, {"code": "X08.01.01593", "description": "指挥中心大屏", "level": "2"}, {"code": "X08.01.01597", "description": "平台软件", "level": "2"}, {"code": "X08.01.01598", "description": "高性能服务器", "level": "2"}, {"code": "X08.01.01600", "description": "存储", "level": "2"}, {"code": "X08.01.01609", "description": "移动执法", "level": "2"}, {"code": "X08.01.01610", "description": "车载系统", "level": "2"}, {"code": "X08.01.01611", "description": "调度台", "level": "2"}, {"code": "X08.01.01612", "description": "指挥中心大屏", "level": "2"}, {"code": "X08.01.01614", "description": "led显示", "level": "2"}, {"code": "X08.01.01616", "description": "纯分布式部署", "level": "2"}, {"code": "X08.01.01617", "description": "集中式＋分布式布置", "level": "2"}, {"code": "X08.01.01618", "description": "纯集中式部署", "level": "2"}, {"code": "X08.01.01621", "description": "法院出入口", "level": "2"}, {"code": "X08.01.01622", "description": "法院周界", "level": "2"}, {"code": "X08.01.01623", "description": "法院羁押室", "level": "2"}, {"code": "X08.01.01624", "description": "法院庭审室", "level": "2"}, {"code": "X08.01.01625", "description": "AR", "level": "2"}, {"code": "X08.01.01627", "description": "场景智能分析", "level": "2"}, {"code": "X08.01.01629", "description": "报警系统", "level": "2"}, {"code": "X08.01.01631", "description": "人脸布控", "level": "2"}, {"code": "X08.01.01633", "description": "访客", "level": "2"}, {"code": "X08.01.01634", "description": "人员通道", "level": "2"}, {"code": "X08.01.01635", "description": "车行通道", "level": "2"}, {"code": "X08.01.01637", "description": "智能X射线检查系统", "level": "2"}, {"code": "X08.01.01639", "description": "违禁品分类安检门", "level": "2"}, {"code": "X08.01.01640", "description": "防暴", "level": "2"}, {"code": "X08.01.01641", "description": "金属探测", "level": "2"}, {"code": "X08.01.01642", "description": "液体检测", "level": "2"}, {"code": "X08.01.01644", "description": "升降柱", "level": "2"}, {"code": "X08.01.01645", "description": "固定式车底检查系统", "level": "2"}, {"code": "X08.01.01647", "description": "执法单兵系统", "level": "2"}, {"code": "X08.01.01649", "description": "离线刷卡方案", "level": "2"}, {"code": "X08.01.01650", "description": "人脸梯控", "level": "2"}, {"code": "X08.01.01652", "description": "门禁一体机", "level": "2"}, {"code": "X08.01.01653", "description": "门禁控制器", "level": "2"}, {"code": "X08.01.01654", "description": "门禁发卡器", "level": "2"}, {"code": "X08.01.01655", "description": "人证设备", "level": "2"}, {"code": "X08.01.01657", "description": "服务器", "level": "2"}, {"code": "X08.01.01661", "description": "三维", "level": "2"}, {"code": "X08.01.01662", "description": "监舍点名(三选一）", "level": "2"}, {"code": "X08.01.01663", "description": "融合通信平台软件X9000相关模块", "level": "2"}, {"code": "X08.01.01667", "description": "服务器", "level": "2"}, {"code": "X08.01.01668", "description": "操作系统", "level": "2"}, {"code": "X08.01.01669", "description": "国产化PC主机", "level": "2"}, {"code": "X08.01.01670", "description": "桌面操作系统", "level": "2"}, {"code": "X08.01.01672", "description": "人脸门禁指纹发卡器", "level": "2"}, {"code": "X08.01.01688", "description": "视频监控", "level": "2"}, {"code": "X08.01.01689", "description": "同录", "level": "2"}, {"code": "X08.01.01691", "description": "视频画面显示", "level": "2"}, {"code": "X08.01.01692", "description": "示证", "level": "2"}, {"code": "X08.01.01693", "description": "存储", "level": "2"}, {"code": "X08.01.01696", "description": "远程会议接入", "level": "2"}, {"code": "X08.01.01698", "description": "MCU", "level": "2"}, {"code": "X08.01.01699", "description": "视频会议", "level": "2"}, {"code": "X08.01.01706", "description": "视频监控", "level": "2"}, {"code": "X08.01.01707", "description": "同录", "level": "2"}, {"code": "X08.01.01709", "description": "视频画面显示", "level": "2"}, {"code": "X08.01.01710", "description": "示证", "level": "2"}, {"code": "X08.01.01711", "description": "存储", "level": "2"}, {"code": "X08.01.01727", "description": "运维系统", "level": "2"}, {"code": "X08.01.01728", "description": "事件分析", "level": "2"}, {"code": "X08.01.01729", "description": "云存储", "level": "2"}, {"code": "X08.01.01730", "description": "设备直存", "level": "2"}, {"code": "X08.01.01733", "description": "视频监控", "level": "2"}, {"code": "X08.01.01736", "description": "物品抓拍", "level": "2"}, {"code": "X08.01.01737", "description": "对讲", "level": "2"}, {"code": "X08.01.01738", "description": "人证核验", "level": "2"}, {"code": "X08.01.01739", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01741", "description": "随身物品存储", "level": "2"}, {"code": "X08.01.01742", "description": "安检", "level": "2"}, {"code": "X08.01.01743", "description": "对讲", "level": "2"}, {"code": "X08.01.01745", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01747", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01748", "description": "手动报警", "level": "2"}, {"code": "X08.01.01749", "description": "可视对讲", "level": "2"}, {"code": "X08.01.01750", "description": "房间状态显示", "level": "2"}, {"code": "X08.01.01752", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01753", "description": "手动报警", "level": "2"}, {"code": "X08.01.01754", "description": "看护打标", "level": "2"}, {"code": "X08.01.01756", "description": "房间状态显示", "level": "2"}, {"code": "X08.01.01758", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01759", "description": "手动报警", "level": "2"}, {"code": "X08.01.01760", "description": "看护打标", "level": "2"}, {"code": "X08.01.01762", "description": "房间状态显示", "level": "2"}, {"code": "X08.01.01770", "description": "视频监控", "level": "2"}, {"code": "X08.01.01772", "description": "门禁控制", "level": "2"}, {"code": "X08.01.01777", "description": "留置办案平台", "level": "2"}, {"code": "X08.01.01778", "description": "校时系统", "level": "2"}, {"code": "X08.01.01779", "description": "运维系统", "level": "2"}, {"code": "X08.01.01780", "description": "事件监测", "level": "2"}, {"code": "X08.01.01781", "description": "中心存储", "level": "2"}, {"code": "X08.01.01782", "description": "设备存储", "level": "2"}, {"code": "X08.01.01784", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01785", "description": "录音录像", "level": "2"}, {"code": "X08.01.01789", "description": "情绪识别", "level": "2"}, {"code": "X08.01.01792", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01794", "description": "示证指挥", "level": "2"}, {"code": "X08.01.01798", "description": "语音识别", "level": "2"}, {"code": "X08.01.01803", "description": "高清同录", "level": "2"}, {"code": "X08.01.01806", "description": "谈话软件", "level": "2"}, {"code": "X08.01.01807", "description": "校时系统", "level": "2"}, {"code": "X08.01.01808", "description": "存储系统", "level": "2"}, {"code": "X08.01.01810", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01813", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01816", "description": "视频监控", "level": "2"}, {"code": "X08.01.01818", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01819", "description": "录音录像", "level": "2"}, {"code": "X08.01.01822", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01823", "description": "录音录像", "level": "2"}, {"code": "X08.01.01824", "description": "示证展示", "level": "2"}, {"code": "X08.01.01827", "description": "远程指挥", "level": "2"}, {"code": "X08.01.01829", "description": "视频监控", "level": "2"}, {"code": "X08.01.01831", "description": "门禁系统", "level": "2"}, {"code": "X08.01.01837", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01838", "description": "录音录像", "level": "2"}, {"code": "X08.01.01839", "description": "物证展示", "level": "2"}, {"code": "X08.01.01840", "description": "指挥对讲", "level": "2"}, {"code": "X08.01.01843", "description": "门禁管理", "level": "2"}, {"code": "X08.01.01845", "description": "指挥上墙", "level": "2"}, {"code": "X08.01.01847", "description": "外出谈话", "level": "2"}, {"code": "X08.01.01850", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01851", "description": "录音录像", "level": "2"}, {"code": "X08.01.01852", "description": "物证展示", "level": "2"}, {"code": "X08.01.01856", "description": "谈话软件", "level": "2"}, {"code": "X08.01.01857", "description": "校时系统", "level": "2"}, {"code": "X08.01.01858", "description": "运维系统", "level": "2"}, {"code": "X08.01.01859", "description": "存储系统", "level": "2"}, {"code": "X08.01.01860", "description": "云存", "level": "2"}, {"code": "X08.01.01861", "description": "行为分析", "level": "2"}, {"code": "X08.01.01863", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01864", "description": "自助谈话登记", "level": "2"}, {"code": "X08.01.01865", "description": "物品抓拍", "level": "2"}, {"code": "X08.01.01866", "description": "手动报警", "level": "2"}, {"code": "X08.01.01867", "description": "人证对比", "level": "2"}, {"code": "X08.01.01868", "description": "对讲", "level": "2"}, {"code": "X08.01.01871", "description": "随身物品管理", "level": "2"}, {"code": "X08.01.01872", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01873", "description": "手动报警", "level": "2"}, {"code": "X08.01.01874", "description": "对讲", "level": "2"}, {"code": "X08.01.01875", "description": "安检系统", "level": "2"}, {"code": "X08.01.01879", "description": "房态显示", "level": "2"}, {"code": "X08.01.01880", "description": "视频监控", "level": "2"}, {"code": "X08.01.01882", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01883", "description": "录音录像", "level": "2"}, {"code": "X08.01.01885", "description": "房态显示", "level": "2"}, {"code": "X08.01.01887", "description": "音视频监控", "level": "2"}, {"code": "X08.01.01888", "description": "房态显示", "level": "2"}, {"code": "X08.01.01889", "description": "手动报警", "level": "2"}, {"code": "X08.01.01890", "description": "录音录像", "level": "2"}, {"code": "X08.01.01891", "description": "示证展示", "level": "2"}, {"code": "X08.01.01892", "description": "对讲", "level": "2"}, {"code": "X08.01.01893", "description": "情绪识别", "level": "2"}, {"code": "X08.01.01898", "description": "视频监控", "level": "2"}, {"code": "X08.01.01900", "description": "门禁系统", "level": "2"}, {"code": "X08.01.01904", "description": "语音识别", "level": "2"}, {"code": "X08.01.01909", "description": "对讲终端", "level": "2"}, {"code": "X08.01.01911", "description": "管理机", "level": "2"}, {"code": "X08.01.01912", "description": "管理平台", "level": "2"}, {"code": "X08.01.01913", "description": "边缘计算", "level": "2"}, {"code": "X08.01.01915", "description": "集中存储", "level": "2"}, {"code": "X08.01.01919", "description": "监仓内屏（10.2）", "level": "2"}, {"code": "X08.01.01920", "description": "监仓内屏（15.6）", "level": "2"}, {"code": "X08.01.01921", "description": "监仓内屏（10.2）", "level": "2"}, {"code": "X08.01.01922", "description": "监仓内屏（15.6）", "level": "2"}, {"code": "X08.01.01923", "description": "监仓外屏", "level": "2"}, {"code": "X08.01.01924", "description": "软件平台（纯电教）", "level": "2"}, {"code": "X08.01.01925", "description": "信号源端", "level": "2"}, {"code": "X08.01.01926", "description": "前端设备", "level": "2"}, {"code": "X08.01.01927", "description": "VR互动硬件部分", "level": "2"}, {"code": "X08.01.01929", "description": "校时系统", "level": "2"}, {"code": "X08.01.01930", "description": "手动报警", "level": "2"}, {"code": "X08.01.01931", "description": "备份存储", "level": "2"}, {"code": "X08.01.01933", "description": "无人机机库", "level": "2"}, {"code": "X08.01.01935", "description": "核心交换机", "level": "2"}, {"code": "X08.01.01936", "description": "汇聚交换机", "level": "2"}, {"code": "X08.01.01937", "description": "接入交换机-室内", "level": "2"}, {"code": "X08.01.01938", "description": "接入交换机-室外", "level": "2"}, {"code": "X08.01.01941", "description": "下一代防火墙", "level": "2"}, {"code": "X08.01.01942", "description": "EDR杀毒软件", "level": "2"}, {"code": "X08.01.01943", "description": "日志审计", "level": "2"}, {"code": "X08.01.01945", "description": "运维审计", "level": "2"}, {"code": "X08.01.01947", "description": "漏洞扫描系统", "level": "2"}, {"code": "X08.01.01949", "description": "WEB应用防火墙", "level": "2"}, {"code": "X08.01.01951", "description": "核心交换机", "level": "2"}, {"code": "X08.01.01952", "description": "汇聚交换机", "level": "2"}, {"code": "X08.01.01953", "description": "接入交换机-室内", "level": "2"}, {"code": "X08.01.01954", "description": "接入交换机-室外", "level": "2"}, {"code": "X08.01.01957", "description": "下一代防火墙", "level": "2"}, {"code": "X08.01.01958", "description": "EDR杀毒软件", "level": "2"}, {"code": "X08.01.01959", "description": "日志审计", "level": "2"}, {"code": "X08.01.01961", "description": "运维审计", "level": "2"}, {"code": "X08.01.01964", "description": "漏洞扫描系统", "level": "2"}, {"code": "X08.01.01966", "description": "WEB应用防火墙", "level": "2"}, {"code": "X08.01.01968", "description": "核心交换机", "level": "2"}, {"code": "X08.01.01969", "description": "接入交换机-室内", "level": "2"}, {"code": "X08.01.01970", "description": "接入交换机-室外", "level": "2"}, {"code": "X08.01.01976", "description": "接入交换机", "level": "2"}, {"code": "X08.01.01978", "description": "接入交换机", "level": "2"}, {"code": "X08.01.01980", "description": "核心交换机", "level": "2"}, {"code": "X08.01.01981", "description": "接入交换机-室内", "level": "2"}, {"code": "X08.01.01982", "description": "接入交换机-室外", "level": "2"}, {"code": "X08.01.01984", "description": "核心交换机", "level": "2"}, {"code": "X08.01.01985", "description": "接入交换机-室内", "level": "2"}, {"code": "X08.01.01986", "description": "接入交换机-室外", "level": "2"}, {"code": "X08.01.01989", "description": "其他", "level": "2"}, {"code": "X08.01.01996", "description": "接入交换机-室内", "level": "2"}, {"code": "X08.01.01997", "description": "接入交换机-室外", "level": "2"}, {"code": "X08.02.00069", "description": "摄像机", "level": "2"}, {"code": "X08.02.00070", "description": "摄像机", "level": "2"}, {"code": "X08.02.00071", "description": "摄像机", "level": "2"}, {"code": "X08.02.00072", "description": "摄像机", "level": "2"}, {"code": "X08.02.00073", "description": "摄像机", "level": "2"}, {"code": "X08.02.00079", "description": "监室单目全景智能半球", "level": "2"}, {"code": "X08.02.00081", "description": "事件监测智能分析", "level": "2"}, {"code": "X08.02.00085", "description": "罪犯出监人脸核验终端", "level": "2"}, {"code": "X08.02.00086", "description": "摄像机", "level": "2"}, {"code": "X08.02.00087", "description": "摄像机", "level": "2"}, {"code": "X08.02.00089", "description": "EVS集中存储", "level": "2"}, {"code": "X08.02.00090", "description": "非视频设备接入网关", "level": "2"}, {"code": "X08.02.00091", "description": "门禁", "level": "2"}, {"code": "X08.02.00092", "description": "谈话主机", "level": "2"}, {"code": "X08.02.00093", "description": "智能分析设备", "level": "2"}, {"code": "X08.02.00094", "description": "平台接入网关", "level": "2"}, {"code": "X08.02.00095", "description": "平台接入网关", "level": "2"}, {"code": "X08.02.00098", "description": "音视频监控", "level": "2"}, {"code": "X08.02.00099", "description": "音视频监控", "level": "2"}, {"code": "X08.02.00100", "description": "音视频监控", "level": "2"}, {"code": "X08.02.00101", "description": "音视频监控", "level": "2"}, {"code": "X08.02.00102", "description": "音视频监控", "level": "2"}, {"code": "X08.02.00105", "description": "前智能", "level": "2"}, {"code": "X08.02.00106", "description": "后智能", "level": "2"}, {"code": "X08.02.00110", "description": "监室&放风场", "level": "2"}, {"code": "X08.02.00111", "description": "所内公共区域", "level": "2"}, {"code": "X08.02.00112", "description": "采集设备", "level": "2"}, {"code": "X08.02.00113", "description": "门禁设备", "level": "2"}, {"code": "X08.02.00115", "description": "人行通道", "level": "2"}, {"code": "X08.02.00117", "description": "车行通道", "level": "2"}, {"code": "X08.02.00118", "description": "视频智能分析系统", "level": "2"}, {"code": "X08.02.00119", "description": "无人机反制", "level": "2"}, {"code": "X08.02.00120", "description": "防逃脱", "level": "2"}, {"code": "X08.02.00121", "description": "平台-本地部署", "level": "2"}, {"code": "X08.02.00124", "description": "人脸巡更", "level": "2"}, {"code": "X08.02.00125", "description": "讯问指挥系统", "level": "2"}, {"code": "X08.02.00126", "description": "MCU", "level": "2"}, {"code": "X08.02.00127", "description": "调度台", "level": "2"}, {"code": "X08.02.00132", "description": "高清同录主机（二选一）", "level": "2"}, {"code": "X08.02.00134", "description": "门禁", "level": "2"}, {"code": "X08.02.00136", "description": "软件", "level": "2"}, {"code": "X08.02.00137", "description": "EVS集中存储", "level": "2"}, {"code": "X08.02.00138", "description": "非视频设备接入网关", "level": "2"}, {"code": "X08.02.00139", "description": "视频监控", "level": "2"}, {"code": "X08.02.00140", "description": "同录", "level": "2"}, {"code": "X08.02.00143", "description": "双电源司法谈话主机", "level": "2"}, {"code": "X08.02.00144", "description": "示证显示器", "level": "2"}, {"code": "X08.02.00159", "description": "双电源司法谈话主机", "level": "2"}, {"code": "X08.02.00160", "description": "双电源司法谈话主机", "level": "2"}, {"code": "X08.02.00161", "description": "对讲终端", "level": "2"}, {"code": "X08.02.00162", "description": "液晶", "level": "2"}, {"code": "X08.02.00164", "description": "网络视频解码器（三选一）", "level": "2"}, {"code": "X08.02.00165", "description": "网络视频解码器（三选一）", "level": "2"}, {"code": "X08.02.00166", "description": "定位服务器", "level": "2"}, {"code": "X08.02.00167", "description": "枪型网络摄像机", "level": "2"}, {"code": "X08.02.00168", "description": "球机", "level": "2"}, {"code": "X08.02.00169", "description": "温湿度显示屏", "level": "2"}, {"code": "X08.02.00170", "description": "温湿度显示屏", "level": "2"}, {"code": "X08.02.00171", "description": "全景相机", "level": "2"}, {"code": "X08.03.00365", "description": "*********.43293", "level": "2"}], "3": [{"code": "X08.01.02001", "description": "下一代防火墙", "level": "3"}, {"code": "X08.01.02006", "description": "运维审计", "level": "3"}, {"code": "X08.01.02008", "description": "漏洞扫描系统", "level": "3"}, {"code": "X08.01.02010", "description": "WEB应用防火墙", "level": "3"}, {"code": "X08.01.02019", "description": "普通机架式服务器", "level": "3"}, {"code": "X08.01.02020", "description": "超融合私有云", "level": "3"}, {"code": "X08.01.02021", "description": "服务器", "level": "3"}, {"code": "X08.01.02022", "description": "云存储", "level": "3"}, {"code": "X08.01.02024", "description": "国标联网网关", "level": "3"}, {"code": "X08.01.02026", "description": "业务数据对接网关", "level": "3"}, {"code": "X08.01.02028", "description": "安防管理", "level": "3"}, {"code": "X08.01.02029", "description": "智能管控", "level": "3"}, {"code": "X08.01.02030", "description": "应急指挥", "level": "3"}, {"code": "X08.01.02031", "description": "纯软模块化", "level": "3"}, {"code": "X08.01.02032", "description": "纯软平台", "level": "3"}, {"code": "X08.01.02033", "description": "服务器纯硬件", "level": "3"}, {"code": "X08.01.02034", "description": "软硬一体", "level": "3"}, {"code": "X08.01.02035", "description": "纯软", "level": "3"}, {"code": "X08.01.02036", "description": "纯硬件", "level": "3"}, {"code": "X08.01.02037", "description": "三维", "level": "3"}, {"code": "X08.01.02038", "description": "其他", "level": "3"}, {"code": "X08.01.02039", "description": "对讲终端", "level": "3"}, {"code": "X08.01.02040", "description": "管理机", "level": "3"}, {"code": "X08.01.02041", "description": "管理平台", "level": "3"}, {"code": "X08.01.02042", "description": "车辆生命探测系统", "level": "3"}, {"code": "X08.01.02044", "description": "高精度定位", "level": "3"}, {"code": "X08.01.02048", "description": "音频质检", "level": "3"}, {"code": "X08.01.02050", "description": "情绪识别后端版", "level": "3"}, {"code": "X08.01.02051", "description": "X9000服务器", "level": "3"}, {"code": "X08.01.02052", "description": "X9000服务器", "level": "3"}, {"code": "X08.01.02054", "description": "显控系统", "level": "3"}, {"code": "X08.01.02055", "description": "会议平板", "level": "3"}, {"code": "X08.01.02056", "description": "OPS电脑", "level": "3"}, {"code": "X08.01.02057", "description": "移动支架", "level": "3"}, {"code": "X08.01.02058", "description": "智能书写笔", "level": "3"}, {"code": "X08.01.02059", "description": "投屏器", "level": "3"}, {"code": "X08.01.02060", "description": "指挥中心/分控中心", "level": "3"}, {"code": "X08.01.02061", "description": "指挥中心/分控中心", "level": "3"}, {"code": "X08.01.02062", "description": "上墙管理", "level": "3"}, {"code": "X08.01.02063", "description": "监控", "level": "3"}, {"code": "X08.01.02064", "description": "报警", "level": "3"}, {"code": "X08.01.02065", "description": "示证展示", "level": "3"}, {"code": "X08.01.02066", "description": "信息发布", "level": "3"}, {"code": "X08.01.02067", "description": "上墙管理", "level": "3"}, {"code": "X08.01.02068", "description": "视频监控", "level": "3"}, {"code": "X08.01.02069", "description": "报警管理", "level": "3"}, {"code": "X08.01.02070", "description": "空间定位系统", "level": "3"}, {"code": "X08.01.02071", "description": "空间定位系统", "level": "3"}, {"code": "X08.01.02093", "description": "监控", "level": "3"}, {"code": "X08.01.02095", "description": "全景监控", "level": "3"}, {"code": "X08.01.02097", "description": "监控", "level": "3"}, {"code": "X08.01.02099", "description": "监控", "level": "3"}, {"code": "X08.01.02101", "description": "监控", "level": "3"}, {"code": "X08.01.02103", "description": "监控", "level": "3"}, {"code": "X08.01.02105", "description": "监控", "level": "3"}, {"code": "X08.01.02107", "description": "监控", "level": "3"}, {"code": "X08.01.02109", "description": "监控", "level": "3"}], "4": [{"code": "*********.12306", "description": "国内大华10寸安卓单屏身份核验终端DH-ASHZ360A-W", "level": "4", "parent": "X08.01.00665"}, {"code": "*********.10144", "description": "国内大华(90度)全高闸（双机芯，室外）-DH-ASGQ500B", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.38910", "description": "中文大华司法人脸标签可视化-400万暖光变焦防暴半球网络摄像机DH-IPC-HDBW8449E-ZVS-LED-0832-DC12AC24V-S2-SFBQ", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.38911", "description": "中文大华司法人脸标签可视化-400万暖光变焦枪型网络摄像机DH-IPC-HFW8449K-ZVS-LED-2712-DC12AC24V-S2-SFBQ", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.10296", "description": "国内大华手持金属探测器DH-XIS4000-M（充电电池+座充）-非ROHS", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.10312", "description": "国内大华普通金属安检门DH-ISC-D118S(主板V2.2)", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.12128", "description": "国内大华10寸智能门禁一体机K款（室外、IC卡、WiFi）DH-ASI9213K-W-V1", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.10105", "description": "国内大华智能X射线检查系统DH-ISC-M6550A", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.12467", "description": "国内大华门神7寸智能门禁一体机DH-ASI7213H", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.11212", "description": "国内大华两门双向门禁控制器C款(铁箱型，室内)-DH-ASC2202C-D", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.38912", "description": "中文大华司法人脸标签可视化-400万暖光变焦枪型网络摄像机DH-IPC-HFW8449K-ZVS-LED-0832-DC12AC24V-S2-SFBQ", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.11821", "description": "中文大华热成像人体测温黑体DH-TPC-HBB-AGN", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.38909", "description": "中文大华司法人脸标签可视化-400万暖光变焦防暴半球网络摄像机DH-IPC-HDBW8449E-ZVS-LED-2712-DC12AC24V-S2-SFBQ", "level": "4", "parent": "X08.01.00666"}, {"code": "*********.10179", "description": "中文大华液压升降柱控制器（一拖六）-非ROHS", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.43211", "description": "中文大华mini天镜-400万融智能双光变焦枪型网络摄像机DH-IPC-HFW8449K2-ZRL-IL4-0832-DC12AC24V-S3", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.42324", "description": "中文大华mini天镜-400万融智能红外变焦防暴半球网络摄像机DH-IPC-HDBW8449E1-ZRL-2712-DC12AC24V-S3", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.10173", "description": "中文大华全自动液压一体升降柱（10mm）-非ROHS", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.43304", "description": "中文大华mini天镜-400万融智能双光变焦枪型网络摄像机DH-IPC-HFW8449K2-ZRL-IL4-2712-DC12AC24V-S3", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.11922", "description": "国内大华明行系列智慧云班牌（司法班牌）DH-ECH22-SAI202AS", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.10325", "description": "固定式车底检查系统-非ROHS", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.14516", "description": "中文大华200万双光变焦智能出入口摄像机（220V）DH-ITC216-PW9H-Z", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.12943", "description": "中文中性出入口抓拍一体机杆件（银色+球头+1.2米）ITSZJ-1102-12", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.42325", "description": "中文大华mini天镜-400万融智能红外变焦防暴半球网络摄像机DH-IPC-HDBW8449E1-ZRL-0832-DC12AC24V-S3", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.10363-001", "description": "升降柱控制柜-非ROHS", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.11581", "description": "国内大华明行系列智慧云班牌（双目单刷卡）DH-ECH22-SAI202AA", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.39513", "description": "中文大华天鹰-1600万全景红外定焦四目拼接枪型网络摄像机DH-IPC-PFW81649-A180-G-DC12AC24V-S2", "level": "4", "parent": "X08.01.00667"}, {"code": "*********.11653", "description": "公安监管AB门防误放模块DH-DSS-P9500-DSJG-FWF", "level": "4", "parent": "X08.01.00668"}, {"code": "*********.11640", "description": "公安监管设备管理授权模块DH-DSS-P9500-DSJG-BaseVideoMgr", "level": "4", "parent": "X08.01.00668"}, {"code": "*********.11644", "description": "公安监管门禁管理模块DH-DSS-P9500-DSJG-AccessControl", "level": "4", "parent": "X08.01.00668"}, {"code": "*********.11641", "description": "公安监管电子地图模块DH-DSS-P9500-DSJG-BaseMap", "level": "4", "parent": "X08.01.00668"}, {"code": "*********.11643", "description": "公安监管人像基础应用模块DH-DSS-P9500-DSJG-FaceBase", "level": "4", "parent": "X08.01.00668"}, {"code": "*********.11639", "description": "公安监管通用基础业务模块DH-DSS-P9500-DSJG-BaseVideo", "level": "4", "parent": "X08.01.00668"}, {"code": "*********.11655", "description": "公安监管AB门进出流程管控模块DH-DSS-P9500-DSJG-ABDoor", "level": "4", "parent": "X08.01.00668"}, {"code": "*********.11648", "description": "公安监管AB门车底扫描模块DH-DSS-P9500-DSJG-VehicleRecord", "level": "4", "parent": "X08.01.00668"}, {"code": "*********.12642", "description": "司法行业高配服务器 DH-DSS-P9100S2A-H-HW", "level": "4", "parent": "X08.01.00669"}, {"code": "*********.10791", "description": "国内大华1U监管事件检测智能服务器DH-IVS-IP8000-2ZE-RM1", "level": "4", "parent": "X08.01.00670"}, {"code": "*********.10205", "description": "网络视频存储服务器 国内大华 DH-EVS5224S", "level": "4", "parent": "X08.01.00670"}, {"code": "*********.12556", "description": "司法机关园区服务器\tDH-DSS-P9100D-LL-HW", "level": "4", "parent": "X08.01.00672"}, {"code": "*********.11247", "description": "视频国标对接网关100路扩容授权DH-AGS-B9101-VideoChannel/100", "level": "4", "parent": "X08.01.00672"}, {"code": "*********.11248", "description": "视频国标对接网关2万路扩容授权DH-AGS-B9101-VideoChannel/2W", "level": "4", "parent": "X08.01.00672"}, {"code": "*********.10294", "description": "电子脚扣押解管理APP(纯软)，DH-PSP6500A-G2，含服务", "level": "4", "parent": "X08.01.00673"}, {"code": "*********.10390", "description": "肩章报警器-非ROHS", "level": "4", "parent": "X08.01.00673"}, {"code": "*********.10389", "description": "电子脚扣-非ROHS", "level": "4", "parent": "X08.01.00673"}, {"code": "*********.10298", "description": "一年电子脚扣流量费用，DH-TFD90WAG-DATA-LIS1", "level": "4", "parent": "X08.01.00673"}, {"code": "*********.10120", "description": "电源线-(60227 IEC 52(RVV) 2x0.5)-黑色-200M", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.11544", "description": "中文华锐捷驾驶员监测摄像头（短杆-航空头）DAE-CDM5130-CY", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.11524", "description": "国内华锐捷车载7寸触摸显示屏DH-DAE-MLCDF7-T", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.11078", "description": "中文大华100万HDCVI高清20米微型车载金属海螺（2.8mm）DH-HAC-HDW3200G-M-0280B-S5-100", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.11072", "description": "中文大华前视监测摄像头 DH-DAE-CFM5210-CY-100", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.12263-000", "description": "车载后装线-(MCNU-GXF4-GXM4-12,M12-4芯航空头针型转M12-4芯航空头孔型,非UL)-黑色-12M", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.11126", "description": "中文华锐捷DH-DAE-MNVR4208-GFWI车载录像机", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.12261-000", "description": "车载后装线-(MCNU-GXF4-GXM4-3,M12-4芯航空头针型转M12-4芯航空头孔型,非UL)-黑色-3000mm", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.10627", "description": "车载国内中性防灾备份盒MA-DPBB6401", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.2303", "description": "车载报警按钮(方形带接插件)-非ROHS", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.12262-000", "description": "车载后装线-(MCNU-GXF4-GXM4-6,M12-4芯航空头针型转M12-4芯航空头孔型,非UL)-黑色-6M", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.10429", "description": "T2.00414386", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.20609", "description": "中文大华200万车载HDCVI高清20米红外小方块（2.1mm)DH-HAC-HMW3200-0210B-S5", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.11081", "description": "中文大华100万车载HDCVI同轴高清红外3米L型海螺（2.1mm）DH-HAC-HMW3200L-0210B-S5-100", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.10548", "description": "车载对讲终端MP06", "level": "4", "parent": "X08.01.00674"}, {"code": "*********.15093", "description": "探迹PTZ4M中文大华23倍200W移动布控球（Taurus）", "level": "4", "parent": "X08.01.00675"}, {"code": "*********.15138", "description": "国内大华执法记录仪DH-DSJ-N1-128G", "level": "4", "parent": "X08.01.00675"}, {"code": "*********.12599", "description": "司法行业标准服务器DH-DSS-P9100S2D-E-HW", "level": "4", "parent": "X08.01.00676"}, {"code": "*********.11651", "description": "公安监管所外押解模块DH-DSS-P9500-DSJG-OutEscort", "level": "4", "parent": "X08.01.00676"}, {"code": "*********.10297", "description": "国内大华网络视频存储服务器DH-EVS5016S-V2", "level": "4", "parent": "X08.01.00676"}, {"code": "*********.11652", "description": "公安监管所外押解APPDH-DSS-P9500-DSJG-OutEscortAPP", "level": "4", "parent": "X08.01.00677"}, {"code": "*********.11522", "description": "国内华锐捷车载7寸显示屏DH-DAE-MLCDF7-E", "level": "4", "parent": "X08.01.00678"}, {"code": "*********.11724", "description": "中文华锐捷DH-DAE-MDJ5100-08PIS(HDD)汽车行驶记录仪(8路模拟+1路IPC)(CD)", "level": "4", "parent": "X08.01.00678"}, {"code": "*********.10097", "description": "30天连续云存储年套餐/年度", "level": "4", "parent": "X08.01.00680"}, {"code": "*********.10297", "description": "智能押解管理平台(纯软件)，DH-PSP6500C-G2，含服务", "level": "4", "parent": "X08.01.00680"}, {"code": "*********.10061", "description": "云睿基础视频设备接入服务/年度", "level": "4", "parent": "X08.01.00680"}, {"code": "*********.38561", "description": "中文大华400万星光智能定焦防暴半球网络摄像机DH-IPC-HDBW5443DR-SA-0280B-S3", "level": "4", "parent": "X08.01.00729"}, {"code": "*********.10045", "description": "高保真拾音器-DH-HS22S", "level": "4", "parent": "X08.01.00729"}, {"code": "*********.10019", "description": "单防区扩展模块DH-ARM801", "level": "4", "parent": "X08.01.00730"}, {"code": "*********.10079", "description": "紧急按钮（86盒型）-DH-ARD811-非ROHS", "level": "4", "parent": "X08.01.00730"}, {"code": "*********.10282", "description": "闭门器（60-80KG）-DH-ASF103S", "level": "4", "parent": "X08.01.00732"}, {"code": "*********.10252", "description": "900款出门按钮-DH-ASF900", "level": "4", "parent": "X08.01.00732"}, {"code": "*********.12253", "description": "国内大华7寸智能门禁一体机IC款（室外）DH-ASI7213SO", "level": "4", "parent": "X08.01.00732"}, {"code": "*********.12255", "description": "国内大华7寸智能门禁一体机IC款（室外、WiFi、指纹）DH-ASI7214SO-W", "level": "4", "parent": "X08.01.00737"}, {"code": "*********.10015", "description": "国内大华280KG单门磁力锁DH-ASF280AL", "level": "4", "parent": "X08.01.00737"}, {"code": "*********.10203", "description": "国内大华磁力锁支架（ZL型）", "level": "4", "parent": "X08.01.00737"}, {"code": "*********.38567", "description": "中文大华400万星光智能定焦防暴半球网络摄像机DH-IPC-HDBW5443R-AS-0280B-S3", "level": "4", "parent": "X08.01.00739"}, {"code": "*********.10510", "description": "国内大华智眸情绪识别一体机DH-IVS-AER3000", "level": "4", "parent": "X08.01.00743"}, {"code": "*********.13463", "description": "口碑SD42定制型号", "level": "4", "parent": "X08.01.00745"}, {"code": "*********.11509", "description": "国内大华教育配件D系列无线投屏器DH-PKP-WP0B", "level": "4", "parent": "X08.01.00748"}, {"code": "*********.10222", "description": "国内大华会议电视终端DH-VCS-TS2000-3X【蓝牙版本】", "level": "4", "parent": "X08.01.00748"}, {"code": "*********.11383", "description": "国内智能会议平板65寸专业款(项目型) DH-LPH65-MC470-P", "level": "4", "parent": "X08.01.00748"}, {"code": "*********.10038", "description": "国内大华会议配件系列55/65/75寸简易移动支架 DH-PKC-MS0A", "level": "4", "parent": "X08.01.00748"}, {"code": "*********.15143", "description": "国内大华执法记录仪DH-DSJ-N1-16G", "level": "4", "parent": "X08.01.00755"}, {"code": "*********.11187", "description": "中文华锐捷DH-DAE-MNVR4208-GFWI(5G)车载录像机", "level": "4", "parent": "X08.01.00756"}, {"code": "*********.13514", "description": "中文大华32倍400万红外高清T型云台摄像机（侧出线）", "level": "4", "parent": "X08.01.00756"}, {"code": "*********.10031", "description": "硬盘-MQ01ABD050V-8M缓存-2.5英寸-SATA接口", "level": "4", "parent": "X08.01.00756"}, {"code": "*********.33464", "description": "中文大华200万红外定焦防暴半球网络摄像机DH-IPC-HDBW3233F-M-AS-0280B", "level": "4", "parent": "X08.01.00756"}, {"code": "*********.10529", "description": "国内大华车载键盘DH-MKB1100", "level": "4", "parent": "X08.01.00756"}, {"code": "*********.15185", "description": "国内大华网络硬盘录像机DH-NVR5432FG-4KS3/I(主板V1.00)", "level": "4", "parent": "X08.01.00758"}, {"code": "*********.12036", "description": "国内大华8寸智能门禁一体机IC款(室外、WiFi、指纹)DH-ASI8214S-W-V1", "level": "4", "parent": "X08.01.00761"}, {"code": "*********.10239", "description": "国内大华43寸室内壁挂智能云显示终端(D5) DH-LDH43-SAI400K", "level": "4", "parent": "X08.01.00762"}, {"code": "*********.15929", "description": "国内大华1.5U解码器DH-NVD1205DU-4I-8K", "level": "4", "parent": "X08.01.00764"}, {"code": "*********.12308", "description": "国内大华55寸LCD标亮一体式整机H1.7（CT53P）", "level": "4", "parent": "X08.01.00764"}, {"code": "*********.16428", "description": "国内大华9路解码器DH-NVD0905DU-4I", "level": "4", "parent": "X08.01.00764"}, {"code": "*********.10054", "description": "国内大华会议电视系统多点控制单元(MCU)", "level": "4", "parent": "X08.01.00766"}, {"code": "*********.10088", "description": "多媒体视频会议服务器DH-VCS-RRS9016-V2", "level": "4", "parent": "X08.01.00766"}, {"code": "*********.10544", "description": "国内大华三代总线防盗报警控制器DH-ARC9016C-V3", "level": "4", "parent": "X08.01.00767"}, {"code": "*********.10571", "description": "国内大华LCD三代报警编程键盘DH-ARK50C（仅配套-V3系列报警主机）", "level": "4", "parent": "X08.01.00767"}, {"code": "*********.10096", "description": "大华-12V7A蓄电池-非ROHS", "level": "4", "parent": "X08.01.00767"}, {"code": "*********.10031", "description": "声光警号-DH-ARA11-国内大华", "level": "4", "parent": "X08.01.00767"}, {"code": "*********.10480", "description": "国内大华UC50系列数字中继板(V2.0)DH-UC50-ITG92A-2E1", "level": "4", "parent": "X08.01.00768"}, {"code": "*********.10181", "description": "UC50系列模拟用户中继板(配套云平台)", "level": "4", "parent": "X08.01.00768"}, {"code": "*********.10175", "description": "UC5000系列融合通信云平台主机", "level": "4", "parent": "X08.01.00768"}, {"code": "*********.10080", "description": "国内大华UC1000数字调度台升级版DH-UC1000-22T-A", "level": "4", "parent": "X08.01.00769"}, {"code": "*********.10073", "description": "企业应急指挥调度系统标准功能模块DH-DSS-X9000-PRO", "level": "4", "parent": "X08.01.00771"}, {"code": "*********.10286", "description": "机械硬盘-ST8000NM017B-8TB-256MB-7200RPM-3.5英寸-3.5-英寸-SATA接口-SATA接口-标准盘（2TJ103-003）", "level": "4", "parent": "X08.01.00773"}, {"code": "*********.10348", "description": "网络视频存储服务器 国内大华 DH-EVS5124S-L", "level": "4", "parent": "X08.01.00773"}, {"code": "*********.15766", "description": "国内大华神算智能视频监控一体机DH-IVSS708-S1-1M-V2", "level": "4", "parent": "X08.01.00774"}, {"code": "*********.15767", "description": "国内大华神算智能视频监控一体机DH-IVSS708-S1-2M-V2", "level": "4", "parent": "X08.01.00774"}, {"code": "*********.10774", "description": "国内大华监管事件检测器DH-IVS-IP8000-MIE", "level": "4", "parent": "X08.01.00775"}, {"code": "*********.11292", "description": "人员进出可视化模块DH-DSS-P9500-CC-PA", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.11253", "description": "门禁管理模块DH-DSS-P9500-DSPC-AccessControl", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.11983", "description": "司法管理通道授权/百路DH-DSS-P9100-Base-VideoMgr", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.10345", "description": "司法基础应用DH-DSS-P9100-Base", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.11291", "description": "社区矫正移动指挥模块DH-DSS-P9500-CC-MC", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.11286", "description": "社区矫正基础业务模块DH-DSS-P9500-CC-DBase", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.11281", "description": "司法监所视频督查(导)模块DH-DSS-P9500-DSPC-Censorship", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.11254", "description": "应急报警管理模块DH-DSS-P9500-DSPC-Alarm", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.12008", "description": "司法门禁通道管理授权/路", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.11260", "description": "人像布控模块DH-DSS-P9500-DSPC-FaceBase", "level": "4", "parent": "X08.01.00776"}, {"code": "*********.12641", "description": "司法行业标准服务器DH-DSS-P9100S2A-E-HW", "level": "4", "parent": "X08.01.00777"}, {"code": "*********.13450", "description": "MA022X-01-硬盘盒组件", "level": "4", "parent": "X08.01.00777"}, {"code": "*********.11243", "description": "视频国标对接网关基础服务DH-AGS-B9101-Base", "level": "4", "parent": "X08.01.00779"}, {"code": "*********.10512", "description": "国内大华视频质量诊断智能服务器DH-IVS-VQ8000-1000", "level": "4", "parent": "X08.01.00780"}, {"code": "*********.10210", "description": "大华智能运维管理平台基础模块DH-NMS-B-Base（自带1000路通道授权）", "level": "4", "parent": "X08.01.00780"}, {"code": "*********.12362", "description": "大华智能运维管理平台(服务器)DH-NMS-B9100S3D-HW/10000", "level": "4", "parent": "X08.01.00780"}, {"code": "*********.10211", "description": "大华智能运维管理平台1000路授权DH-NMS-B-Channel", "level": "4", "parent": "X08.01.00780"}, {"code": "*********.11576", "description": "大华智能运维管理平台报修流程模块DH-NMS-Workflow", "level": "4", "parent": "X08.01.00780"}, {"code": "*********.12643", "description": "司法行业小型服务器DH-DSS-P9100S2A-L-HW", "level": "4", "parent": "X08.01.00816"}, {"code": "*********.11465", "description": "国内大华65寸悦享款智能会议平板(带摄像头)-共享法庭DH-LPH65-MC410-B", "level": "4", "parent": "X08.01.00817"}, {"code": "*********.11415", "description": "国内大华身份证识别模块DH-PKP-ID0A", "level": "4", "parent": "X08.01.00817"}, {"code": "*********.10718", "description": "DH-SmartPSSPlus-WIN64软件", "level": "4", "parent": "X08.01.00818"}, {"code": "*********.11831", "description": "国内大华10寸人脸门禁一体机K款（室内、测温、IC卡、触摸、WiFi）DH-ASI9213K-WT", "level": "4", "parent": "X08.01.00818"}, {"code": "*********.10017", "description": "国内大华280KG单门带锁信号磁力锁DH-ASF280AL-L", "level": "4", "parent": "X08.01.00818"}, {"code": "*********.10019", "description": "专用飞碟型防暴拾音器-HS-26S", "level": "4", "parent": "X08.01.00843"}, {"code": "*********.36694", "description": "中文大华200万定焦分离式针孔网络摄像机(方平86盒套装)DH-IPC-HUM7243-FR-BOX4-0280B-JS", "level": "4", "parent": "X08.01.00843"}, {"code": "*********.39733", "description": "中文大华400万双光人车警戒定焦防暴半球网络摄像机DH-IPC-HDBW5443R1-YL-PV-AS-0360B-S3", "level": "4", "parent": "X08.01.00843"}, {"code": "*********.10249", "description": "中文大华240米周界安防雷达DH-PFR4K-B240", "level": "4", "parent": "X08.01.00844"}, {"code": "*********.13042", "description": "中文大华热成像全域摄像机DH-TPC-SDA8441-B25Z45-BM-S22", "level": "4", "parent": "X08.01.00844"}, {"code": "*********.14271", "description": "中文大华测温型热成像单目枪摄像机-DH-TPC-BF5601-TB19-DC-S2", "level": "4", "parent": "X08.01.00844"}, {"code": "*********.16101", "description": "恒定大光圈惊鸿灵犀-双400万合智能柔光双色网络球机25X(8-32mm)", "level": "4", "parent": "X08.01.00844"}, {"code": "*********.14523", "description": "中文大华睿界200万双光变焦智能出入口杆式抓拍一体机DH-IPMECS-2232-Z", "level": "4", "parent": "X08.01.00845"}, {"code": "*********.43556", "description": "中文大华双枪-双400万双光智能警戒双变焦枪型网络摄像机DH-IPC-MFW54443V-ZYH-PV-27135/27135-JM", "level": "4", "parent": "X08.01.00845"}, {"code": "*********.39222", "description": "中文大华旗舰哈勃-2400万270°AR哈勃守望者网络摄像机DH-PSDW82449M-A270-D440-HM-AR-DC36V-S3 ", "level": "4", "parent": "X08.01.00846"}, {"code": "*********.40017", "description": "中文大华400万易智能红外变焦防暴半球网络摄像机DH-IPC-HDBW5449E-ZYH-2712-S3", "level": "4", "parent": "X08.01.00847"}, {"code": "*********.42404", "description": "中文大华离岗检测/火焰检测-400万火焰检测离岗检测双光变焦防暴半球网络摄像机DH-IPC-HDBW5443R1-ZAST-27135-LGJC/HYJC", "level": "4", "parent": "X08.01.00847"}, {"code": "*********.43301", "description": "中文大华mini天镜-400万融智能双光变焦枪型网络摄像机DH-IPC-HFW8449DK2-ZRL-IL4-2712-S3", "level": "4", "parent": "X08.01.00848"}, {"code": "*********.13895", "description": "SD8A中文大华56倍400W激光网络球型摄像机", "level": "4", "parent": "X08.01.00848"}, {"code": "*********.42242", "description": "中文大华mini天镜-400万融智能双光变焦枪型网络摄像机DH-IPC-HFW8449F1-ZRL-IL4-2712-DC12AC24V-S3", "level": "4", "parent": "X08.01.00848"}, {"code": "*********.39254", "description": "中文大华旗舰天阙-400万合智能旗舰双变焦天阙网络摄像机DH-IPC-MFW84449-ZCHM-G-0856/0832-S3", "level": "4", "parent": "X08.01.00848"}, {"code": "*********.34983", "description": "中文大华200万1拖2分离式针孔网络摄像机(主机盒)DH-IPC-HUM7243-RL-E2", "level": "4", "parent": "X08.01.00849"}, {"code": "*********.34960", "description": "中文中性200万定焦分离式针孔网络摄像机(方锥镜头)IPC-HUM7243-L2-0280B", "level": "4", "parent": "X08.01.00849"}, {"code": "*********.38482", "description": "中文大华防油污-400万防油污红外定焦枪型网络摄像机DH-IPC-HFW7443M-AS-SFC-IL-X-0360B", "level": "4", "parent": "X08.01.00850"}, {"code": "*********.10286", "description": "双头静音锁-DH-ASF601B", "level": "4", "parent": "X08.01.00855"}, {"code": "*********.10010", "description": "插墙式电源适配器-AC100V~240V-24W-12V2A-V级-ADS-25FSG-12 12024GPCN-Φ5.5×Φ2.1×10-国标", "level": "4", "parent": "X08.01.00855"}, {"code": "*********.10248", "description": "500KG单门磁力锁-DH-ASF500A-DH-ASF500A", "level": "4", "parent": "X08.01.00855"}, {"code": "*********.12409", "description": "国内大华室内C款铁箱型门禁控制器（双门双向）DH-ASC4202C-D", "level": "4", "parent": "X08.01.00855"}, {"code": "*********.10283", "description": "闭门器（80-100KG）-DH-ASF104S", "level": "4", "parent": "X08.01.00855"}, {"code": "*********.10271", "description": "500KG磁力锁支架（L型）-DH-ASF500L", "level": "4", "parent": "X08.01.00855"}, {"code": "*********.10155", "description": "磁开关入侵探测器-DH-ARD312-铁门门磁", "level": "4", "parent": "X08.01.00855"}, {"code": "*********.12400", "description": "国内大华室内C款铁箱型门禁控制器（单门双向）DH-ASC4201C-D", "level": "4", "parent": "X08.01.00855"}, {"code": "*********.11419", "description": "国内大华发卡器(IC，室内)DH-ASM100-V1", "level": "4", "parent": "X08.01.00856"}, {"code": "*********.11502", "description": "国内大华电容指纹采集器(指纹+IC+ID+CPU，室内)-DH-ASM101A-C", "level": "4", "parent": "X08.01.00856"}, {"code": "*********.11351", "description": "国内大华4.3寸单屏台式自助采集终端DH-ASHZ200A-W", "level": "4", "parent": "X08.01.00856"}, {"code": "*********.10005", "description": "IC卡-IC-M1-复旦微IC", "level": "4", "parent": "X08.01.00856"}, {"code": "*********.12470", "description": "国内大华门神7寸智能门禁一体机（IC卡、ID卡、WIFI）DH-ASI7213H-DW", "level": "4", "parent": "X08.01.00857"}, {"code": "*********.11670", "description": "国内大华读卡器方形B款 (室外，IC，不带按键) DH-ASR1100B-V1", "level": "4", "parent": "X08.01.00858"}, {"code": "*********.10018", "description": "双防区扩展模块DH-ARM802", "level": "4", "parent": "X08.01.00860"}, {"code": "*********.10045-001", "description": "电子围栏用双防区脉冲主机-DH-AREF-C02", "level": "4", "parent": "X08.01.00860"}, {"code": "*********.10044-001", "description": "电子围栏用单防区脉冲主机-DH-AREF-C01", "level": "4", "parent": "X08.01.00860"}, {"code": "*********.10197", "description": "单防区输出扩展模块DH-ARM911", "level": "4", "parent": "X08.01.00861"}, {"code": "*********.10148", "description": "三光束主动红外探测器-DH-ARD631-200-探测范围200米", "level": "4", "parent": "X08.01.00861"}, {"code": "*********.10151", "description": "四光束主动红外探测器-DH-ARD641-100-探测范围100米", "level": "4", "parent": "X08.01.00861"}, {"code": "*********.10152", "description": "四光束主动红外探测器-DH-ARD641-150-探测范围150米", "level": "4", "parent": "X08.01.00861"}, {"code": "*********.10146", "description": "三光束主动红外探测器-DH-ARD631-100-探测范围100米", "level": "4", "parent": "X08.01.00861"}, {"code": "*********.10052", "description": "双光束主动红外探测器-非ROHS", "level": "4", "parent": "X08.01.00861"}, {"code": "*********.10153", "description": "四光束主动红外探测器-DH-ARD641-200-探测范围200米", "level": "4", "parent": "X08.01.00861"}, {"code": "*********.14284", "description": "国内大华智能安检服务器DH-ISC-S7408-2VHD", "level": "4", "parent": "X08.01.00865"}, {"code": "*********.10108", "description": "国内大华普通测温人脸安检门主机DH-ISC-D718-M", "level": "4", "parent": "X08.01.00865"}, {"code": "*********.10295-002", "description": "手持金属探测器-非ROHS", "level": "4", "parent": "X08.01.00865"}, {"code": "*********.10098", "description": "国内大华智能X射线检查系统DH-ISC-M5030A", "level": "4", "parent": "X08.01.00865"}, {"code": "*********.14287", "description": "国内大华智能安检服务器DH-ISC-S7408-VHD", "level": "4", "parent": "X08.01.00865"}, {"code": "*********.14173", "description": "国内金属探测安检门回形针门板ISC-D218-D", "level": "4", "parent": "X08.01.00865"}, {"code": "*********.10323", "description": "便携式爆炸物毒品探测仪", "level": "4", "parent": "X08.01.00865"}, {"code": "*********.16166", "description": "SD4A中文大华25倍400W高清POE网络吸顶球型摄像机（鸿蒙静音版）", "level": "4", "parent": "X08.01.00877"}, {"code": "*********.10139", "description": "中文大华-桌面式电源适配器-AC100V~240V-12V1A-VI级-国标-DH-PFM321-Φ5.5×Φ2.1×12mm", "level": "4", "parent": "X08.01.00877"}, {"code": "*********.46580", "description": "中文大华司法专用半球-400万融智能红外变焦防暴半球网络摄像机DH-IPC-HDBW5443R1-ZAST-27135-JWZY", "level": "4", "parent": "X08.01.00877"}, {"code": "*********.10262", "description": "拾音器-HS-51S", "level": "4", "parent": "X08.01.00879"}, {"code": "*********.10261", "description": "拾音器-HS-50S", "level": "4", "parent": "X08.01.00879"}, {"code": "*********.10195", "description": "LED屏-DH-HH665-AC220V-≤15W-RS485-温湿度显示-580x380x37mm", "level": "4", "parent": "X08.01.00880"}, {"code": "*********.15971", "description": "国内大华双电源审讯专用硬盘录像机(BD版) DH-HVR0405FD-S-HR", "level": "4", "parent": "X08.01.00881"}, {"code": "*********.11365", "description": "国内大华32寸S200智能显示屏-DHL32-S200", "level": "4", "parent": "X08.01.00882"}, {"code": "*********.10080", "description": "紧急按钮-DH-ARD812-非ROHS", "level": "4", "parent": "X08.01.00883"}, {"code": "*********.10604", "description": "审讯谈话软件DH-SXTH", "level": "4", "parent": "X08.01.00884"}, {"code": "*********.13550", "description": "智能柜（随身物品主柜）DH-IFC-GM12-10A", "level": "4", "parent": "X08.01.00886"}, {"code": "*********.11503", "description": "国内大华发卡器(CPU+IC+ID)-DH-ASM100A-C", "level": "4", "parent": "X08.01.00886"}, {"code": "*********.13551", "description": "智能柜（随身物品副柜）DH-IFC-GS16-10A", "level": "4", "parent": "X08.01.00886"}, {"code": "*********.11495", "description": "国内大华四门双向门禁控制器C款(铁箱型，室内)-DH-ASC2204C-D-SL4", "level": "4", "parent": "X08.01.00888"}, {"code": "*********.10267", "description": "280KG磁力锁支架（F型）-DH-ASF280F", "level": "4", "parent": "X08.01.00888"}, {"code": "*********.11665", "description": "国内大华读卡器方形M款金属防暴读卡器 (室内，CPU，带机械按键) DH-ASR1101M-C -V1", "level": "4", "parent": "X08.01.00888"}, {"code": "*********.11638", "description": "国内大华读卡器方形A款 (室内，CPU，不带按键) DH-ASR1100A-C-V1", "level": "4", "parent": "X08.01.00888"}, {"code": "*********.10275", "description": "906款出门按钮-DH-ASF906", "level": "4", "parent": "X08.01.00889"}, {"code": "*********.11494", "description": "国内大华两门双向门禁控制器C款(铁箱型，室内)-DH-ASC2202C-D-SL4", "level": "4", "parent": "X08.01.00889"}, {"code": "*********.10159", "description": "国密CPU卡-MW5108(SM_CPU)", "level": "4", "parent": "X08.01.00889"}, {"code": "*********.10172", "description": "中文大华全自动液压一体升降柱（8mm）-非ROHS", "level": "4", "parent": "X08.01.00891"}, {"code": "*********.10121", "description": "国内大华43寸S200监控显示器-DH-LM43-S200", "level": "4", "parent": "X08.01.00892"}, {"code": "*********.10060", "description": "国内大华65寸F400监视器-DH-LM65-F400", "level": "4", "parent": "X08.01.00893"}, {"code": "*********.40903", "description": "中文大华双光双摄-400万易智能双光双摄网络摄像机DH-IPC-MFW54249DK1-ZYH-0832-S2", "level": "4", "parent": "X08.01.00895"}, {"code": "*********.13396", "description": "中文大华哨兵系列400万全结构化轻卡口DH-CP435-RU1F-ZF1030-C2", "level": "4", "parent": "X08.01.00898"}, {"code": "*********.40968", "description": "中文大华双光双摄-400万融智能双光双摄网络摄像机DH-IPC-MFW84449K1-ZRL-X-0832-S4", "level": "4", "parent": "X08.01.00898"}, {"code": "*********.42200", "description": "中文大华mini天镜-400万融智能标准枪型网络摄像机DH-IPC-HF8449F-ZRL-S3", "level": "4", "parent": "X08.01.00898"}, {"code": "*********.11204", "description": "国内大华明行系列智慧云班牌（双刷卡带门禁和话机防拽卡扣）DH-ECH22-HAI200AA", "level": "4", "parent": "X08.01.00899"}, {"code": "*********.40138", "description": "中文大华400万双光人车警戒变焦防暴半球网络摄像机DH-IPC-HDBW5443R1-ZYL-PV-AS-0624-S3", "level": "4", "parent": "X08.01.00899"}, {"code": "*********.11043", "description": "协同会商模块DH-DSS-X9000-XTHS", "level": "4", "parent": "X08.01.00908"}, {"code": "*********.10319", "description": "应急软件业务支持费", "level": "4", "parent": "X08.01.00908"}, {"code": "*********.11051", "description": "广播通知模块DH-DSS-X9000-GBTZ", "level": "4", "parent": "X08.01.00908"}, {"code": "*********.11045", "description": "视频会议接入模块DH-DSS-X9000-MCU", "level": "4", "parent": "X08.01.00908"}, {"code": "*********.10252", "description": "语音调度模块DH-DSS-X9000-YYDD", "level": "4", "parent": "X08.01.00908"}, {"code": "*********.11057", "description": "应急-app模块DH-DSS-X-App", "level": "4", "parent": "X08.01.00908"}, {"code": "*********.10063", "description": "国内大华会议电视系统多点控制单元(MCU)DH-VCS-MCU9530", "level": "4", "parent": "X08.01.00909"}, {"code": "*********.10039", "description": "国内大华融合调度UC80系列接口板DH-UC80-MIF02DA", "level": "4", "parent": "X08.01.00910"}, {"code": "*********.10062", "description": "国内大华融合调度UC80系列模拟用户板DH-UC80-ISG03DA-56S", "level": "4", "parent": "X08.01.00910"}, {"code": "*********.10064", "description": "国内大华融合调度UC80系列模拟中继板DH-UC80-ISG04DA-4808SO", "level": "4", "parent": "X08.01.00910"}, {"code": "*********.10137", "description": "融合通信云UC80系列主控板\tDH-UC80-OPC9010A", "level": "4", "parent": "X08.01.00910"}, {"code": "*********.10066", "description": "国内大华融合调度UC80系列数字中继板DH-UC80-ITG01DA-2E1", "level": "4", "parent": "X08.01.00910"}, {"code": "*********.0071", "description": "DH-MSP 12U多业务接入平台整机", "level": "4", "parent": "X08.01.00910"}, {"code": "*********.10176", "description": "UC5000系列融合通信语音网关", "level": "4", "parent": "X08.01.00911"}, {"code": "*********.10074", "description": "国内大华UC3000系列集群音频接入网关DH-UC3000-AFI01A", "level": "4", "parent": "X08.01.00912"}, {"code": "*********.10145", "description": "国内大华10寸调度终端DH-UC1010-10T-W", "level": "4", "parent": "X08.01.00913"}, {"code": "*********.10081", "description": "国内大华UC1000双屏调度台升级版DH-UC1000-22T-DE", "level": "4", "parent": "X08.01.00913"}, {"code": "*********.12600", "description": "司法行业高配服务器DH-DSS-P9100S2D-H-HW", "level": "4", "parent": "X08.01.00914"}, {"code": "*********.10435", "description": "云计算超融合中配服务器", "level": "4", "parent": "X08.01.00915"}, {"code": "*********.10129", "description": "MagicHCI软件-超融合 X86 license", "level": "4", "parent": "X08.01.00915"}, {"code": "*********.10047", "description": "大华NTP时间服务器", "level": "4", "parent": "X08.01.00916"}, {"code": "*********.10848", "description": "视频云存储（标准版）-数据存储节点DH-CSS8236AC-HVER-8T", "level": "4", "parent": "X08.01.00917"}, {"code": "*********.10847", "description": "视频云存储-元数据服务器硬件DH-CSS9100AC-HVER-HW", "level": "4", "parent": "X08.01.00917"}, {"code": "*********.11677", "description": "司法业务对接模块DH-DSS-P9500-DSPC-BusinessConnection", "level": "4", "parent": "X08.01.00921"}, {"code": "*********.11986", "description": "司法电子地图引擎DH-DSS-P9100-Base-Map", "level": "4", "parent": "X08.01.00923"}, {"code": "*********.11250", "description": "司法监所通用基础业务模块DH-DSS-P9500-DSPC-BaseVideo", "level": "4", "parent": "X08.01.00923"}, {"code": "*********.11255", "description": "巡更管理模块DH-DSS-P9500-DSPC-Patrol", "level": "4", "parent": "X08.01.00923"}, {"code": "*********.11257", "description": "IP对讲管理模块DH-DSS-P9500-DSPC-Intercom", "level": "4", "parent": "X08.01.00923"}, {"code": "*********.11739", "description": "可视对讲管理模块DH-DSS-P9500-DSPC-IntercomManager", "level": "4", "parent": "X08.01.00923"}, {"code": "*********.11256", "description": "周界电网管理模块DH-DSS-P9500-DSPC-Perimeter", "level": "4", "parent": "X08.01.00923"}, {"code": "*********.11261", "description": "过车管理模块DH-DSS-P9500-DSPC-VehicleRecord", "level": "4", "parent": "X08.01.00924"}, {"code": "*********.11265", "description": "司法监所出收工点名模块DH-DSS-P9500-DSPC-WorkCount", "level": "4", "parent": "X08.01.00924"}, {"code": "*********.11270", "description": "司法监所所外押解模块DH-DSS-P9500-DSPC-OutEscort", "level": "4", "parent": "X08.01.00924"}, {"code": "*********.11264", "description": "监舍点名模块DH-DSS-P9500-DSPC-PrisonCount", "level": "4", "parent": "X08.01.00924"}, {"code": "*********.11266", "description": "司法监所工间点名模块DH-DSS-P9500-DSPC-FactoryCount", "level": "4", "parent": "X08.01.00924"}, {"code": "*********.11263", "description": "AB门进出流程管理模块DH-DSS-P9500-DSPC-ABDoor", "level": "4", "parent": "X08.01.00924"}, {"code": "*********.11258", "description": "司法监所AR实景模块DH-DSS-P9500-DSPC-AR", "level": "4", "parent": "X08.01.00924"}, {"code": "*********.11274", "description": "司法监所人车网格化模块DH-DSS-P9500-DSPC-CommandCoordinating", "level": "4", "parent": "X08.01.00925"}, {"code": "*********.11479", "description": "大华智能运维管理平台资产管理模块DH-NMS-B-AM", "level": "4", "parent": "X08.01.00927"}, {"code": "*********.10013", "description": "大华智能运维管理平台DH-NMS-B9100-PRO/10000", "level": "4", "parent": "X08.01.00928"}, {"code": "*********.10012", "description": "大华智能运维管理平台DH-NMS-B9100-PRO(5000)", "level": "4", "parent": "X08.01.00928"}, {"code": "*********.10011", "description": "大华智能运维管理平台DH-NMS-B9100-PRO(1000)", "level": "4", "parent": "X08.01.00928"}, {"code": "*********.12071", "description": "大华智能运维管理平台（服务器）DH-NMS-B9100S3D-HW-E", "level": "4", "parent": "X08.01.00929"}, {"code": "*********.10514", "description": "国内大华视频质量诊断智能服务器DH-IVS-VQ8000-10000", "level": "4", "parent": "X08.01.00930"}, {"code": "*********.10515", "description": "国内大华视频质量诊断智能服务器硬件DH-IVS-VQ8000-HW", "level": "4", "parent": "X08.01.00930"}, {"code": "*********.10513", "description": "国内大华视频质量诊断智能服务器DH-IVS-VQ8000-5000", "level": "4", "parent": "X08.01.00930"}, {"code": "*********.11673", "description": "大华智能运维管理平台5000路通道视频质量巡检扩容授权DH-NMS-B-VQD-Cluster/5000", "level": "4", "parent": "X08.01.00931"}, {"code": "*********.11674", "description": "大华智能运维管理平台10000路通道视频质量巡检扩容授权DH-NMS-B-VQD-Cluster/10000", "level": "4", "parent": "X08.01.00931"}, {"code": "*********.11672", "description": "大华智能运维管理平台1000路通道视频质量巡检扩容授权DH-NMS-B-VQD-Cluster/1000", "level": "4", "parent": "X08.01.00931"}, {"code": "*********.37518", "description": "中文大华300万红外定焦防暴半球网络摄像机DH-IPC-HDBW8343R1-0185B", "level": "4", "parent": "X08.01.00936"}, {"code": "*********.39768", "description": "中文大华400万双光人车警戒定焦枪型网络摄像机DH-IPC-HFW5443M1-YL-PV-AS-0800B-S3", "level": "4", "parent": "X08.01.00937"}, {"code": "*********.14444", "description": "SD6C3中文大华32倍400W声光警戒网络智能球机", "level": "4", "parent": "X08.01.00937"}, {"code": "*********.15520", "description": "SD8A1中文大华40倍400W星光网络球机（35114）", "level": "4", "parent": "X08.01.00937"}, {"code": "*********.44340", "description": "中文大华百米周界-3×200万红外定焦三目周界枪型网络摄像机DH-IPC-MFW5643T2-E3-PV-0360/1200/2500B", "level": "4", "parent": "X08.01.00937"}, {"code": "*********.10923", "description": "国内大华1U监管事件检测智能服务器DH-IVS-IP9000-H-RM1", "level": "4", "parent": "X08.01.00946"}, {"code": "*********.12377", "description": "国内大华可视双按键盲文防暴紧急求助终端DH-VTA2502G", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.10283", "description": "机械硬盘-ST4000NM000B-4TB-256MB-7200RPM-3.5英寸-3.5-英寸-SATA接口-SATA接口-标准盘（2TF100-003）", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.12910-001", "description": "其他线-(RF高频线,SMA公头转SMA公头)-黑色-3000mm", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.12112", "description": "国内大华10寸B款安卓管理机 DH-VTS8A40B-CG", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.17792-000", "description": "ZA042X-00-暗装沉壳-DH920WF1", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.10033", "description": "国内大华高速存储卡DH-TF-P100/128GB", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.45226", "description": "中文中性200万定焦分离式方锥针孔镜头组件（加入配单必须选配镜头线缆）IPC-HUM7243-L2-0280B-S2", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.45220", "description": "中文大华200万1拖1分离式针孔主机盒DH-IPC-HUM7243-E1-S2", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.10048", "description": "中文大华嵌入式防水（暴）拾音器（含降噪控制软件V1.0）", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.10143-001", "description": "桌面式电源适配器-AC100V~240V-48W-12V4A-VI级-带磁环-Φ5.5×Φ2.1×10", "level": "4", "parent": "X08.01.01011"}, {"code": "*********.10111", "description": "CPU卡读写器-ASM103-非ROHS", "level": "4", "parent": "X08.01.01012"}, {"code": "*********.10123", "description": "国内大华闭门器（80-100KG）DH-ASF104R", "level": "4", "parent": "X08.01.01012"}, {"code": "*********.10553", "description": "便携审讯同步录音录像硬盘录像机", "level": "4", "parent": "X08.01.01013"}, {"code": "*********.10554", "description": "便携审讯同步录音录像硬盘录像机-#项目专用#", "level": "4", "parent": "X08.01.01013"}, {"code": "*********.15970", "description": "国内大华双电源审讯专用硬盘录像机(DVD版) DH-HVR0405FD-S-HR", "level": "4", "parent": "X08.01.01014"}, {"code": "*********.11657", "description": "公安监管智慧督导模块DH-DSS-P9500-DSJG-Censorship", "level": "4", "parent": "X08.01.01016"}, {"code": "*********.10203", "description": "公安监管视频结构化模块DH-DSS-P9500-DSJG-VideoCameraStructurization", "level": "4", "parent": "X08.01.01016"}, {"code": "*********.10182", "description": "大华智能运维管理平台视图巡检DH-NMS-C-VDA", "level": "4", "parent": "X08.01.01021"}, {"code": "*********.10518", "description": "心理矫正终端-非ROHS", "level": "4", "parent": "X08.01.01061"}, {"code": "*********.13093", "description": "国内大华USB相机产品全能款DH-VCS-UC400", "level": "4", "parent": "X08.01.01094"}, {"code": "*********.12127", "description": "国内大华10寸智能门禁一体机K款（室外、IC卡）DH-ASI9213K-V1", "level": "4", "parent": "X08.01.01115"}, {"code": "*********.14883", "description": "单变焦灵犀SDT5X中文大华双400W高清网络摄像机(10-50mm)(6mm)", "level": "4", "parent": "X08.01.01117"}, {"code": "*********.14208", "description": "国内大华双显示器操作台DH-ISC-2DCC", "level": "4", "parent": "X08.01.01120"}, {"code": "*********.10273", "description": "国内中性6550出入口接物架ISC-M6550-IORT", "level": "4", "parent": "X08.01.01120"}, {"code": "*********.10144", "description": "无线微震车辆生命检测系统（含电磁微震融合检测系统V2.0软件）DH-UCS-VMW-非ROHS", "level": "4", "parent": "X08.01.01122"}, {"code": "*********.14209", "description": "国内中性单显示器操作台ISC-1DCC", "level": "4", "parent": "X08.01.01154"}, {"code": "*********.13795", "description": "国内中性 接物架 OEM-ISC-M6550-IORT", "level": "4", "parent": "X08.01.01154"}, {"code": "*********.12918", "description": "中文大华慧系列二代300万AI一体化抓拍单元（卡口专用）DH-CP302-RU2G-C1", "level": "4", "parent": "X08.01.01161"}, {"code": "*********.10125", "description": "中文大华300万像素1/1.8英寸4-18mm自动光圈手动变焦镜头-DH-OPT-118F0418D-IR3M", "level": "4", "parent": "X08.01.01161"}, {"code": "*********.10145", "description": "三光束主动红外探测器-DH-ARD631-50-探测范围50米", "level": "4", "parent": "X08.01.01161"}, {"code": "*********.10184", "description": "中文大华暖光LED频闪灯15°DH-ITALE-060AA-PW3515", "level": "4", "parent": "X08.01.01161"}, {"code": "*********.10010", "description": "中文大华行业型79G出入口防砸雷达", "level": "4", "parent": "X08.01.01162"}, {"code": "*********.10106", "description": "中文大华室外3行2字双色诱导屏DH-ITSXS-1301-32", "level": "4", "parent": "X08.01.01162"}, {"code": "*********.10251", "description": "中文大华行业变频右向直臂道闸 4米（2秒）-非ROHS", "level": "4", "parent": "X08.01.01162"}, {"code": "*********.12822", "description": "电子标识固定式四通道读写器NVIR-GDGW-DHFT000B", "level": "4", "parent": "X08.01.01164"}, {"code": "*********.37379", "description": "中文大华合智能800万至尊版双拼天阙网络摄像机DH-IPC-MFW88849-ZCHH-E3-2080-S3", "level": "4", "parent": "X08.01.01165"}, {"code": "*********.10007", "description": "Windows双屏台式智能访客机-DH-ASV2001B", "level": "4", "parent": "X08.01.01173"}, {"code": "*********.10053-000", "description": "DH-PFB121W-普通壁装枪机支架-DH816W2-中文大华", "level": "4", "parent": "X08.01.01179"}, {"code": "*********.39766", "description": "中文大华400万双光人车警戒定焦枪型网络摄像机DH-IPC-HFW5443M1-YL-PV-AS-0360B-S3", "level": "4", "parent": "X08.01.01179"}, {"code": "*********.14450", "description": "SD8A1中文大华40倍400W超星光网络球机", "level": "4", "parent": "X08.01.01180"}, {"code": "*********.14533", "description": "国内大华智慧物流专用分析一体机DH-IVSS724WL-16M", "level": "4", "parent": "X08.01.01186"}, {"code": "*********.14532", "description": "国内大华智慧物流专用分析一体机DH-IVSS716WL-8M", "level": "4", "parent": "X08.01.01186"}, {"code": "*********.10021", "description": "月台状态LED屏双行四字（分辨率：64*32）-非ROHS", "level": "4", "parent": "X08.01.01187"}, {"code": "*********.10024", "description": "月台状态LED屏双行八字（分辨率：128*32）-非ROHS", "level": "4", "parent": "X08.01.01187"}, {"code": "*********.10023", "description": "月台状态LED屏单行八字（分辨率：128*16）-非ROHS", "level": "4", "parent": "X08.01.01187"}, {"code": "*********.10022", "description": "月台状态LED屏单行四字（分辨率：64*16）-非ROHS", "level": "4", "parent": "X08.01.01187"}, {"code": "*********.14824", "description": "SD8A1中文大华40倍400W星光网络球机（GPS）", "level": "4", "parent": "X08.01.01188"}, {"code": "*********.41412", "description": "中文大华400万双光融智能变焦防暴半球网络摄像机DH-IPC-HDBW8443R1-ZRL-27135-S3", "level": "4", "parent": "X08.01.01190"}, {"code": "*********.39228", "description": "中文大华天鹰-3200万全景红外定焦四目拼接(带GPS)枪型网络摄像机DH-IPC-PFW83240-A180-G-DC12AC24V-S2", "level": "4", "parent": "X08.01.01192"}, {"code": "*********.36591", "description": "中文大华天镜PRO-400万全智能暖光变焦天镜枪型网络摄像机DH-IPC-HFW8449L-ZMVS-LED-0856-S3", "level": "4", "parent": "X08.01.01194"}, {"code": "*********.38310", "description": "中文大华经典哈勃-1600万360°哈勃守望者网络摄像机DH-PSDW81649M-A360-D440-HL-DC36V-S3", "level": "4", "parent": "X08.01.01199"}, {"code": "*********.13477", "description": "中文大华哨兵系列400万轻型雷达视频一体机 DH-CP435-SU1F-JQE", "level": "4", "parent": "X08.01.01202"}, {"code": "*********.13996", "description": "国内大华智能X射线检查系统DH-ISC-M5030", "level": "4", "parent": "X08.01.01210"}, {"code": "*********.10104", "description": "国内大华接物架DH-ISC-M6550-IORT", "level": "4", "parent": "X08.01.01210"}, {"code": "*********.12436", "description": "浩睿企业基础_智能物联综合管理平台（一体机）", "level": "4", "parent": "X08.01.01211"}, {"code": "*********.10365", "description": "网络视频存储服务器 国内大华 DH-EVS5124S-H", "level": "4", "parent": "X08.01.01211"}, {"code": "*********.10869", "description": "国内大华视频综合平台增强型主机DH-M70-4U-E", "level": "4", "parent": "X08.01.01211"}, {"code": "*********.10277", "description": "国内大华网络键盘DH-NKB5200", "level": "4", "parent": "X08.01.01211"}, {"code": "*********.12292", "description": "智能物联综合管理平台（标准版服务器）DH-ICC-B8900S4F-E32", "level": "4", "parent": "X08.01.01213"}, {"code": "*********.12288", "description": "智能物联综合管理平台（企业版服务器）DH-ICC-B8900S4F-U64", "level": "4", "parent": "X08.01.01213"}, {"code": "*********.12676", "description": "智能物联综合管理平台(旗舰版服务器)", "level": "4", "parent": "X08.01.01213"}, {"code": "*********.10161", "description": "浩睿企业基础_出入口车道数量授权（规模数量）", "level": "4", "parent": "X08.01.01214"}, {"code": "*********.10144", "description": "浩睿企业基础_视频通道路数授权（规模数量）", "level": "4", "parent": "X08.01.01214"}, {"code": "*********.10142", "description": "浩睿企业基础_对讲门禁通道路数授权（规模数量）", "level": "4", "parent": "X08.01.01214"}, {"code": "*********.10196", "description": "设备运维系统_运维通道路数授权", "level": "4", "parent": "X08.01.01214"}, {"code": "*********.10130", "description": "浩睿企业基础_门禁通道路数授权（规模数量）", "level": "4", "parent": "X08.01.01214"}, {"code": "*********.10084", "description": "浩睿企业基础_智能物联综合管理平台（纯软平台）", "level": "4", "parent": "X08.01.01214"}, {"code": "*********.10164", "description": "浩睿企业基础_车位数量授权（规模数量）", "level": "4", "parent": "X08.01.01214"}, {"code": "*********.10080", "description": "智能目标布控", "level": "4", "parent": "X08.01.01215"}, {"code": "*********.10104", "description": "浩睿企业基础_AR云景业务系统（业务组件 ）", "level": "4", "parent": "X08.01.01215"}, {"code": "*********.10210", "description": "浩睿企业基础_AR云景通道路数授权（规模数量）", "level": "4", "parent": "X08.01.01215"}, {"code": "*********.10087", "description": "浩睿企业基础_梯控管理系统（业务组件）", "level": "4", "parent": "X08.01.01216"}, {"code": "*********.10085", "description": "浩睿企业基础_动环管理系统（业务组件）", "level": "4", "parent": "X08.01.01216"}, {"code": "*********.10086", "description": "浩睿企业基础_访客管理系统（业务组件）", "level": "4", "parent": "X08.01.01216"}, {"code": "*********.10219", "description": "工单管理系统", "level": "4", "parent": "X08.01.01216"}, {"code": "*********.10211", "description": "浩睿企业基础_巡更管理系统（业务组件）", "level": "4", "parent": "X08.01.01216"}, {"code": "*********.10101", "description": "浩睿企业基础_考勤管理系统（业务组件）", "level": "4", "parent": "X08.01.01217"}, {"code": "*********.10302", "description": "智能空开通道授权DH-ICC-Common-VD-ASCHN", "level": "4", "parent": "X08.01.01217"}, {"code": "*********.10088", "description": "浩睿企业基础_消费管理系统（业务组件）", "level": "4", "parent": "X08.01.01217"}, {"code": "*********.10209", "description": "浩睿物流_物流月台个数授权（规模数量）", "level": "4", "parent": "X08.01.01218"}, {"code": "*********.10103", "description": "浩睿物流_物流管理系统（业务组件）", "level": "4", "parent": "X08.01.01218"}, {"code": "*********.10197", "description": "浩睿企业基础_停车管理系统（业务组件）", "level": "4", "parent": "X08.01.01218"}, {"code": "*********.10027", "description": "消防NB独立式设备接入授权（规模数量）DH-HY-P8000-NB-AUT", "level": "4", "parent": "X08.01.01219"}, {"code": "*********.10026", "description": "消防主机设备接入授权（规模数量）DH-HY-P8000-HOST-AUT", "level": "4", "parent": "X08.01.01219"}, {"code": "*********.10030", "description": "浩睿企业基础_消防业务系统（业务组件）DH-ICC-FireControl-base", "level": "4", "parent": "X08.01.01219"}, {"code": "*********.10025", "description": "消防用户信息传输装置接入授权（规模数量）DH-HY-P8000-CON-AUT", "level": "4", "parent": "X08.01.01219"}, {"code": "*********.12784", "description": "司法行业高配服务器", "level": "4", "parent": "X08.01.01315"}, {"code": "*********.10057", "description": "国内大华会议电视系统多点控制单元(MCU)DH-VCS-MCU9570-4K机箱", "level": "4", "parent": "X08.01.01316"}, {"code": "*********.10426", "description": "网络视频存储服务器 国内大华 DH-EVS7124S-H", "level": "4", "parent": "X08.01.01317"}, {"code": "*********.15968", "description": "国内大华审讯专用硬盘录像机(DVD版) DH-HVR0405FD-S-H", "level": "4", "parent": "X08.01.01318"}, {"code": "*********.10224", "description": "国内大华会议电视终端DH-VCS-TS4000V3.0", "level": "4", "parent": "X08.01.01318"}, {"code": "*********.10123", "description": "国内大华55寸S400监控显示器-DH-LM55-S400", "level": "4", "parent": "X08.01.01318"}, {"code": "*********.13791", "description": "国内大华蓝牙/有线全向数字麦克风DH-VCS-MCA500", "level": "4", "parent": "X08.01.01318"}, {"code": "*********.10278", "description": "机械硬盘-ST4000VX015-4TB-256MB-5400RPM-3.5英寸-3.5-英寸-SATA接口-SATA接口-标准盘（3CU104-500）", "level": "4", "parent": "X08.01.01318"}, {"code": "*********.10006", "description": "视频物证展示台-非ROHS", "level": "4", "parent": "X08.01.01318"}, {"code": "*********.14614", "description": "SD4A中文大华25倍400W高清POE网络吸顶球型摄像机", "level": "4", "parent": "X08.01.01319"}, {"code": "*********.10587", "description": "公安监管实战基础版", "level": "4", "parent": "X08.01.01321"}, {"code": "*********.10588", "description": "公安监管实战监室数量", "level": "4", "parent": "X08.01.01322"}, {"code": "*********.10602", "description": "公安监管实战加强版", "level": "4", "parent": "X08.01.01323"}, {"code": "*********.10603", "description": "公安监管实战平台定制", "level": "4", "parent": "X08.01.01324"}, {"code": "*********.11991", "description": "司法高可靠服务DH-DSS-P9100-Base-HA", "level": "4", "parent": "X08.01.01327"}, {"code": "*********.11988", "description": "司法GB35114A级管理服务DH-DSS-P9100-Base-Safe-GB35114A", "level": "4", "parent": "X08.01.01327"}, {"code": "*********.11984", "description": "司法管理通道授权/路DH-DSS-P9100-Base-VideoMgr(1)", "level": "4", "parent": "X08.01.01327"}, {"code": "*********.11985", "description": "司法流媒体节点DH-DSS-P9100-Base-Node", "level": "4", "parent": "X08.01.01327"}, {"code": "*********.10545", "description": "视频云存储（标准版）-数据存储节点DH-CSS7436S-VRE", "level": "4", "parent": "X08.01.01330"}, {"code": "*********.10201", "description": "网络视频存储服务器 国内大华 DH-EVS7224S", "level": "4", "parent": "X08.01.01331"}, {"code": "*********.11869", "description": "大华报警门禁接入网关", "level": "4", "parent": "X08.01.01333"}, {"code": "*********.13789", "description": "国内华创高性能台式计算机DH-HDC5100F-飞腾D2000/8-8GB*1-256GB M.2 NVMe SSD*1-AMD520 1G HDMI+VGA-含光驱-板载GE*2-200W电源-预装试用版Kylin-无显示器", "level": "4", "parent": "X08.01.01334"}, {"code": "*********.10284", "description": "银河麒麟桌面操作系统V10正版授权_含3年免费升级服务_Desktop-V10-SP1_飞腾", "level": "4", "parent": "X08.01.01334"}, {"code": "*********.13789-0003", "description": "ERR220506029国内华创高性能台式计算机DH-HDC5100F-飞腾D2000/8-8GB*1-256GB M.2 NVMe SSD*1-AMD550 2G HDMI+VGA-含光驱-板载GE*2-200W电源-预装正版Kylin,含1年免费服务-无显示器", "level": "4", "parent": "X08.01.01334"}, {"code": "*********.10657", "description": "国内大华机架式服务器-DH-RS2270-Phytium S2500*2-32G*4-4T LFF SATA*2，480G R SFF SATA*2-SAS3108 1G，含超级电容-板载GE*2-800W CRPS*2-滑轨，12个LFF硬盘盘位", "level": "4", "parent": "X08.01.01334"}, {"code": "*********.10331", "description": "银河麒麟高级服务器操作系统V10正版授权_含3年免费升级服务_Server-V10-SP2_飞腾", "level": "4", "parent": "X08.01.01334"}, {"code": "*********.37628", "description": "中文大华司法半球防悬挂配件DH-PFA775", "level": "4", "parent": "X08.01.01341"}, {"code": "*********.14723", "description": "三目鹰视SDT8X中文大华400W超星光高清网络球型摄像机", "level": "4", "parent": "X08.01.01342"}, {"code": "*********.13338", "description": "中文大华测温型热成像双目中枪摄像机DH-TPC-BF5441-TB25F12-BM-S2", "level": "4", "parent": "X08.01.01342"}, {"code": "*********.10281", "description": "中文大华50米区域安防雷达DH-PFR4Q-E50", "level": "4", "parent": "X08.01.01343"}, {"code": "*********.40116", "description": "中文大华200万双光人车警戒变焦枪型网络摄像机DH-IPC-HFW5243F1-ZYL-PV-AS-0624-S3", "level": "4", "parent": "X08.01.01344"}, {"code": "*********.38569", "description": "中文大华400万星光智能定焦防暴半球网络摄像机DH-IPC-HDBW5443R-AS-0600B-S3", "level": "4", "parent": "X08.01.01344"}, {"code": "*********.10158", "description": "国密PSAM卡-MW5108(SM_PSAM)", "level": "4", "parent": "X08.01.01349"}, {"code": "*********.12403", "description": "国内大华室内C款铁箱型门禁控制器（四门双向）DH-ASC4204C-D", "level": "4", "parent": "X08.01.01351"}, {"code": "*********.10573", "description": "国内485型报警输出扩展模块DH-ARM708-RS", "level": "4", "parent": "X08.01.01353"}, {"code": "*********.10549", "description": "国内大华三代网络防盗报警控制器DH-ARC2008C-V3", "level": "4", "parent": "X08.01.01353"}, {"code": "*********.10572", "description": "国内大华LCD三代报警编程键盘DH-ARK50C-RC（仅配套-V3系列报警主机）", "level": "4", "parent": "X08.01.01353"}, {"code": "*********.10689", "description": "终端杆绝缘子配套包（六线制）-EH-DGD-J-6-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10052", "description": "电子围栏用承力杆-EH-CG-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10060", "description": "电子围栏用线连接器-EH-XX-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10031", "description": "脉冲电子围栏用合金线（400m）-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10049", "description": "电子围栏用终端杆-EH-DGD-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10061", "description": "电子围栏用警示牌-EH-YSB-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10490", "description": "室外旋转警灯-DH-ARA13-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10693", "description": "中间杆绝缘子配套包（四线制）-EH-PV-J-4-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10690", "description": "承力杆绝缘子配套包（四线制）-EH-CG-J-4-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10055", "description": "电子围栏用中间杆-EH-PV-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10059", "description": "电子围栏用紧线器-EH-SJQ-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10694", "description": "中间杆绝缘子配套包（六线制）-EH-PV-J-6-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10062", "description": "电子围栏用接地桩-EH-JDZ-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10047", "description": "电子围栏用高压避雷器-EH-BLQ-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10688", "description": "终端杆绝缘子配套包（四线制）-EH-DGD-J-4-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10030", "description": "电子围栏用高压绝缘线（100m）-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10695", "description": "底座配套包（中间杆）-EH-DG-D-1-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10691", "description": "承力杆绝缘子配套包（六线制）-EH-CG-J-6-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10692", "description": "底座配套包（终端杆或承力杆）-EH-DG-D-2-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10046", "description": "电子围栏用不锈钢防水箱-EH-FSX-非ROHS", "level": "4", "parent": "X08.01.01356"}, {"code": "*********.10066", "description": "张力围栏用终端受力杆-EH-ZDJ-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10064", "description": "张力围栏用四道双防区控制杆-ARTF-02-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10065", "description": "张力围栏用中间支撑杆-EH-KX-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10069", "description": "张力围栏用张力弹簧-EH-DH-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10068", "description": "张力围栏用张力收紧器-EH-SJQZ-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10067", "description": "张力围栏用转向滑轮-EH-HN-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10701", "description": "张力线束线器-EH-ZLSX-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10700", "description": "受力杆专用底座-EH-SLDZ-2-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10698", "description": "控制杆专用底座-EH-KZDZ-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10029", "description": "多股张力线（500m）-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10699", "description": "支撑杆专用底座-EH-SLDZ-1-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10063", "description": "张力围栏用四道单防区控制杆-ARTF-01-非ROHS", "level": "4", "parent": "X08.01.01358"}, {"code": "*********.10044", "description": "中文大华48芯室外单模光缆（层绞式铝带铠装）-DH-GYTA-48B1.3", "level": "4", "parent": "X08.01.01360"}, {"code": "*********.10227", "description": "嵌入式linux防区型振动光纤主机（16防区）", "level": "4", "parent": "X08.01.01360"}, {"code": "*********.10228", "description": "嵌入式linux防区型振动光纤主机（8防区）", "level": "4", "parent": "X08.01.01360"}, {"code": "*********.10046", "description": "中文大华24芯室外单模光缆（层绞式铝带铠装）-DH-GYTA-24B1.3", "level": "4", "parent": "X08.01.01360"}, {"code": "*********.11373", "description": "国内大华21.45寸V200监视器-DH-LM22-V200A", "level": "4", "parent": "X08.01.01360"}, {"code": "*********.10235", "description": "防区中继盒", "level": "4", "parent": "X08.01.01360"}, {"code": "*********.10018", "description": "中文大华-2芯室外单模光缆-DH-GYXTW-2B1", "level": "4", "parent": "X08.01.01360"}, {"code": "*********.10236", "description": "防区终端盒", "level": "4", "parent": "X08.01.01360"}, {"code": "*********.10280", "description": "MBUS总线八防区输入扩展模块DH-ARM808（主板V1.01）", "level": "4", "parent": "X08.01.01361"}, {"code": "*********.10488", "description": "主动红外对射T型配套支架(T-Type Bracket)-DH-ARA36T-非ROHS", "level": "4", "parent": "X08.01.01362"}, {"code": "*********.10030", "description": "电子警号(Electronic Siren)-DH-ARA16-国内大华", "level": "4", "parent": "X08.01.01362"}, {"code": "*********.10489", "description": "主动红外对射L型配套支架(L-Type Bracket)-DH-ARA36L-非ROHS", "level": "4", "parent": "X08.01.01362"}, {"code": "*********.12368", "description": "中文大华智慧安检综合管理平台DH-DSS-ISC8000D", "level": "4", "parent": "X08.01.01365"}, {"code": "*********.12312", "description": "国内大华安卓15.6寸智能信息交互终端DH-VTA6A41M-SF", "level": "4", "parent": "X08.01.01370"}, {"code": "*********.12317", "description": "国内中性15.6寸/21.5寸交互终端明装支架VTM64", "level": "4", "parent": "X08.01.01370"}, {"code": "*********.16682", "description": "国内大华智能视频监控一体机DH-IVSS708-S1-1M-V2/H", "level": "4", "parent": "X08.01.01373"}, {"code": "*********.10727", "description": "报警模块-非ROHS", "level": "4", "parent": "X08.01.01374"}, {"code": "*********.10726", "description": "声光报警器-非ROHS", "level": "4", "parent": "X08.01.01374"}, {"code": "*********.10724", "description": "光幕感应器-非ROHS", "level": "4", "parent": "X08.01.01374"}, {"code": "*********.10820", "description": "防自缢预警系统软件FZYTC-XS-01", "level": "4", "parent": "X08.01.01374"}, {"code": "*********.10725", "description": "物联网全向探测器-非ROHS", "level": "4", "parent": "X08.01.01374"}, {"code": "*********.10728", "description": "雨雪探测器-非ROHS", "level": "4", "parent": "X08.01.01374"}, {"code": "*********.14872", "description": "中文大华无人机反制器DH-UAV-D-1000JV2", "level": "4", "parent": "X08.01.01386"}, {"code": "*********.11073", "description": "中文大华100万HDCVI同轴高清车载20米D型红外垂直小方块（2.8mm）DH-HAC-HMW3200D-V-0280B-S5-100", "level": "4", "parent": "X08.01.01391"}, {"code": "*********.10063", "description": "国产大华加固型服务器-DH-HS7304A-Core i5-9500-16G*1-256G mSATA SSD*1,2T LFF SATA*1-集成显卡-板载GE*2-350W*1-预装Win10 IOT", "level": "4", "parent": "X08.01.01393"}, {"code": "*********.11646", "description": "公安监管智能巡更模块DH-DSS-P9500-DSJG-Patrol", "level": "4", "parent": "X08.01.01399"}, {"code": "*********.10595", "description": "监室信息交互系统服务平台", "level": "4", "parent": "X08.01.01402"}, {"code": "*********.12637", "description": "国内中性10寸智能防暴信息交互终端暗装沉壳VTM80", "level": "4", "parent": "X08.01.01402"}, {"code": "*********.10597", "description": "公安监管实战监室管教服务平台", "level": "4", "parent": "X08.01.01402"}, {"code": "*********.10598", "description": "公安监管实战监室管教APP", "level": "4", "parent": "X08.01.01402"}, {"code": "*********.10596", "description": "公安监管实战监室交互APP", "level": "4", "parent": "X08.01.01402"}, {"code": "*********.17604-000", "description": "ZA041X-01-沉壳-DH920WF1", "level": "4", "parent": "X08.01.01402"}, {"code": "*********.13355-000", "description": "电源线-(国标3芯,10A250V)-黑色-1200mm-国标", "level": "4", "parent": "X08.01.01402"}, {"code": "*********.12310", "description": "国内大华安卓10.2寸智能防暴信息交互终端DH-VTA5A41M-SF", "level": "4", "parent": "X08.01.01402"}, {"code": "*********.10521", "description": "高清拍摄仪-非ROHS", "level": "4", "parent": "X08.01.01404"}, {"code": "*********.14548", "description": "智能随身物品柜-主柜DH-IFC-GM10-10B-G", "level": "4", "parent": "X08.01.01404"}, {"code": "*********.14546", "description": "智能随身物品柜-副柜DH-IFC-GS14-10B-G", "level": "4", "parent": "X08.01.01404"}, {"code": "*********.10601", "description": "公安监管自助提讯会见APP", "level": "4", "parent": "X08.01.01406"}, {"code": "*********.10600", "description": "公安监所智慧窗口服务管理APP", "level": "4", "parent": "X08.01.01406"}, {"code": "*********.10599", "description": "公安监所智慧窗口服务管理系统", "level": "4", "parent": "X08.01.01406"}, {"code": "*********.10552", "description": "国内大华31.5寸安防显示器DH-LM32-F200", "level": "4", "parent": "X08.01.01412"}, {"code": "*********.10590", "description": "公安监管实战巡控岗电子水牌", "level": "4", "parent": "X08.01.01414"}, {"code": "*********.10592", "description": "公安监管实战伙房电子水牌", "level": "4", "parent": "X08.01.01414"}, {"code": "*********.10594", "description": "公安监管实战管教岗电子水牌", "level": "4", "parent": "X08.01.01414"}, {"code": "*********.10591", "description": "公安监管实战医务室电子水牌", "level": "4", "parent": "X08.01.01414"}, {"code": "*********.10589", "description": "公安监管实战所领导电子水牌", "level": "4", "parent": "X08.01.01414"}, {"code": "*********.10109", "description": "国内大华55寸F400监视器-DH-LM55-F400", "level": "4", "parent": "X08.01.01414"}, {"code": "*********.10593", "description": "公安监管实战综合岗电子水牌", "level": "4", "parent": "X08.01.01414"}, {"code": "*********.10038", "description": "智能床垫-非ROHS", "level": "4", "parent": "X08.01.01431"}, {"code": "*********.10575", "description": "律师身份核验终端-非ROHS", "level": "4", "parent": "X08.01.01432"}, {"code": "*********.10158", "description": "桌面式电源适配器-AC180V~264V-24W-12V2A-V级-DH-PFM301-国标-安防电源v1.0-Φ5.5×Φ2.1×10", "level": "4", "parent": "X08.01.01441"}, {"code": "*********.10124", "description": "国内大华65寸S400监控显示器-DH-LM65-S400", "level": "4", "parent": "X08.01.01443"}, {"code": "*********.14872", "description": "国内大华电子证据采集站DH-EEC300D8-N1（挂机）", "level": "4", "parent": "X08.01.01451"}, {"code": "*********.16069", "description": "国内大华电子证据采集站ZCS-DHTE2-V2（主机）", "level": "4", "parent": "X08.01.01451"}, {"code": "*********.10157", "description": "桌面式电源适配器-AC180V~264V-24W-12V2A-V级-DH-PFM300-安防宽温电源v1.1（中文大华）-Φ5.5×Φ2.1×10", "level": "4", "parent": "X08.01.01470"}, {"code": "*********.15582", "description": "大华高耐用存储卡128GB", "level": "4", "parent": "X08.01.01470"}, {"code": "*********.11335", "description": "自助式办案终端-非ROHS", "level": "4", "parent": "X08.01.01471"}, {"code": "*********.11328", "description": "国内大华10寸单屏身份核验终端DH-ASHZ350A", "level": "4", "parent": "X08.01.01472"}, {"code": "*********.12858", "description": "中文大华单目手持测温仪DH-TPC-HI20", "level": "4", "parent": "X08.01.01478"}, {"code": "*********.13543", "description": "国内大华智能X射线检查系统DH-ISC-M100100D", "level": "4", "parent": "X08.01.01478"}, {"code": "*********.11535", "description": "SD52 中文大华400W网络机芯高速智能球", "level": "4", "parent": "X08.01.01491"}, {"code": "*********.35322", "description": "中文大华200万定焦分离式针孔网络摄像机(圆锥套装,带8米线缆)DH-IPC-HUM7243-RL-BOX1-0280B", "level": "4", "parent": "X08.01.01501"}, {"code": "*********.11036", "description": "国内会议ZW系列OPS电脑（I5十二代/8G/256G固态/激活）-DH-MP-PI5C82IS", "level": "4", "parent": "X08.01.01505"}, {"code": "*********.11935", "description": "国内大华室内LED屏地衣系列DH-PHSIA1.5-SF_国星金线（600*337.5mm压铸铝箱体整屏，1㎡）", "level": "4", "parent": "X08.01.01505"}, {"code": "*********.10035", "description": "国内智能会议平板无线投屏器DH-PKP-WP1B", "level": "4", "parent": "X08.01.01505"}, {"code": "*********.10017", "description": "小间距LED发送盒-DH-LCS-M600-四网口", "level": "4", "parent": "X08.01.01505"}, {"code": "*********.11115", "description": "国内智能会议平板标准款智能笔  DH-PKP-IP0A", "level": "4", "parent": "X08.01.01505"}, {"code": "*********.12181", "description": "国内大华75寸经典款Pro S2智能会议平板DH-LCH75-MC420-C-S2", "level": "4", "parent": "X08.01.01508"}, {"code": "*********.11286", "description": "语音监听系统一体机-非ROHS", "level": "4", "parent": "X08.01.01509"}, {"code": "*********.13138", "description": "司法行业高配服务器DH-DSS-P9100H-HW-ARM-H128", "level": "4", "parent": "X08.01.01509"}, {"code": "*********.10532", "description": "角色分离拾音器-非ROHS", "level": "4", "parent": "X08.01.01509"}, {"code": "*********.10411", "description": "语音识别授权  DH-ITK8000-1-PRO", "level": "4", "parent": "X08.01.01509"}, {"code": "*********.13137", "description": "司法行业高配服务器DH-DSS-P9100H-HW-ARM-H64", "level": "4", "parent": "X08.01.01509"}, {"code": "*********.10410", "description": "云平台引擎 DH-ITK8000", "level": "4", "parent": "X08.01.01509"}, {"code": "*********.10330", "description": "银河麒麟高级服务器操作系统V10正版授权_含1年免费升级服务_Server-V10-SP2_飞腾", "level": "4", "parent": "X08.01.01512"}, {"code": "*********.12946", "description": "司法行业高配服务器DH-DSS-P9100S2H-HW-ARM-H128", "level": "4", "parent": "X08.01.01512"}, {"code": "*********.10689", "description": "国内大华1U监管事件检测智能服务器（E3 1275V5-8G*2-SSD128G-4T*2-E卡*1-8G*2-含加密狗）DH-IVS-IP8000-E-GU1", "level": "4", "parent": "X08.01.01517"}, {"code": "*********.10044", "description": "桌面式电源适配器-AC100V~240V-12W-12V1A-V级-DH-PFM321-带国标AC输入线-Φ5.5×Φ2.1×12-中文大华", "level": "4", "parent": "X08.01.01523"}, {"code": "*********.15198", "description": "智能随身物品柜-主柜DH-IFC-GM14-10B-G", "level": "4", "parent": "X08.01.01530"}, {"code": "*********.15199", "description": "智能随身物品柜-副柜2mDH-IFC-GS18-10B-G", "level": "4", "parent": "X08.01.01530"}, {"code": "*********.10899", "description": "OA定位系统软件-非ROHS", "level": "4", "parent": "X08.01.01535"}, {"code": "*********.10895", "description": "防拆心率腕带-非ROHS", "level": "4", "parent": "X08.01.01535"}, {"code": "*********.10064", "description": "机架式服务器-DH-RS2207CH-G02-Xeon 4314*1-32G*2-2T LFF SAS*2-SAS3408 MR-加配GE*2-900W*2-滑轨-#项目专用#", "level": "4", "parent": "X08.01.01535"}, {"code": "*********.10897", "description": "腕带拆卸工具-非ROHS", "level": "4", "parent": "X08.01.01535"}, {"code": "*********.10896", "description": "腕带便携充电器-非ROHS", "level": "4", "parent": "X08.01.01535"}, {"code": "*********.10894", "description": "AOA室内定位基站-非ROHS", "level": "4", "parent": "X08.01.01535"}, {"code": "*********.10517", "description": "国内大华视频云存储-数据存储节点DH-CSS7336S-VRE", "level": "4", "parent": "X08.01.01551"}, {"code": "*********.10528", "description": "视频云存储-元数据服务器硬件DH-CSS9100IA-LVE2-HW", "level": "4", "parent": "X08.01.01551"}, {"code": "*********.10285", "description": "机械硬盘-ST6000NM019B-6TB-256MB-7200RPM-3.5英寸-3.5-英寸-SATA接口-SATA接口-标准盘（2TG103-003）", "level": "4", "parent": "X08.01.01575"}, {"code": "*********.11064", "description": "中文大华二层管理型全千兆以太网交换机DH-S4200-24GT4GF ，24个10/100/1000Mbps 电口，4个1000Mbps光口", "level": "4", "parent": "X08.01.01580"}, {"code": "*********.10488", "description": "网络视频存储服务器 国内大华 DH-EVS5024S-R-V2", "level": "4", "parent": "X08.01.01589"}, {"code": "*********.15134", "description": "国内大华执法记录仪DH-DSJ-N1-16G/GA", "level": "4", "parent": "X08.01.01590"}, {"code": "*********.10854", "description": "中文大华200万红外定焦防暴半球网络摄像机DH-IPC-HDBW3233F-M-DAE-AS-0280B", "level": "4", "parent": "X08.01.01591"}, {"code": "*********.10709", "description": "车载换代防护机箱组件MA-PB01 ", "level": "4", "parent": "X08.01.01591"}, {"code": "*********.26108", "description": "中文大华200万红外定焦车载网络摄像机DH-IPC-HMW7230-0280B", "level": "4", "parent": "X08.01.01591"}, {"code": "*********.10018", "description": "希捷2.5寸2T 企业级SATA硬盘-ST2000NX0253", "level": "4", "parent": "X08.01.01591"}, {"code": "*********.16307", "description": "国内大华1080P智能LED控制器", "level": "4", "parent": "X08.01.01593"}, {"code": "*********.11102-D001", "description": "国内大华600*337.5全前维护小间距LED灯珠备品附件包006_DH-PHSAB1.2/1.5（D12C_C-2）_20210912GXT-AZ", "level": "4", "parent": "X08.01.01593"}, {"code": "*********.15207", "description": "中文大华无人机（2021）DH-UAV-X1550V3", "level": "4", "parent": "X08.01.01609"}, {"code": "*********.11101-D001", "description": "国内大华600*337.5全前维护小间距LED灯板006_DH-PHSIA1.5-SF-C（D12C_C-2）_20210912GXT-AZ", "level": "4", "parent": "X08.01.01614"}, {"code": "*********.15125", "description": "国内大华超清分布式坐席管理系统输出节点DH-DSMS0105UHO-4K", "level": "4", "parent": "X08.01.01616"}, {"code": "*********.15973", "description": "国内大华iSee智慧大屏管家软件（大屏控制模块）DH-iSee-CVCS", "level": "4", "parent": "X08.01.01616"}, {"code": "*********.14768", "description": "国内大华分布式坐席机架（旗舰款-4U_竖8_配套NEB0105HI）DH-DSMS-FRA", "level": "4", "parent": "X08.01.01616"}, {"code": "*********.15115", "description": "国内大华分布式坐席管理系统拼接节点DH-DSMS0205UHP-4K", "level": "4", "parent": "X08.01.01616"}, {"code": "*********.16106", "description": "DC12V24A 8分路机架式电源", "level": "4", "parent": "X08.01.01616"}, {"code": "*********.15149", "description": "国内大华分布式坐席机架（6U_竖8_配套DSMS0205UHP）DH-DSMS-FRA-E", "level": "4", "parent": "X08.01.01616"}, {"code": "*********.15893", "description": "国内大华分布式坐席管理系统KVM输入节点DH-DSMS0115EPI-4K", "level": "4", "parent": "X08.01.01616"}, {"code": "*********.10204", "description": "分布式坐席管理系统DH-KVM-SOFT", "level": "4", "parent": "X08.01.01616"}, {"code": "*********.16487", "description": "国内大华视频综合平台主机DH-M80（配置S24）", "level": "4", "parent": "X08.01.01617"}, {"code": "*********.14746", "description": "SD6C3项目型中文大华25倍400W网络智能球机", "level": "4", "parent": "X08.01.01621"}, {"code": "*********.13891", "description": "灵犀PRO SDT6X中文大华高清网络摄像机25X(4mm)", "level": "4", "parent": "X08.01.01621"}, {"code": "*********.0934", "description": "DH-PFB300C-吊装支架-DH816W2-中文大华", "level": "4", "parent": "X08.01.01621"}, {"code": "*********.37892", "description": "中文大华双目客流-400万红外定焦海螺网络摄像机DH-IPC-HDW7443X-E2-0200B", "level": "4", "parent": "X08.01.01621"}, {"code": "*********.42243", "description": "中文大华mini天镜-400万融智能双光变焦枪型网络摄像机DH-IPC-HFW8449F1-ZRL-IL4-0832-DC12AC24V-S3", "level": "4", "parent": "X08.01.01622"}, {"code": "*********.40121", "description": "中文大华400万双光人车警戒变焦枪型网络摄像机DH-IPC-HFW5443F1-ZYL-PV-AS-27135-S3", "level": "4", "parent": "X08.01.01622"}, {"code": "*********.40310", "description": "中文大华200万双光人车警戒定焦枪型网络摄像机DH-IPC-HFW5243M1-YL-PV-SA-0360B-S3", "level": "4", "parent": "X08.01.01622"}, {"code": "*********.42383", "description": "中文大华500万红外定焦防暴半球网络摄像机DH-IPC-HDBW8543R1-0185B-QH", "level": "4", "parent": "X08.01.01623"}, {"code": "*********.40123", "description": "中文大华400万双光人车警戒变焦枪型网络摄像机DH-IPC-HFW5443F1-ZYL-PV-AS-0624-S3", "level": "4", "parent": "X08.01.01624"}, {"code": "*********.10240-001", "description": "PFB710WA-SG-高防腐不锈钢壁装支架-DH218WG1-中文中性", "level": "4", "parent": "X08.01.01625"}, {"code": "*********.40616", "description": "中文大华经典哈勃-1600万360°哈勃守望者网络摄像机DH-PSDW81649M-A360-D440-HL-AR-DC36V-ATC-S3", "level": "4", "parent": "X08.01.01625"}, {"code": "*********.15710", "description": "国内大华神算智能视频监控一体机DH-IVSS716-S1-4M-V2/L", "level": "4", "parent": "X08.01.01631"}, {"code": "*********.42641", "description": "光网口-中文大华200万易智能红外变焦半球型光网口网络摄像机DH-IPC-HDBW5243DE-ZYH-NF-DMSX-27135-DC12AC24V-S2", "level": "4", "parent": "X08.01.01631"}, {"code": "*********.11618", "description": "国内大华桥式直角摆闸（右闸，单开简易孔，室外）DH-ASGB511Y-R", "level": "4", "parent": "X08.01.01634"}, {"code": "*********.11620", "description": "国内大华桥式直角摆闸（中闸，双开简易孔，室外）DH-ASGB522Y-D", "level": "4", "parent": "X08.01.01634"}, {"code": "*********.11535", "description": "国内大华外置二维码模块ASF-QR-MODULE", "level": "4", "parent": "X08.01.01634"}, {"code": "*********.11621", "description": "国内大华桥式直角摆闸（左闸，单开简易孔，室外）DH-ASGB511Y-L", "level": "4", "parent": "X08.01.01634"}, {"code": "*********.11383", "description": "国内大华人脸识别通用闸机支架_ DH-ASF053-ZJ", "level": "4", "parent": "X08.01.01634"}, {"code": "*********.10340", "description": "国内大华Y款摆闸门翼（不锈钢）-700mm  ASF-B700S", "level": "4", "parent": "X08.01.01634"}, {"code": "*********.11605", "description": "国内大华闸机转换板DH-ASF056-V2", "level": "4", "parent": "X08.01.01634"}, {"code": "*********.10726", "description": "中文中性行业变频道闸配杆（加强筋直臂，不分左右4米）-非ROHS", "level": "4", "parent": "X08.01.01635"}, {"code": "*********.14503", "description": "中文大华睿界400万双光变焦智能出入口杆式抓拍一体机DH-IPMECS-2234-Z", "level": "4", "parent": "X08.01.01635"}, {"code": "*********.15441", "description": "中文大华行业行云C8系列800万一体化右向圆杆无簧道闸（LCD屏）DH-IPMECD-3147-RV8-LCD", "level": "4", "parent": "X08.01.01635"}, {"code": "*********.10034", "description": "中文大华高端右向直臂道闸 4米 （1.4秒）DH-IPMECD-3012-RM40-T14", "level": "4", "parent": "X08.01.01635"}, {"code": "*********.10733", "description": "中文中性停车场行业出入口二代自助终端（无扫码读头）-非ROHS", "level": "4", "parent": "X08.01.01635"}, {"code": "*********.14645", "description": "国内大华智能X射线检查系统DH-ISC-M6550D", "level": "4", "parent": "X08.01.01637"}, {"code": "*********.14210", "description": "国内中性双显示器操作台ISC-2DCC", "level": "4", "parent": "X08.01.01637"}, {"code": "*********.14725", "description": "国内大华信息发布分类门主机DH-ISC-D818TR", "level": "4", "parent": "X08.01.01639"}, {"code": "*********.10025", "description": "防爆毯-非ROHS", "level": "4", "parent": "X08.01.01640"}, {"code": "*********.10016", "description": "防爆罐-非ROHS", "level": "4", "parent": "X08.01.01640"}, {"code": "*********.10026", "description": "台式液体检测仪-非ROHS", "level": "4", "parent": "X08.01.01642"}, {"code": "*********.10171", "description": "中文大华全自动液压一体升降柱（6mm）-非ROHS", "level": "4", "parent": "X08.01.01644"}, {"code": "*********.10361", "description": "中文大华液压升降柱控制柜（一拖八）-非ROHS", "level": "4", "parent": "X08.01.01644"}, {"code": "*********.10005", "description": "开关电源-24V/50W-非ROHS", "level": "4", "parent": "X08.01.01649"}, {"code": "*********.10040", "description": "发卡系统-DH-ECS-01B-非ROHS", "level": "4", "parent": "X08.01.01649"}, {"code": "*********.10036", "description": "轿内分层型控制器（不含单独刷卡读头）-DH-ECC-16F-非ROHS", "level": "4", "parent": "X08.01.01649"}, {"code": "*********.10012", "description": "箱体-梯控控制器箱体-非ROHS", "level": "4", "parent": "X08.01.01649"}, {"code": "*********.10038", "description": "刷卡读卡头-DH-ECR-01A-非ROHS", "level": "4", "parent": "X08.01.01649"}, {"code": "*********.11859", "description": "国内大华7寸S款室内IC卡款智能门禁一体机（WiFi）DH-ASI7213S-W", "level": "4", "parent": "X08.01.01650"}, {"code": "*********.10158", "description": "大华人脸梯控转接模块-非ROHS", "level": "4", "parent": "X08.01.01650"}, {"code": "*********.11727", "description": "国内大华7寸智能门禁一体机IC款(室内、WiFi、测温)DH-ASI7213K-WT", "level": "4", "parent": "X08.01.01652"}, {"code": "*********.12406", "description": "国内大华室内B款便携型门禁控制器（四门单向）DH-ASC4204B-S", "level": "4", "parent": "X08.01.01653"}, {"code": "*********.12404", "description": "国内大华室内B款便携型门禁控制器（单门双向）DH-ASC4201B-D", "level": "4", "parent": "X08.01.01653"}, {"code": "*********.12307", "description": "国内大华10寸安卓双屏身份核验终端DH-ASHZ530A-W", "level": "4", "parent": "X08.01.01655"}, {"code": "*********.11708", "description": "数智孪生可视化应用DH-DSS-P9500-DSTY-3DBaseVideo", "level": "4", "parent": "X08.01.01661"}, {"code": "*********.12785", "description": "司法行业小型服务器", "level": "4", "parent": "X08.01.01667"}, {"code": "*********.12787", "description": "司法行业高配服务器", "level": "4", "parent": "X08.01.01667"}, {"code": "*********.10769", "description": "银河麒麟桌面操作系统V10正版授权_含1年免费升级服务_Desktop-V10_飞腾_Kylin-Desktop-V10-Release-Build1-2101-arm64", "level": "4", "parent": "X08.01.01670"}, {"code": "*********.10770", "description": "银河麒麟桌面操作系统V10正版授权_含3年免费升级服务_Desktop-V10_飞腾_Kylin-Desktop-V10-Release-Build1-2101-arm64", "level": "4", "parent": "X08.01.01670"}, {"code": "*********.11352", "description": "国内大华4.3寸单屏台式指纹人证终端DH-ASHZ230A-W", "level": "4", "parent": "X08.01.01672"}, {"code": "*********.14979", "description": "国内大华4路超高清解码器DH-NVD0405DU-2I-8K", "level": "4", "parent": "X08.01.01691"}, {"code": "*********.10120", "description": "国内大华31.5寸V200监视器-DH-LM32-V200", "level": "4", "parent": "X08.01.01691"}, {"code": "*********.15177", "description": "国内大华网络硬盘录像机DH-NVR5432-4KS3/I(主板V1.00)", "level": "4", "parent": "X08.01.01693"}, {"code": "*********.10227", "description": "国内大华会议电视终端DH-VCS-TS4200-V5", "level": "4", "parent": "X08.01.01693"}, {"code": "*********.13022", "description": "智能运维管理平台服务器DH-NMS-B9100S2H-HW-ARM-U32", "level": "4", "parent": "X08.01.01727"}, {"code": "*********.10915", "description": "国内大华2U监管事件检测智能服务器DH-IVS-IP9000-H-S1-DC2", "level": "4", "parent": "X08.01.01728"}, {"code": "*********.10793", "description": "视频云存储（标准版）-数据存储节点DH-CSS8236AC-HVE-8T", "level": "4", "parent": "X08.01.01729"}, {"code": "*********.10593", "description": "视频云存储-元数据服务器硬件DH-CSS9100AC-HVE-HW", "level": "4", "parent": "X08.01.01729"}, {"code": "*********.10543", "description": "鞋底金属探测器-非ROHS", "level": "4", "parent": "X08.01.01742"}, {"code": "*********.10136", "description": "国内大华智能X射线检查系统DH-ISC-M5030", "level": "4", "parent": "X08.01.01742"}, {"code": "*********.46579", "description": "中文大华司法专用半球-400万融智能红外变焦防暴半球网络摄像机DH-IPC-HDBW8443R1-ZAST-27135-JWZY", "level": "4", "parent": "X08.01.01747"}, {"code": "*********.12638", "description": "国内中性15.6寸智能防暴信息交互终端暗装沉壳VTM81", "level": "4", "parent": "X08.01.01750"}, {"code": "*********.12581", "description": "国内大华安卓15.6寸智能防暴信息交互终端DH-VTA6A41M-SFK", "level": "4", "parent": "X08.01.01750"}, {"code": "*********.15076", "description": "国内大华神算智能视频监控一体机DH-IVSS716DR-S1-8M-V2", "level": "4", "parent": "X08.01.01770"}, {"code": "*********.10283", "description": "银河麒麟桌面操作系统V10正版授权_含1年免费升级服务_Desktop-V10-SP1_飞腾", "level": "4", "parent": "X08.01.01777"}, {"code": "*********.10339", "description": "银河麒麟高级服务器操作系统V10正版授权_含3年免费升级服务_Server-V10-SP2_ARM（鲲鹏/飞腾）", "level": "4", "parent": "X08.01.01777"}, {"code": "*********.13040", "description": "NTP校时服务器(单北斗）", "level": "4", "parent": "X08.01.01778"}, {"code": "*********.10039", "description": "音频主机配套专用拾音器-HS-31S", "level": "4", "parent": "X08.01.01784"}, {"code": "*********.10397", "description": "国内大华音频处理器DH-RBAP3001", "level": "4", "parent": "X08.01.01784"}, {"code": "*********.10396", "description": "国内大华桌面嵌入式拾音器DH-HSP200P", "level": "4", "parent": "X08.01.01784"}, {"code": "*********.15025", "description": "国内大华1路超高清编码器DH-NEB0105HI-4K", "level": "4", "parent": "X08.01.01794"}, {"code": "*********.11852", "description": "国内大华75寸经典款S2智能会议平板DH-LCH75-MC420-C", "level": "4", "parent": "X08.01.01794"}, {"code": "*********.10546", "description": "国内大华43寸S200安防显示器DH-LM43-S200", "level": "4", "parent": "X08.01.01803"}, {"code": "*********.45575", "description": "中文大华mini天镜-400万融智能红外变焦防暴半球网络摄像机DH-IPC-HDBW8443R1-ZRL-27135-S4", "level": "4", "parent": "X08.01.01810"}, {"code": "*********.36322", "description": "中文大华200万定焦分离式针孔网络摄像机(方平86盒套装)DH-IPC-HUM7243-FR-BOX4-0280B-YB", "level": "4", "parent": "X08.01.01816"}, {"code": "*********.40608", "description": "中文大华200万变焦防暴半球网络摄像机DH-IPC-HDB5243R-ZAS-27135", "level": "4", "parent": "X08.01.01827"}, {"code": "*********.10373", "description": "网络视频存储服务器 国内大华 DH-EVS5124S-H-10G", "level": "4", "parent": "X08.01.01845"}, {"code": "*********.40609", "description": "中文大华200万变焦防暴半球网络摄像机DH-IPC-HDB5243R-ZAS-X-27135", "level": "4", "parent": "X08.01.01845"}, {"code": "*********.10057", "description": "国内大华43寸F200监视器-DH-LM43-F200", "level": "4", "parent": "X08.01.01852"}, {"code": "*********.10520", "description": "自助一体机-非ROHS", "level": "4", "parent": "X08.01.01864"}, {"code": "*********.10527", "description": "慧脑智能语音识别系统DH-ITK8000-S", "level": "4", "parent": "X08.01.01904"}, {"code": "*********.14920", "description": "国内大华智能视频监控一体机DH-IVSS708-S2-1I-V2", "level": "4", "parent": "X08.01.01913"}, {"code": "*********.12582", "description": "国内大华安卓15.6寸智能防暴信息交互终端（测温款）DH-VTA6A41M-SFKT", "level": "4", "parent": "X08.01.01920"}, {"code": "*********.11381", "description": "数字监控管理平台_信息发布管理系统_行业（业务组件）", "level": "4", "parent": "X08.01.01924"}, {"code": "*********.11382", "description": "数字监控管理平台_信息发布管理系统_信息屏路数授权_行业（规模数量）", "level": "4", "parent": "X08.01.01924"}, {"code": "*********.38158", "description": "中文大华800万录播融智能红外变焦半球网络摄像机DH-IPC-HDBW8841H-ZRL-LB-2712-DC12AC24V-S2", "level": "4", "parent": "X08.01.01925"}, {"code": "*********.15165", "description": "国内大华分布式显控安卓播放盒DH-DS04-AI400", "level": "4", "parent": "X08.01.01926"}, {"code": "*********.10414", "description": "国内大华55寸室内壁挂智能云显示终端(C2) DH-LDH55-FAI200M", "level": "4", "parent": "X08.01.01926"}, {"code": "*********.10412", "description": "国内大华43寸室内壁挂智能云显示终端(C2) DH-LDH43-FAI200M", "level": "4", "parent": "X08.01.01926"}, {"code": "*********.10213", "description": "网络视频存储服务器 国内大华 DH-EVS5248S", "level": "4", "parent": "X08.01.01931"}, {"code": "*********.10085", "description": "无人机双目热成像云台(PZ)DH-UAV-GA-TV-4640U-F13-非ROHS", "level": "4", "parent": "X08.01.01933"}, {"code": "*********.10084", "description": "无人机8K云台(PZ)DH-UAV-GA-V-4800-非ROHS", "level": "4", "parent": "X08.01.01933"}, {"code": "*********.11228", "description": "中文大华6槽位框式核心交换机DH-S10506", "level": "4", "parent": "X08.01.01935"}, {"code": "*********.10102", "description": "中文大华三层管理型万兆以太网交换机DH-S6600-48XF2QF-A，48个1/10Gbps光口，2个40Gbps光口", "level": "4", "parent": "X08.01.01936"}, {"code": "*********.11882", "description": "中文大华三层管理型万兆以太网交换机DH-S6600-24XF2QF-A，24个1/10Gbps光口，2个40Gbps光口", "level": "4", "parent": "X08.01.01936"}, {"code": "*********.11597", "description": "中文大华二层管理型全千兆PoE交换机DH-S4200-24GT4GF-375（375W），24个10/100/1000Mbps PoE电口，4个1Gbps光口", "level": "4", "parent": "X08.01.01937"}, {"code": "*********.10745", "description": "中文大华二层非管理千兆准工业级以太网交换机DH-IS3200-9GT2GF，9个10/100/1000Mbps 电口，2个1000Mbps光口", "level": "4", "parent": "X08.01.01938"}, {"code": "*********.11053", "description": "中文大华二层管理型全千兆工业级以太网交换机DH-IS5500-6GT4GF，6个10/100/1000Mbps 电口，4个1000Mbps 光口", "level": "4", "parent": "X08.01.01938"}, {"code": "*********.10248", "description": "深信服 下一代防火墙AF-1000-FH1800A-非ROHS", "level": "4", "parent": "X08.01.01941"}, {"code": "*********.10821", "description": "深信服EDR-终端安全管理系统软件V3.0（产品控制中心）（永久）", "level": "4", "parent": "X08.01.01942"}, {"code": "*********.10822", "description": "深信服EDR-终端安全-端点安全软件V3.0（PC基础版）（含1个PC端点授权）", "level": "4", "parent": "X08.01.01942"}, {"code": "*********.10828", "description": "深信服EDR-终端安全-端点安全软件V3.0（服务器探针版）（含1个服务器端点授权）", "level": "4", "parent": "X08.01.01942"}, {"code": "*********.10254", "description": "绿盟日志审计系统LASNX3-DH110-非ROHS", "level": "4", "parent": "X08.01.01943"}, {"code": "*********.10270", "description": "绿盟运维审计系统OSMSNX3-DH110-非ROHS", "level": "4", "parent": "X08.01.01945"}, {"code": "*********.10262", "description": "绿盟漏洞扫描系统RSASNX3-DH110-非ROHS", "level": "4", "parent": "X08.01.01947"}, {"code": "*********.10259", "description": "绿盟WEB应用防护系统WAFNX3-DH110-非ROHS", "level": "4", "parent": "X08.01.01949"}, {"code": "*********.10082", "description": "中文大华三层管理型千兆以太网交换机DH-S5600-24GF4XF-A，24个10/100/1000Mbps光口，4个1/10Gbps光口", "level": "4", "parent": "X08.01.01968"}, {"code": "*********.11879", "description": "中文大华三层管理型全千兆以太网交换机DH-S5600-24GT4XF-A，16个10/100/1000Mbps电口，8个10/100/1000Mbps电口（combo口），8个100M/1000Mbps光口（combo口），4个1/10Gbps光口", "level": "4", "parent": "X08.01.01968"}, {"code": "*********.11987", "description": "司法视频云APP终端DH-DSS-P9100-Base-APP", "level": "4", "parent": "X08.01.01989"}, {"code": "*********.12463", "description": "司法行业高配服务器DH-DSS-P9100F-H-HW", "level": "4", "parent": "X08.01.02019"}, {"code": "*********.10898", "description": "腕带座充-非ROHS", "level": "4", "parent": "X08.01.02044"}, {"code": "*********.11087", "description": "国内大华音频事件检测智能网关授权（1路分析）DH-ASD8000-1C-PRO", "level": "4", "parent": "X08.01.02048"}, {"code": "*********.10571", "description": "国内大华2U音频事件检测智能网关DH-ASD8000-1E-DC2", "level": "4", "parent": "X08.01.02048"}, {"code": "*********.10004", "description": "国内大华音质检测算法模型（含U盘）DH-ASD8000-MD-JSQE-PRO", "level": "4", "parent": "X08.01.02048"}, {"code": "*********.10896", "description": "全解析智能服务器", "level": "4", "parent": "X08.01.02050"}, {"code": "*********.10896", "description": "国内大华智眸情绪识别软件授权DH-IVS-AER3000-1C-PRO", "level": "4", "parent": "X08.01.02050"}, {"code": "*********.12945", "description": "司法行业高配服务器DH-DSS-P9100S2H-HW-ARM-H64", "level": "4", "parent": "X08.01.02051"}, {"code": "*********.16031", "description": "国内大华1.5U解码器DH-NVD1205DU-2I-8K", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.13043", "description": "国内大华投屏软件（多窗口-实物交付）DH-RTS-View", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.10136", "description": "国内大华扩展款多媒体服务器一体机（i3-1115G4-8G-M_256G-DP-HDMI）DH-MPC5300-IG-2OP", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.16072", "description": "国内大华4K高清二合一控制器", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.16486", "description": "国内大华6口MLED控制器", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.11872", "description": "国内大华46寸LCD标亮一体式整机F3.5（CT63P）", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.15014", "description": "国内大华LED控制器DH-LCS-V1000-4U", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.12267", "description": "国内大华55寸LCD标亮一体式整机F1.7（CT63P）DH-LS550UDM-YEF-V5", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.12266", "description": "国内大华46寸LCD标亮一体式整机F1.7（CT63P）DH-LS460UDM-YEF-V4", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.11873", "description": "国内大华55寸LCD标亮一体式整机F3.5（CT63P）", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.16523", "description": "国内大华20口智能LED控制器", "level": "4", "parent": "X08.01.02054"}, {"code": "*********.12182", "description": "国内大华86寸经典款Pro S2智能会议平板DH-LCH86-MC420-C-S2", "level": "4", "parent": "X08.01.02055"}, {"code": "*********.12180", "description": "国内大华65寸经典款Pro S2智能会议平板DH-LCH65-MC420-C-S2", "level": "4", "parent": "X08.01.02055"}, {"code": "*********.10039", "description": "国内大华会议配件系列86/98寸移动支架 DH-PKC-MS1A", "level": "4", "parent": "X08.01.02057"}, {"code": "*********.15015", "description": "国内大华LED控制器DH-LCS-V1000-7U", "level": "4", "parent": "X08.01.02060"}, {"code": "*********.14639", "description": "国内大华LED拼接处理器DH-LCS-V1000", "level": "4", "parent": "X08.01.02060"}, {"code": "*********.12157", "description": "55寸LCD国内大华标亮4K一体式整机F1.7（一包一）DH-LS550KDM-YEF-V2", "level": "4", "parent": "X08.01.02062"}, {"code": "*********.44804", "description": "中文大华空间定位-400万双光人定焦空间定位防暴半球网络摄像机DH-IPC-HDBW5443R1-YL-PV-AS-0360B-S3-KJDW", "level": "4", "parent": "X08.01.02070"}, {"code": "*********.44805", "description": "中文大华空间定位-400万双光人定焦空间定位防暴半球网络摄像机DH-IPC-HDBW5443R1-YL-PV-AS-0600B-S3-KJDW", "level": "4", "parent": "X08.01.02070"}, {"code": "*********.11150", "description": "人员空间定位地图服务DH-IVS-IP9001-BDPZ", "level": "4", "parent": "X08.01.02070"}, {"code": "*********.44806", "description": "中文大华空间定位-400万双光人定焦空间定位防暴半球网络摄像机DH-IPC-HDBW5443R1-YL-PV-AS-0800B-S3-KJDW", "level": "4", "parent": "X08.01.02070"}, {"code": "*********.10916", "description": "国内大华2U监管事件检测智能服务器DH-IVS-IP9000-2H-S1-DC2", "level": "4", "parent": "X08.02.00081"}, {"code": "*********.10920", "description": "国内大华2U监管事件检测智能服务器DH-IVS-IP9000-6H-DC2", "level": "4", "parent": "X08.02.00081"}, {"code": "*********.10917", "description": "国内大华2U监管事件检测智能服务器DH-IVS-IP9000-3H-S1-DC2", "level": "4", "parent": "X08.02.00081"}, {"code": "*********.10918", "description": "国内大华2U监管事件检测智能服务器DH-IVS-IP9000-4H-DC2", "level": "4", "parent": "X08.02.00081"}, {"code": "*********.11836", "description": "国内大华4.3寸A款室内联网智能门禁一体机（IC卡、WIFI）DH-ASI6213A-W", "level": "4", "parent": "X08.02.00087"}, {"code": "*********.10607", "description": "大华报警门禁接入网关", "level": "4", "parent": "X08.02.00090"}, {"code": "*********.11660", "description": "国内大华方形M款金属防暴读卡器 (室内，IC，带机械按键) DH-ASR1101M-V1", "level": "4", "parent": "X08.02.00091"}, {"code": "*********.40107", "description": "中文大华月台相机-400万物流智慧月台双光警戒变焦枪型网络摄像机DH-IPC-HFW5443F1-ZAST-0624-YTXJ", "level": "4", "parent": "X08.02.00105"}, {"code": "*********.40106", "description": "中文大华月台相机-400万物流智慧月台双光警戒变焦枪型网络摄像机DH-IPC-HFW5443F1-ZAST-27135-YTXJ", "level": "4", "parent": "X08.02.00105"}, {"code": "*********.38558", "description": "中文大华400万星光智能定焦防暴半球网络摄像机DH-IPC-HDBW5443R-SA-0280B-S3", "level": "4", "parent": "X08.02.00106"}, {"code": "*********.38559", "description": "中文大华400万星光智能定焦防暴半球网络摄像机DH-IPC-HDBW5443R-SA-0360B-S3", "level": "4", "parent": "X08.02.00106"}, {"code": "*********.37519", "description": "中文大华300万红外定焦防暴半球网络摄像机DH-IPC-HDBW8343R1-0200B", "level": "4", "parent": "X08.02.00110"}, {"code": "*********.37517", "description": "中文大华500万红外定焦防暴半球网络摄像机DH-IPC-HDBW8543R1-0200B", "level": "4", "parent": "X08.02.00110"}, {"code": "*********.10474", "description": "国内大华光学指纹采集器DH-ASM202", "level": "4", "parent": "X08.02.00112"}, {"code": "*********.15969", "description": "国内大华审讯专用硬盘录像机(BD版) DH-HVR0405FD-S-H", "level": "4", "parent": "X08.02.00125"}, {"code": "*********.10062", "description": "国内大华会议电视系统多点控制单元(MCU)DH-VCS-MCU9110", "level": "4", "parent": "X08.02.00126"}, {"code": "*********.13097", "description": "国内大华高清1080P会议电视摄像机DH-VCS-C700", "level": "4", "parent": "X08.02.00139"}, {"code": "*********.17791-000", "description": "ZA042X-00-国内明装壳-DH920WF1", "level": "4", "parent": "X08.02.00161"}, {"code": "*********.10779", "description": "国内大华65寸4KLCD标亮一体式整机F3.5", "level": "4", "parent": "X08.02.00162"}, {"code": "*********.12150", "description": "国内大华46寸LCD标亮一体式整机F0.88（CT63P）DH-LS460UEM-YEF-V1", "level": "4", "parent": "X08.02.00162"}, {"code": "*********.12151", "description": "国内大华55寸LCD标亮一体式整机F0.88（CT63P）DH-LS550UEM-YEF-V2", "level": "4", "parent": "X08.02.00162"}, {"code": "*********.10830", "description": "国内大华人员空间定位智能服务器DH-IVS-IP9001-2H-DC2", "level": "4", "parent": "X08.02.00166"}, {"code": "*********.10831", "description": "国内大华人员空间定位智能服务器DH-IVS-IP9001-3H-DC2", "level": "4", "parent": "X08.02.00166"}, {"code": "*********.10829", "description": "国内大华人员空间定位智能服务器DH-IVS-IP9001-H-DC2", "level": "4", "parent": "X08.02.00166"}, {"code": "*********.10828", "description": "国内大华人员空间定位智能服务器DH-IVS-IP9001-4H-S2-DC2", "level": "4", "parent": "X08.02.00166"}, {"code": "*********.10250", "description": "温湿度显示屏-非ROHS", "level": "4", "parent": "X08.02.00169"}]};
        let productLifecycleData = {"*********.12306": "量产", "*********.10144": "量产", "*********.38910": "量产", "*********.38911": "量产", "*********.10296": "量产", "*********.10312": "量产", "*********.12128": "量产", "*********.10105": "量产", "*********.12467": "量产", "*********.11212": "即将停售", "*********.38912": "量产", "*********.11821": "量产", "*********.38909": "量产", "*********.10179": "量产", "*********.43211": "量产", "*********.42324": "量产", "*********.10173": "量产", "*********.43304": "量产", "*********.11922": "量产", "*********.10325": "量产", "*********.14516": "量产", "*********.12943": "量产", "*********.42325": "量产", "*********.10363-001": "开发", "*********.11581": "量产", "*********.39513": "量产", "*********.11653": "量产", "*********.11640": "量产", "*********.11644": "量产", "*********.11641": "量产", "*********.11643": "量产", "*********.11639": "量产", "*********.11655": "量产", "*********.11648": "量产", "*********.12642": "量产", "*********.10791": "量产", "*********.10205": "量产", "*********.12556": "量产", "*********.11247": "量产", "*********.11248": "量产", "*********.10294": "量产", "*********.10390": "量产", "*********.10389": "量产", "*********.10298": "量产", "*********.10120": "即将停售", "*********.11544": "量产", "*********.11524": "量产", "*********.11078": "量产", "*********.11072": "量产", "*********.12263-000": "量产", "*********.11126": "量产", "*********.12261-000": "量产", "*********.10627": "量产", "*********.2303": "量产", "*********.12262-000": "量产", "*********.10429": "量产", "*********.20609": "量产", "*********.11081": "量产", "*********.10548": "量产", "*********.15093": "即将停售", "*********.15138": "量产", "*********.12599": "量产", "*********.11651": "量产", "*********.10297": "量产", "*********.11652": "量产", "*********.11522": "量产", "*********.11724": "量产", "*********.10097": "量产", "*********.10297": "量产", "*********.10061": "量产", "*********.38561": "量产", "*********.10045": "量产", "*********.10019": "量产", "*********.10079": "量产", "*********.10282": "即将停售", "*********.10252": "即将停售", "*********.12253": "量产", "*********.12255": "量产", "*********.10015": "量产", "*********.10203": "量产", "*********.38567": "量产", "*********.10510": "量产", "*********.13463": "量产", "*********.11509": "量产", "*********.10222": "量产", "*********.11383": "即将停售", "*********.10038": "量产", "*********.15143": "量产", "*********.11187": "量产", "*********.13514": "量产", "*********.10031": "量产", "*********.33464": "量产", "*********.10529": "量产", "*********.15185": "量产", "*********.12036": "量产", "*********.10239": "量产", "*********.15929": "量产", "*********.12308": "量产", "*********.16428": "量产", "*********.10054": "量产", "*********.10088": "量产", "*********.10544": "量产", "*********.10571": "量产", "*********.10096": "开发", "*********.10031": "开发", "*********.10480": "量产", "*********.10181": "量产", "*********.10175": "量产", "*********.10080": "量产", "*********.10073": "量产", "*********.10286": "量产", "*********.10348": "即将停售", "*********.15766": "即将停售", "*********.15767": "量产", "*********.10774": "量产", "*********.11292": "量产", "*********.11253": "量产", "*********.11983": "量产", "*********.10345": "量产", "*********.11291": "量产", "*********.11286": "原型机", "*********.11281": "量产", "*********.11254": "量产", "*********.12008": "量产", "*********.11260": "量产", "*********.12641": "量产", "*********.13450": "量产", "*********.11243": "量产", "*********.10512": "量产", "*********.10210": "量产", "*********.12362": "量产", "*********.10211": "量产", "*********.11576": "量产", "*********.12643": "量产", "*********.11465": "即将停售", "*********.11415": "量产", "*********.10718": "量产", "*********.11831": "量产", "*********.10017": "量产", "*********.10019": "量产", "*********.36694": "量产", "*********.39733": "量产", "*********.10249": "量产", "*********.13042": "量产", "*********.14271": "量产", "*********.16101": "量产", "*********.14523": "量产", "*********.43556": "量产", "*********.39222": "量产", "*********.40017": "量产", "*********.42404": "量产", "*********.43301": "量产", "*********.13895": "量产", "*********.42242": "量产", "*********.39254": "量产", "*********.34983": "即将停售", "*********.34960": "即将停售", "*********.38482": "量产", "*********.10286": "量产", "*********.10010": "量产", "*********.10248": "量产", "*********.12409": "量产", "*********.10283": "即将停售", "*********.10271": "开发", "*********.10155": "量产", "*********.12400": "量产", "*********.11419": "量产", "*********.11502": "量产", "*********.11351": "量产", "*********.10005": "量产", "*********.12470": "量产", "*********.11670": "量产", "*********.10018": "量产", "*********.10045-001": "开发", "*********.10044-001": "开发", "*********.10197": "量产", "*********.10148": "开发", "*********.10151": "开发", "*********.10152": "开发", "*********.10146": "开发", "*********.10052": "量产", "*********.10153": "开发", "*********.14284": "量产", "*********.10108": "量产", "*********.10295-002": "开发", "*********.10098": "量产", "*********.14287": "量产", "*********.14173": "量产", "*********.10323": "量产", "*********.16166": "量产", "*********.10139": "量产", "*********.46580": "工程样机", "*********.10262": "开发", "*********.10261": "量产", "*********.10195": "量产", "*********.15971": "量产", "*********.11365": "即将停售", "*********.10080": "量产", "*********.10604": "量产", "*********.13550": "即将停售", "*********.11503": "量产", "*********.13551": "即将停售", "*********.11495": "量产", "*********.10267": "开发", "*********.11665": "量产", "*********.11638": "量产", "*********.10275": "量产", "*********.11494": "量产", "*********.10159": "量产", "*********.10172": "量产", "*********.10121": "即将停售", "*********.10060": "量产", "*********.40903": "量产", "*********.13396": "量产", "*********.40968": "即将停售", "*********.42200": "量产", "*********.11204": "量产", "*********.40138": "量产", "*********.11043": "量产", "*********.10319": "量产", "*********.11051": "量产", "*********.11045": "量产", "*********.10252": "量产", "*********.11057": "量产", "*********.10063": "量产", "*********.10039": "量产", "*********.10062": "量产", "*********.10064": "量产", "*********.10137": "量产", "*********.10066": "量产", "*********.0071": "量产", "*********.10176": "量产", "*********.10074": "量产", "*********.10145": "量产", "*********.10081": "量产", "*********.12600": "量产", "*********.10435": "量产", "*********.10129": "含风险发布", "*********.10047": "开发", "*********.10848": "含风险发布", "*********.10847": "含风险发布", "*********.11677": "量产", "*********.11986": "量产", "*********.11250": "量产", "*********.11255": "量产", "*********.11257": "量产", "*********.11739": "量产", "*********.11256": "量产", "*********.11261": "量产", "*********.11265": "量产", "*********.11270": "原型机", "*********.11264": "量产", "*********.11266": "量产", "*********.11263": "原型机", "*********.11258": "量产", "*********.11274": "量产", "*********.11479": "量产", "*********.10013": "量产", "*********.10012": "量产", "*********.10011": "量产", "*********.12071": "量产", "*********.10514": "量产", "*********.10515": "量产", "*********.10513": "量产", "*********.11673": "量产", "*********.11674": "量产", "*********.11672": "量产", "*********.37518": "量产", "*********.39768": "量产", "*********.14444": "量产", "*********.15520": "量产", "*********.44340": "量产", "*********.10923": "量产", "*********.12377": "量产", "*********.10283": "量产", "*********.12910-001": "量产", "*********.12112": "量产", "*********.17792-000": "量产", "*********.10033": "量产", "*********.45226": "量产", "*********.45220": "量产", "*********.10048": "量产", "*********.10143-001": "量产", "*********.10111": "量产", "*********.10123": "量产", "*********.10553": "量产", "*********.10554": "量产", "*********.15970": "量产", "*********.11657": "量产", "*********.10203": "量产", "*********.10182": "量产", "*********.10518": "量产", "*********.13093": "量产", "*********.12127": "量产", "*********.14883": "量产", "*********.14208": "工程样机", "*********.10273": "工程样机", "*********.10144": "量产", "*********.14209": "量产", "*********.13795": "即将停售", "*********.12918": "量产", "*********.10125": "量产", "*********.10145": "开发", "*********.10184": "即将停售", "*********.10010": "量产", "*********.10106": "量产", "*********.10251": "量产", "*********.12822": "量产", "*********.37379": "量产", "*********.10007": "量产", "*********.10053-000": "量产", "*********.39766": "量产", "*********.14450": "量产", "*********.14533": "工程样机", "*********.14532": "工程样机", "*********.10021": "量产", "*********.10024": "量产", "*********.10023": "量产", "*********.10022": "量产", "*********.14824": "量产", "*********.41412": "即将停售", "*********.39228": "量产", "*********.36591": "量产", "*********.38310": "量产", "*********.13477": "即将停售", "*********.13996": "即将停售", "*********.10104": "原型机", "*********.12436": "量产", "*********.10365": "量产", "*********.10869": "量产", "*********.10277": "量产", "*********.12292": "量产", "*********.12288": "量产", "*********.12676": "量产", "*********.10161": "量产", "*********.10144": "量产", "*********.10142": "量产", "*********.10196": "量产", "*********.10130": "量产", "*********.10084": "量产", "*********.10164": "量产", "*********.10080": "量产", "*********.10104": "量产", "*********.10210": "量产", "*********.10087": "量产", "*********.10085": "量产", "*********.10086": "量产", "*********.10219": "量产", "*********.10211": "量产", "*********.10101": "量产", "*********.10302": "量产", "*********.10088": "量产", "*********.10209": "量产", "*********.10103": "量产", "*********.10197": "量产", "*********.10027": "量产", "*********.10026": "量产", "*********.10030": "量产", "*********.10025": "量产", "*********.12784": "量产", "*********.10057": "量产", "*********.10426": "量产", "*********.15968": "量产", "*********.10224": "量产", "*********.10123": "量产", "*********.13791": "含风险发布", "*********.10278": "量产", "*********.10006": "量产", "*********.14614": "量产", "*********.10587": "开发", "*********.10588": "开发", "*********.10602": "开发", "*********.10603": "开发", "*********.11991": "量产", "*********.11988": "量产", "*********.11984": "量产", "*********.11985": "量产", "*********.10545": "量产", "*********.10201": "量产", "*********.11869": "量产", "*********.13789": "量产", "*********.10284": "开发", "*********.13789-0003": "量产", "*********.10657": "即将停售", "*********.10331": "开发", "*********.37628": "量产", "*********.14723": "量产", "*********.13338": "量产", "*********.10281": "量产", "*********.40116": "量产", "*********.38569": "量产", "*********.10158": "量产", "*********.12403": "量产", "*********.10573": "量产", "*********.10549": "量产", "*********.10572": "量产", "*********.10689": "量产", "*********.10052": "量产", "*********.10060": "量产", "*********.10031": "量产", "*********.10049": "量产", "*********.10061": "量产", "*********.10490": "量产", "*********.10693": "量产", "*********.10690": "量产", "*********.10055": "量产", "*********.10059": "量产", "*********.10694": "量产", "*********.10062": "量产", "*********.10047": "量产", "*********.10688": "量产", "*********.10030": "量产", "*********.10695": "量产", "*********.10691": "量产", "*********.10692": "量产", "*********.10046": "开发", "*********.10066": "量产", "*********.10064": "开发", "*********.10065": "量产", "*********.10069": "量产", "*********.10068": "量产", "*********.10067": "量产", "*********.10701": "量产", "*********.10700": "量产", "*********.10698": "开发", "*********.10029": "量产", "*********.10699": "量产", "*********.10063": "开发", "*********.10044": "量产", "*********.10227": "量产", "*********.10228": "量产", "*********.10046": "量产", "*********.11373": "量产", "*********.10235": "量产", "*********.10018": "量产", "*********.10236": "量产", "*********.10280": "量产", "*********.10488": "开发", "*********.10030": "开发", "*********.10489": "量产", "*********.12368": "量产", "*********.12312": "量产", "*********.12317": "量产", "*********.16682": "量产", "*********.10727": "量产", "*********.10726": "量产", "*********.10724": "量产", "*********.10820": "开发", "*********.10725": "量产", "*********.10728": "量产", "*********.14872": "量产", "*********.11073": "量产", "*********.10063": "开发", "*********.11646": "量产", "*********.10595": "开发", "*********.12637": "量产", "*********.10597": "开发", "*********.10598": "开发", "*********.10596": "开发", "*********.17604-000": "量产", "*********.13355-000": "量产", "*********.12310": "量产", "*********.10521": "量产", "*********.14548": "量产", "*********.14546": "量产", "*********.10601": "开发", "*********.10600": "开发", "*********.10599": "开发", "*********.10552": "量产", "*********.10590": "开发", "*********.10592": "开发", "*********.10594": "开发", "*********.10591": "开发", "*********.10589": "开发", "*********.10109": "即将停售", "*********.10593": "开发", "*********.10038": "量产", "*********.10575": "量产", "*********.10158": "量产", "*********.10124": "量产", "*********.14872": "含风险发布", "*********.16069": "量产", "*********.10157": "量产", "*********.15582": "量产", "*********.11335": "量产", "*********.11328": "即将停售", "*********.12858": "量产", "*********.13543": "即将停售", "*********.11535": "即将停售", "*********.35322": "即将停售", "*********.11036": "量产", "*********.11935": "量产", "*********.10035": "量产", "*********.10017": "量产", "*********.11115": "量产", "*********.12181": "量产", "*********.11286": "量产", "*********.13138": "量产", "*********.10532": "量产", "*********.10411": "开发", "*********.13137": "量产", "*********.10410": "开发", "*********.10330": "开发", "*********.12946": "量产", "*********.10689": "量产", "*********.10044": "即将停售", "*********.15198": "量产", "*********.15199": "量产", "*********.10899": "开发", "*********.10895": "开发", "*********.10064": "量产", "*********.10897": "开发", "*********.10896": "开发", "*********.10894": "开发", "*********.10517": "量产", "*********.10528": "量产", "*********.10285": "量产", "*********.11064": "量产", "*********.10488": "量产", "*********.15134": "量产", "*********.10854": "量产", "*********.10709": "量产", "*********.26108": "量产", "*********.10018": "开发", "*********.16307": "量产", "*********.11102-D001": "量产", "*********.15207": "量产", "*********.11101-D001": "量产", "*********.15125": "量产", "*********.15973": "量产", "*********.14768": "量产", "*********.15115": "量产", "*********.16106": "含风险发布", "*********.15149": "含风险发布", "*********.15893": "量产", "*********.10204": "量产", "*********.16487": "量产", "*********.14746": "量产", "*********.13891": "工程样机", "*********.0934": "量产", "*********.37892": "量产", "*********.42243": "量产", "*********.40121": "量产", "*********.40310": "量产", "*********.42383": "量产", "*********.40123": "量产", "*********.10240-001": "开发", "*********.40616": "量产", "*********.15710": "量产", "*********.42641": "量产", "*********.11618": "量产", "*********.11620": "量产", "*********.11535": "量产", "*********.11621": "量产", "*********.11383": "量产", "*********.10340": "量产", "*********.11605": "量产", "*********.10726": "量产", "*********.14503": "量产", "*********.15441": "量产", "*********.10034": "量产", "*********.10733": "量产", "*********.14645": "即将停售", "*********.14210": "量产", "*********.14725": "即将停售", "*********.10025": "量产", "*********.10016": "量产", "*********.10026": "量产", "*********.10171": "量产", "*********.10361": "量产", "*********.10005": "开发", "*********.10040": "开发", "*********.10036": "开发", "*********.10012": "开发", "*********.10038": "开发", "*********.11859": "量产", "*********.10158": "量产", "*********.11727": "量产", "*********.12406": "量产", "*********.12404": "量产", "*********.12307": "量产", "*********.11708": "量产", "*********.12785": "量产", "*********.12787": "量产", "*********.10769": "开发", "*********.10770": "开发", "*********.11352": "量产", "*********.14979": "量产", "*********.10120": "即将停售", "*********.15177": "废弃", "*********.10227": "量产", "*********.13022": "量产", "*********.10915": "量产", "*********.10793": "量产", "*********.10593": "量产", "*********.10543": "量产", "*********.10136": "含风险发布", "*********.46579": "工程样机", "*********.12638": "量产", "*********.12581": "量产", "*********.15076": "量产", "*********.10283": "开发", "*********.10339": "开发", "*********.13040": "量产", "*********.10039": "量产", "*********.10397": "量产", "*********.10396": "量产", "*********.15025": "量产", "*********.11852": "即将停售", "*********.10546": "量产", "*********.45575": "量产", "*********.36322": "量产", "*********.40608": "量产", "*********.10373": "量产", "*********.40609": "量产", "*********.10057": "即将停售", "*********.10520": "量产", "*********.10527": "开发", "*********.14920": "量产", "*********.12582": "量产", "*********.11381": "量产", "*********.11382": "量产", "*********.38158": "量产", "*********.15165": "量产", "*********.10414": "量产", "*********.10412": "量产", "*********.10213": "量产", "*********.10085": "量产", "*********.10084": "量产", "*********.11228": "量产", "*********.10102": "量产", "*********.11882": "量产", "*********.11597": "量产", "*********.10745": "即将停售", "*********.11053": "量产", "*********.10248": "量产", "*********.10821": "开发", "*********.10822": "废弃", "*********.10828": "废弃", "*********.10254": "量产", "*********.10270": "量产", "*********.10262": "量产", "*********.10259": "量产", "*********.10082": "即将停售", "*********.11879": "量产", "*********.11987": "量产", "*********.12463": "即将停售", "*********.10898": "开发", "*********.11087": "量产", "*********.10571": "量产", "*********.10004": "量产", "*********.10896": "原型机", "*********.10896": "开发", "*********.12945": "量产", "*********.16031": "量产", "*********.13043": "量产", "*********.10136": "量产", "*********.16072": "量产", "*********.16486": "量产", "*********.11872": "量产", "*********.15014": "即将停售", "*********.12267": "量产", "*********.12266": "量产", "*********.11873": "量产", "*********.16523": "量产", "*********.12182": "量产", "*********.12180": "量产", "*********.10039": "量产", "*********.15015": "即将停售", "*********.14639": "即将停售", "*********.12157": "量产", "*********.44804": "量产", "*********.44805": "量产", "*********.11150": "量产", "*********.44806": "量产", "*********.10916": "量产", "*********.10920": "量产", "*********.10917": "量产", "*********.10918": "量产", "*********.11836": "量产", "*********.10607": "量产", "*********.11660": "量产", "*********.40107": "量产", "*********.40106": "量产", "*********.38558": "量产", "*********.38559": "量产", "*********.37519": "量产", "*********.37517": "量产", "*********.10474": "量产", "*********.15969": "量产", "*********.10062": "量产", "*********.13097": "量产", "*********.17791-000": "量产", "*********.10779": "量产", "*********.12150": "量产", "*********.12151": "量产", "*********.10830": "量产", "*********.10831": "量产", "*********.10829": "量产", "*********.10828": "量产", "*********.10250": "量产"};
        let replacementRecords = JSON.parse(localStorage.getItem('replacementRecords') || '[]');
        let currentConfig = 'high';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            renderTabs();
            updateStatistics();
        });

        function setupEventListeners() {
            document.getElementById('configType').addEventListener('change', function() {
                currentConfig = this.value;
                renderTabs();
                updateStatistics();
            });

            document.getElementById('lifecycleFilter').addEventListener('change', filterProducts);
            document.getElementById('searchInput').addEventListener('input', function() {
                searchProducts(this.value);
            });
        }

        function renderTabs() {
            const tabNav = document.getElementById('tabNavigation');
            const tabContent = document.getElementById('tabContent');
            
            const level2Items = hierarchyData["2"] || [];
            
            tabNav.innerHTML = '';
            tabContent.innerHTML = '';
            
            level2Items.forEach((item, index) => {
                const tabButton = document.createElement('button');
                tabButton.className = `py-4 px-4 border-b-2 font-medium text-sm whitespace-nowrap ${index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`;
                tabButton.textContent = item.description;
                tabButton.onclick = () => switchTab(item.code, tabButton);
                tabNav.appendChild(tabButton);
                
                const tabPane = document.createElement('div');
                tabPane.id = `tab-${item.code}`;
                tabPane.className = `tab-content ${index === 0 ? 'active' : ''}`;
                tabPane.innerHTML = generateTabContent(item.code);
                tabContent.appendChild(tabPane);
            });
        }

        function generateTabContent(parentCode) {
            const products = (hierarchyData["4"] || []).filter(item => item.parent === parentCode);
            
            if (products.length === 0) {
                return '<div class="text-center text-gray-500 py-8">暂无产品数据</div>';
            }
            
            let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';
            
            products.forEach(product => {
                const lifecycle = getProductLifecycle(product.code);
                const lifecycleClass = `lifecycle-${lifecycle}`;
                
                html += `
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow product-card" data-lifecycle="${lifecycle}">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-medium text-gray-800 text-sm">${product.description}</h4>
                            <span class="lifecycle-badge ${lifecycleClass}">${lifecycle}</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-3">${product.code}</p>
                        <div class="flex justify-between items-center">
                            <button onclick="viewProductDetails('${product.code}')" 
                                    class="text-blue-600 hover:text-blue-800 text-xs">
                                <i class="fas fa-eye mr-1"></i>详情
                            </button>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        function switchTab(code, button) {
            document.querySelectorAll('#tabNavigation button').forEach(btn => {
                btn.className = btn.className.replace('border-blue-500 text-blue-600', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
            });
            button.className = button.className.replace('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'border-blue-500 text-blue-600');
            
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`tab-${code}`).classList.add('active');
        }

        function getProductLifecycle(productCode) {
            return productLifecycleData[productCode] || '量产';
        }

        function updateStatistics() {
            const allProducts = hierarchyData["4"] || [];
            const totalCount = allProducts.length;
            const activeCount = allProducts.filter(p => getProductLifecycle(p.code) === '量产').length;
            const sampleCount = allProducts.filter(p => getProductLifecycle(p.code) === '工程样机').length;
            const retiredCount = allProducts.filter(p => ['停产', '退市'].includes(getProductLifecycle(p.code))).length;
            
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('activeCount').textContent = activeCount;
            document.getElementById('sampleCount').textContent = sampleCount;
            document.getElementById('retiredCount').textContent = retiredCount;
            document.getElementById('replacedCount').textContent = 0;
        }

        function filterProducts() {
            const lifecycle = document.getElementById('lifecycleFilter').value;
            const productCards = document.querySelectorAll('.product-card');
            
            productCards.forEach(card => {
                if (!lifecycle || card.dataset.lifecycle === lifecycle) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function searchProducts(query) {
            const productCards = document.querySelectorAll('.product-card');
            
            productCards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (!query || text.includes(query.toLowerCase())) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function viewProductDetails(productCode) {
            const product = (hierarchyData["4"] || []).find(p => p.code === productCode);
            const lifecycle = getProductLifecycle(productCode);
            
            alert(`产品详情：\n编号：${productCode}\n描述：${product ? product.description : '未知'}\n生命周期：${lifecycle}`);
        }
    </script>
</body>
</html>